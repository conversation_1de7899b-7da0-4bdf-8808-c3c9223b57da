name: Unity Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test Unity Project
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        unity-version:
          - 2023.3.0f1
        test-mode:
          - EditMode
          - PlayMode

    steps:
      # 检出代码
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          lfs: true

      # 缓存Unity Library
      - name: Cache Unity Library
        uses: actions/cache@v3
        with:
          path: Library
          key: Library-${{ matrix.unity-version }}-${{ hashFiles('Assets/**', 'Packages/**', 'ProjectSettings/**') }}
          restore-keys: |
            Library-${{ matrix.unity-version }}-
            Library-

      # 运行Unity测试
      - name: Run Unity Tests
        uses: game-ci/unity-test-runner@v4
        id: unity-tests
        env:
          UNITY_LICENSE: ${{ secrets.UNITY_LICENSE }}
          UNITY_EMAIL: ${{ secrets.UNITY_EMAIL }}
          UNITY_PASSWORD: ${{ secrets.UNITY_PASSWORD }}
        with:
          unity-version: ${{ matrix.unity-version }}
          test-mode: ${{ matrix.test-mode }}
          artifacts-path: test-results
          github-token: ${{ secrets.GITHUB_TOKEN }}
          check-name: Unity Test Results (${{ matrix.test-mode }})

      # 上传测试结果
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: Test results (${{ matrix.test-mode }})
          path: test-results

      # 解析测试结果
      - name: Parse test results
        if: always()
        run: |
          if [ -f "test-results/results.xml" ]; then
            echo "=== Unity Test Results ==="
            cat test-results/results.xml

            # 提取测试统计
            TOTAL_TESTS=$(grep -o 'total="[0-9]*"' test-results/results.xml | grep -o '[0-9]*' || echo "0")
            PASSED_TESTS=$(grep -o 'passed="[0-9]*"' test-results/results.xml | grep -o '[0-9]*' || echo "0")
            FAILED_TESTS=$(grep -o 'failed="[0-9]*"' test-results/results.xml | grep -o '[0-9]*' || echo "0")

            echo "Total Tests: $TOTAL_TESTS"
            echo "Passed Tests: $PASSED_TESTS"
            echo "Failed Tests: $FAILED_TESTS"

            # 设置输出变量
            echo "total-tests=$TOTAL_TESTS" >> $GITHUB_OUTPUT
            echo "passed-tests=$PASSED_TESTS" >> $GITHUB_OUTPUT
            echo "failed-tests=$FAILED_TESTS" >> $GITHUB_OUTPUT
          else
            echo "No test results found"
          fi

  # 代码覆盖率分析
  coverage:
    name: Code Coverage Analysis
    runs-on: ubuntu-latest
    needs: test
    if: always()

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download test results
        uses: actions/download-artifact@v3
        with:
          name: Test results (EditMode)
          path: test-results

      # 代码覆盖率分析 - 适配新的测试结构
      - name: Generate coverage report
        run: |
          echo "=== Code Coverage Analysis ==="
          echo "Coverage analysis for unified test structure"
          echo "Test structure: Assets/Tests/"
          echo "- EditMode: Unit, Integration, Utilities"
          echo "- PlayMode: Functional, Performance, EndToEnd"
          echo "- Shared: Mocks, Fixtures, Helpers, Data"
          echo "Recommended tools: OpenCover, Coverlet, or Unity Code Coverage package"

  # 性能测试
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run performance tests
        run: |
          echo "=== Performance Test Analysis ==="
          echo "Performance tests for unified structure:"
          echo "- Memory usage analysis"
          echo "- Network latency simulation"
          echo "- Frame rate benchmarks"
          echo "- Load testing with multiple clients"
          echo "Performance tests located in: Assets/Tests/PlayMode/Performance/"

  # 通知结果
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, coverage, performance]
    if: always()

    steps:
      - name: Notify success
        if: needs.test.result == 'success'
        run: |
          echo "✅ All tests passed successfully!"
          echo "Test suite completed without errors."

      - name: Notify failure
        if: needs.test.result == 'failure'
        run: |
          echo "❌ Some tests failed!"
          echo "Please check the test results and fix any issues."
          exit 1

      # 可以添加Slack、Discord或邮件通知
      # - name: Slack Notification
      #   if: always()
      #   uses: 8398a7/action-slack@v3
      #   with:
      #     status: ${{ job.status }}
      #     webhook_url: ${{ secrets.SLACK_WEBHOOK }}
