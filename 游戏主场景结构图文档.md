# Goose Duck Kill 3 游戏主场景结构图文档

## 📋 概述

本文档详细描述了 **Goose Duck Kill 3** 游戏的主场景结构设计，包括场景层次结构、游戏对象组织、UI布局和网络同步架构。项目采用模块化场景设计，支持多种游戏状态和跨平台部署。

## 🎮 场景架构总览

### 场景分类
```
Assets/Scenes/
├── MainMenu/           # 主菜单场景
├── Lobby/             # 游戏大厅场景
├── Maps/              # 游戏地图场景集合
│   ├── Skeld/         # 经典地图
│   ├── Mira/          # 总部地图
│   └── Polus/         # 星球地图
└── SampleScene.unity  # 开发测试场景
```

### 游戏状态与场景映射
| 游戏状态 | 对应场景 | 主要功能 |
|---------|---------|----------|
| `MainMenu` | MainMenu | 启动界面、设置、连接服务器 |
| `Lobby` | Lobby | 房间管理、玩家准备、游戏配置 |
| `Starting` | Maps/* | 角色分配、倒计时、游戏准备 |
| `Playing` | Maps/* | 主要游戏循环、任务执行、互动 |
| `Meeting` | Maps/* | 会议讨论、投票系统 |
| `Ending` | Maps/* | 结果展示、统计信息 |

## 🏗️ 主场景结构设计

### 📋 结构图标识说明

在以下场景结构图中，我们使用不同的标识来区分不同类型的元素：

- **[GameObject]** - Unity场景中的游戏对象
- **[Unity Component]** - Unity内置组件 (如Camera、Light、Image等)
- **[Script Component]** - 自定义脚本组件 (如MobileUIRoot、SimpleLightController等)
- **[功能]** - 组件提供的功能特性
- **[GameObject Group]** - 多个相关GameObject的组合

### 1. MainMenu 场景结构 (详细标识版)

```
MainMenu Scene [Unity Scene]
├── 📷 Main Camera [GameObject]
│   ├── Camera [Unity Component] (默认)
│   ├── Audio Listener [Unity Component] (默认)
│   ├── 🎮 SimpleCameraController [Script Component] (推荐)
│   │   ├── 浮动效果控制 [功能]
│   │   ├── 呼吸效果控制 [功能]
│   │   └── 基础动态效果 [功能]
│   └── 🎬 MainMenuCameraController [Script Component] (高级)
│       ├── 浮动效果 + 缩放动画 [功能]
│       ├── 旋转效果 + 过渡动画 [功能]
│       ├── 响应式屏幕适配 [功能]
│       └── 完整动态效果套件 [功能]
├── 💡 Directional Light [GameObject]
│   ├── Light [Unity Component] (默认)
│   ├── 🌟 SimpleLightController [Script Component] (推荐)
│   │   ├── 自动平台优化 [功能]
│   │   ├── 基础光照参数配置 [功能]
│   │   └── 阴影自适应控制 [功能]
│   └── 🌈 MainMenuLightController [Script Component] (高级)
│       ├── 动态光照效果 [功能]
│       ├── 强度脉冲动画 [功能]
│       ├── 颜色变换效果 [功能]
│       ├── 时间模拟系统 [功能]
│       └── 平台性能优化 [功能]
├── 🎨 UI Canvas [GameObject] (Screen Space - Overlay)
│   ├── Canvas [Unity Component]
│   ├── Canvas Scaler [Unity Component]
│   ├── Graphic Raycaster [Unity Component]
│   ├── 📱 Mobile UI Root [GameObject] (移动端适配)
│   │   ├── RectTransform [Unity Component]
│   │   ├── 🎮 MobileUIRoot [Script Component] (核心控制器)
│   │   │   ├── 自适应布局管理 [功能]
│   │   │   ├── 屏幕方向适配 [功能]
│   │   │   ├── 动态缩放控制 [功能]
│   │   │   └── 性能优化监控 [功能]
│   │   ├── 🛡️ Safe Area Root [GameObject]
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── SafeAreaHandler [Script Component] (安全区域处理)
│   │   │   ├── 刘海屏适配 [功能]
│   │   │   ├── 圆角屏幕适配 [功能]
│   │   │   ├── Home指示器适配 [功能]
│   │   │   └── 调试可视化 [功能]
│   │   ├── 🎮 Mobile Controls Root [GameObject]
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── MobileControlsManager [Script Component] (控制管理器)
│   │   │   ├── Virtual Joystick [GameObject] (虚拟摇杆)
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   ├── Background [GameObject] (背景圆环)
│   │   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   │   └── Image [Unity Component]
│   │   │   │   ├── Handle [GameObject] (操作手柄)
│   │   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   │   └── Image [Unity Component]
│   │   │   │   ├── SimpleVirtualJoystick [Script Component] (触摸检测)
│   │   │   │   └── 输入反馈 [功能]
│   │   │   ├── Action Buttons [GameObject Group] (动作按钮组)
│   │   │   │   ├── Settings Button [GameObject]
│   │   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   │   ├── Image [Unity Component]
│   │   │   │   │   ├── Button [Unity Component]
│   │   │   │   │   └── MobileActionButton [Script Component]
│   │   │   │   ├── Menu Button [GameObject]
│   │   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   │   ├── Image [Unity Component]
│   │   │   │   │   ├── Button [Unity Component]
│   │   │   │   │   └── MobileActionButton [Script Component]
│   │   │   │   └── Back Button [GameObject]
│   │   │   │       ├── RectTransform [Unity Component]
│   │   │   │       ├── Image [Unity Component]
│   │   │   │       ├── Button [Unity Component]
│   │   │   │       └── MobileActionButton [Script Component]
│   │   │   ├── GestureRecognizer [Script Component] (手势识别)
│   │   │   │   ├── 滑动手势检测 [功能]
│   │   │   │   ├── 点击手势检测 [功能]
│   │   │   │   ├── 长按手势检测 [功能]
│   │   │   │   └── 多点触控支持 [功能]
│   │   │   └── HapticFeedbackController [Script Component] (触觉反馈)
│   │   │       ├── 轻度震动 [功能]
│   │   │       ├── 中度震动 [功能]
│   │   │       ├── 重度震动 [功能]
│   │   │       └── 自定义震动模式 [功能]
│   │   └── 📊 Mobile HUD Root [GameObject]
│   │       ├── RectTransform [Unity Component]
│   │       ├── MobileHUDManager [Script Component] (HUD管理器)
│   │       ├── Status Indicators [GameObject] (状态指示器)
│   │       │   ├── RectTransform [Unity Component]
│   │       │   ├── Network Indicator [GameObject]
│   │       │   │   ├── RectTransform [Unity Component]
│   │       │   │   ├── Image [Unity Component]
│   │       │   │   └── NetworkIndicator [Script Component]
│   │       │   ├── FPS Counter [GameObject]
│   │       │   │   ├── RectTransform [Unity Component]
│   │       │   │   ├── Text [Unity Component]
│   │       │   │   └── FPSCounter [Script Component]
│   │       │   ├── Battery Indicator [GameObject]
│   │       │   │   ├── RectTransform [Unity Component]
│   │       │   │   ├── Text [Unity Component]
│   │       │   │   └── BatteryIndicator [Script Component]
│   │       │   └── Performance Monitor [GameObject]
│   │       │       ├── RectTransform [Unity Component]
│   │       │       ├── Text [Unity Component]
│   │       │       └── PerformanceMonitor [Script Component]
│   │       ├── Notification System [GameObject] (通知系统)
│   │       │   ├── RectTransform [Unity Component]
│   │       │   ├── NotificationManager [Script Component]
│   │       │   ├── Toast消息 [功能]
│   │       │   ├── 弹窗提示 [功能]
│   │       │   └── 系统通知 [功能]
│   │       └── Debug Panel Reference [GameObject] (调试面板引用)
│   │           ├── RectTransform [Unity Component]
│   │           ├── DebugPanelReference [Script Component] (引用场景根级Debug Console)
│   │           └── 移动端UI适配 [功能]
│   ├── 🖥️ Desktop UI Root [GameObject]
│   │   ├── RectTransform [Unity Component]
│   │   ├── Main Panel [GameObject]
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Image [Unity Component] (背景)
│   │   │   ├── Title Logo [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Image [Unity Component]
│   │   │   ├── Play Button [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   ├── Image [Unity Component]
│   │   │   │   ├── Button [Unity Component]
│   │   │   │   └── Text [Unity Component] (子对象)
│   │   │   ├── Settings Button [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   ├── Image [Unity Component]
│   │   │   │   ├── Button [Unity Component]
│   │   │   │   └── Text [Unity Component] (子对象)
│   │   │   └── Quit Button [GameObject]
│   │   │       ├── RectTransform [Unity Component]
│   │   │       ├── Image [Unity Component]
│   │   │       ├── Button [Unity Component]
│   │   │       └── Text [Unity Component] (子对象)
│   │   ├── Settings Panel [GameObject]
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Image [Unity Component] (背景)
│   │   │   ├── Audio Settings [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Slider [Unity Component] (音量)
│   │   │   ├── Graphics Settings [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Dropdown [Unity Component] (质量)
│   │   │   └── Controls Settings [GameObject]
│   │   │       ├── RectTransform [Unity Component]
│   │   │       └── Toggle [Unity Component] (设置项)
│   │   └── Connection Panel [GameObject]
│   │       ├── RectTransform [Unity Component]
│   │       ├── Image [Unity Component] (背景)
│   │       ├── Server Input [GameObject]
│   │       │   ├── RectTransform [Unity Component]
│   │       │   └── InputField [Unity Component]
│   │       ├── Player Name Input [GameObject]
│   │       │   ├── RectTransform [Unity Component]
│   │       │   └── InputField [Unity Component]
│   │       └── Connect Button [GameObject]
│   │           ├── RectTransform [Unity Component]
│   │           ├── Image [Unity Component]
│   │           ├── Button [Unity Component]
│   │           └── Text [Unity Component] (子对象)
│   └── 🔧 UI Manager [GameObject]
│       ├── ConnectionUI [Script Component]
│       ├── SettingsManager [Script Component]
│       └── MobileUIAdapter [Script Component]
├── 🌐 Network Manager [GameObject]
│   ├── NetworkRunner [Script Component]
│   ├── NetworkManager [Script Component]
│   └── AuthManager [Script Component]
├── 🎵 Audio Manager [GameObject]
│   ├── Background Music [GameObject]
│   │   └── Audio Source [Unity Component]
│   ├── UI Sound Effects [GameObject]
│   │   └── Audio Source [Unity Component]
│   ├── AudioManager [Script Component]
│   └── Audio Mixer Groups [功能]
├── 📱 Mobile Performance Optimizer [GameObject]
│   └── PerformanceOptimizer [Script Component]
├── 🔍 Debug Console [GameObject] (统一调试系统)
│   ├── DebugConsole [Script Component] (核心调试控制器)
│   │   ├── 平台自适应UI [功能]
│   │   ├── 命令行接口 [功能]
│   │   ├── 日志管理系统 [功能]
│   │   └── 开发工具集成 [功能]
│   ├── 📱 Mobile Debug UI [GameObject] (移动端调试界面)
│   │   ├── RectTransform [Unity Component]
│   │   ├── Canvas Group [Unity Component] (透明度控制)
│   │   ├── MobileDebugUI [Script Component]
│   │   ├── Touch Visualizer [GameObject] (触摸点可视化)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Image [Unity Component]
│   │   │   └── TouchVisualizer [Script Component]
│   │   ├── Safe Area Indicator [GameObject] (安全区域显示)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Image [Unity Component] (边框显示)
│   │   │   └── SafeAreaIndicator [Script Component]
│   │   ├── Performance HUD [GameObject] (性能数据显示)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Text [Unity Component] (FPS显示)
│   │   │   ├── Text [Unity Component] (内存显示)
│   │   │   └── PerformanceHUD [Script Component]
│   │   └── Debug Log Panel [GameObject] (日志输出面板)
│   │       ├── RectTransform [Unity Component]
│   │       ├── Image [Unity Component] (背景)
│   │       ├── Scroll View [GameObject]
│   │       │   ├── ScrollRect [Unity Component]
│   │       │   └── Debug Text [GameObject]
│   │       │       ├── RectTransform [Unity Component]
│   │       │       └── Text [Unity Component]
│   │       └── DebugLogPanel [Script Component]
│   ├── 🖥️ Desktop Debug UI [GameObject] (桌面端调试界面)
│   │   ├── RectTransform [Unity Component]
│   │   ├── DesktopDebugUI [Script Component]
│   │   ├── Console Window [GameObject] (控制台窗口)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Image [Unity Component] (窗口背景)
│   │   │   ├── Input Field [GameObject] (命令输入)
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── InputField [Unity Component]
│   │   │   └── Output Text [GameObject] (输出显示)
│   │   │       ├── RectTransform [Unity Component]
│   │   │       └── Text [Unity Component]
│   │   ├── Debug Info Panel [GameObject] (调试信息面板)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Image [Unity Component] (面板背景)
│   │   │   ├── System Info [GameObject] (系统信息)
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Text [Unity Component]
│   │   │   ├── Network Info [GameObject] (网络信息)
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Text [Unity Component]
│   │   │   └── Performance Metrics [GameObject] (性能指标)
│   │   │       ├── RectTransform [Unity Component]
│   │   │       └── Text [Unity Component]
│   │   └── Cheat Panel [GameObject] (作弊面板 - 开发专用)
│   │       ├── RectTransform [Unity Component]
│   │       ├── Image [Unity Component] (面板背景)
│   │       ├── CheatManager [Script Component]
│   │       └── Cheat Buttons [GameObject Group] (作弊按钮组)
│   │           ├── God Mode Button [GameObject]
│   │           ├── Teleport Button [GameObject]
│   │           ├── Speed Hack Button [GameObject]
│   │           └── Resource Hack Button [GameObject]
│   └── 🔧 Debug System Manager [GameObject] (调试系统管理器)
│       ├── DebugSystemManager [Script Component] (系统管理器)
│       │   ├── 平台检测与UI切换 [功能]
│       │   ├── 调试级别控制 [功能]
│       │   ├── 性能监控 [功能]
│       │   ├── 日志过滤与分类 [功能]
│       │   └── 开发工具集成 [功能]
│       ├── DebugCommandProcessor [Script Component] (命令处理器)
│       │   ├── 命令解析 [功能]
│       │   ├── 参数验证 [功能]
│       │   ├── 执行结果反馈 [功能]
│       │   └── 历史记录管理 [功能]
│       └── DebugDataCollector [Script Component] (数据收集器)
│           ├── 系统信息收集 [功能]
│           ├── 性能数据采集 [功能]
│           ├── 网络状态监控 [功能]
│           └── 用户行为追踪 [功能]
└── 🎮 EventSystem [GameObject]
    ├── EventSystem [Unity Component]
    └── Standalone Input Module [Unity Component]
```

### 2. Lobby 场景结构 (详细标识版)

```
Lobby Scene [Unity Scene]
├── 📷 Main Camera [GameObject]
│   ├── Camera [Unity Component]
│   ├── Audio Listener [Unity Component]
│   ├── 🎮 SimpleCameraController [Script Component] (基础)
│   │   ├── 轻微浮动效果 [功能]
│   │   └── 静态背景渲染 [功能]
│   └── 🎬 MainMenuCameraController [Script Component] (可选)
│       ├── 动态背景效果 [功能]
│       ├── 玩家进入/退出动画 [功能]
│       └── 房间状态响应动画 [功能]
├── 💡 Directional Light [GameObject]
│   ├── Light [Unity Component] (默认)
│   ├── 🌟 SimpleLightController [Script Component] (标准)
│   │   ├── 大厅环境光照优化 [功能]
│   │   ├── 暖色调光照配置 [功能]
│   │   └── 性能友好设置 [功能]
│   └── 🌐 LightingManager [Script Component] (全局)
│       ├── 场景光照自动切换 [功能]
│       ├── 环境光统一管理 [功能]
│       └── 雾效和天空盒控制 [功能]
├── 🎨 UI Canvas [GameObject] (Screen Space - Overlay)
│   ├── Canvas [Unity Component]
│   ├── Canvas Scaler [Unity Component]
│   ├── Graphic Raycaster [Unity Component]
│   ├── 📋 Room Info Panel [GameObject]
│   │   ├── RectTransform [Unity Component]
│   │   ├── Image [Unity Component] (背景)
│   │   ├── Room Name Display [GameObject]
│   │   │   ├── RectTransform [Unity Component]
│   │   │   └── Text [Unity Component]
│   │   ├── Player Count [GameObject] (X/10)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   └── Text [Unity Component]
│   │   ├── Game Settings Display [GameObject]
│   │   │   ├── RectTransform [Unity Component]
│   │   │   └── Text [Unity Component]
│   │   └── Room Code [GameObject]
│   │       ├── RectTransform [Unity Component]
│   │       └── Text [Unity Component]
│   ├── 👥 Player List Panel [GameObject]
│   │   ├── RectTransform [Unity Component]
│   │   ├── Image [Unity Component] (背景)
│   │   ├── Player Entry Prefab [Prefab] (动态生成)
│   │   │   ├── RectTransform [Unity Component]
│   │   │   ├── Player Avatar [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Image [Unity Component]
│   │   │   ├── Player Name [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Text [Unity Component]
│   │   │   ├── Ready Status [GameObject]
│   │   │   │   ├── RectTransform [Unity Component]
│   │   │   │   └── Image [Unity Component]
│   │   │   └── Host Crown [GameObject] (if host)
│   │   │       ├── RectTransform [Unity Component]
│   │   │       └── Image [Unity Component]
│   │   └── Scroll View [GameObject]
│   │       ├── RectTransform [Unity Component]
│   │       └── ScrollRect [Unity Component]
│   ├── ⚙️ Game Settings Panel (Host Only)
│   │   ├── Map Selection
│   │   ├── Player Limits
│   │   ├── Game Rules
│   │   └── Apply Button
│   ├── 🎮 Control Panel
│   │   ├── Ready/Unready Button
│   │   ├── Start Game Button (Host Only)
│   │   ├── Leave Room Button
│   │   └── Chat Input
│   └── 📱 Mobile UI Adaptations
│       ├── Virtual Keyboard Support
│       └── Touch-Friendly Buttons
├── 🌐 Network Components
│   ├── RoomManager Component
│   ├── PlayerSpawner Component
│   └── LobbyNetworkManager Component
├── 🎵 Ambient Audio
│   ├── Background Music (Audio Source)
│   └── UI Sound Effects (Audio Source)
├── 📊 Performance Monitor
└── 🎮 EventSystem
    ├── EventSystem Component
    └── Standalone Input Module Component
```

### 3. Game Map 场景结构 (以Skeld为例) (详细标识版)

```
Skeld Game Scene [Unity Scene]
├── 📷 Main Camera [GameObject]
│   ├── Camera [Unity Component]
│   ├── Audio Listener [Unity Component]
│   ├── 🎯 CameraFollow [Script Component] (核心跟随脚本)
│   │   ├── 玩家跟随控制 [功能]
│   │   ├── 平滑移动算法 [功能]
│   │   ├── 边界限制系统 [功能]
│   │   ├── 死区检测 [功能]
│   │   ├── 预测移动 [功能]
│   │   └── 震动效果支持 [功能]
│   └── 🌐 CameraManager [Script Component] (全局管理)
│       ├── 场景切换自动配置 [功能]
│       ├── 性能自适应调整 [功能]
│       └── 平台优化设置 [功能]
├── 💡 Directional Light [GameObject]
│   ├── Light [Unity Component] (默认)
│   ├── 🌟 SimpleLightController [Script Component] (游戏场景)
│   │   ├── 游戏环境光照 [功能]
│   │   ├── 冷色调配置 [功能]
│   │   ├── 阴影优化设置 [功能]
│   │   └── 雾效支持 [功能]
│   └── 🌐 LightingManager [Script Component] (全局管理)
│       ├── 动态光照质量调整 [功能]
│       ├── 平台特定优化 [功能]
│       └── 场景过渡效果 [功能]
├── 🗺️ Environment Root
│   ├── 🏢 Map Geometry
│   │   ├── Rooms
│   │   │   ├── Cafeteria
│   │   │   ├── Upper Engine
│   │   │   ├── Lower Engine
│   │   │   ├── Security
│   │   │   ├── Reactor
│   │   │   ├── Medbay
│   │   │   ├── Electrical
│   │   │   ├── Storage
│   │   │   ├── Admin
│   │   │   ├── Communications
│   │   │   ├── O2
│   │   │   ├── Weapons
│   │   │   ├── Shields
│   │   │   └── Navigation
│   │   ├── Corridors & Hallways
│   │   └── Vents System
│   ├── 🚪 Interactive Objects
│   │   ├── Doors (可关闭/破坏)
│   │   ├── Emergency Button
│   │   ├── Security Cameras
│   │   └── Admin Table
│   ├── 📋 Task Stations
│   │   ├── Task Prefab Instances
│   │   │   ├── Task Interaction Zone
│   │   │   ├── Task UI Trigger
│   │   │   ├── Visual Task Indicator
│   │   │   └── Network Sync Component
│   │   └── Task Manager
│   └── 💡 Lighting System
│       ├── Room Lights (Point Lights)
│       ├── Emergency Lighting (Spot Lights)
│       └── Sabotage Effects (Dynamic Lights)
├── 👥 Players Root
│   ├── 🎮 Player Spawn Points
│   │   ├── Spawn Point (Cafeteria)
│   │   └── Respawn Points (各房间)
│   └── 👤 Player Prefab Instances (动态生成)
│       ├── PlayerController Component
│       ├── PlayerMovement Component
│       ├── PlayerInteraction Component
│       ├── PlayerNetwork Component
│       ├── PlayerAnimation Component
│       ├── Collider & Rigidbody
│       └── Visual Components
│           ├── Character Sprite
│           ├── Name Tag
│           ├── Color Customization
│           └── Role Indicator (隐藏)
├── 🎨 Game UI Canvas
│   ├── 📱 Mobile UI Root
│   │   ├── Virtual Joystick
│   │   ├── Action Buttons
│   │   │   ├── Use/Interact Button
│   │   │   ├── Kill Button (Duck Only)
│   │   │   ├── Report Button
│   │   │   ├── Emergency Button
│   │   │   └── Vent Button (Duck Only)
│   │   └── Mobile HUD
│   ├── 🖥️ Desktop UI Root
│   │   ├── Game HUD
│   │   │   ├── Task List
│   │   │   ├── Mini Map
│   │   │   ├── Emergency Cooldown
│   │   │   ├── Kill Cooldown (Duck Only)
│   │   │   └── Player Status
│   │   ├── Task UI Panel
│   │   │   ├── Task Instructions
│   │   │   ├── Task Progress
│   │   │   ├── Task Interaction
│   │   │   └── Task Complete Animation
│   │   ├── Meeting UI Panel
│   │   │   ├── Discussion Timer
│   │   │   ├── Voting Timer
│   │   │   ├── Player Vote Buttons
│   │   │   ├── Chat System
│   │   │   └── Vote Results
│   │   ├── Game Over Panel
│   │   │   ├── Victory/Defeat Screen
│   │   │   ├── Player Statistics
│   │   │   ├── Match Summary
│   │   │   └── Return to Lobby Button
│   │   └── Settings Overlay
│   │       ├── Audio Controls
│   │       ├── Graphics Settings
│   │       └── Controls Mapping
│   └── 🔧 UI Controllers
│       ├── GameUIManager Component
│       ├── TaskUIController Component
│       ├── MeetingUIController Component
│       └── MobileUIController Component
├── 🌐 Network & Game Management
│   ├── 🎮 Core Managers
│   │   ├── GameManager (Singleton)
│   │   │   ├── Game State Management
│   │   │   ├── Round Management
│   │   │   ├── Timer Management
│   │   │   └── Event Coordination
│   │   ├── GameStateManager
│   │   │   ├── State Transitions
│   │   │   └── State-Specific Logic
│   │   ├── RoleManager
│   │   │   ├── Role Assignment
│   │   │   ├── Role Abilities
│   │   │   └── Role Validation
│   │   └── ScoreManager
│   │       ├── Victory Conditions
│   │       ├── Score Tracking
│   │       └── Statistics
│   ├── 🌐 Network Components
│   │   ├── NetworkRunner
│   │   ├── NetworkManager
│   │   ├── RPC Manager
│   │   └── Authority Manager
│   ├── 📋 Task System
│   │   ├── TaskManager
│   │   ├── Task Definitions
│   │   ├── Task Progress Tracking
│   │   └── Task Completion Events
│   └── 🔧 Sabotage System
│       ├── SabotageManager
│       ├── Sabotage Types
│       │   ├── Lights
│       │   ├── Oxygen
│       │   ├── Reactor
│       │   └── Communications
│       └── Repair Mechanics
├── 🎵 Audio System
│   ├── 🎶 Background Music
│   │   ├── Ambient Tracks (Audio Source)
│   │   ├── Tension Music (Audio Source)
│   │   └── Meeting Music (Audio Source)
│   ├── 🔊 Sound Effects
│   │   ├── Footsteps (Audio Source)
│   │   ├── Task Sounds (Audio Source)
│   │   ├── Kill Sounds (Audio Source)
│   │   ├── Emergency Alarm (Audio Source)
│   │   └── UI Sounds (Audio Source)
│   └── 🎚️ Audio Management
│       ├── Audio Manager
│       ├── 3D Audio Sources
│       └── Audio Mixer Groups
├── 📱 Mobile Optimizations
│   ├── Performance Optimizer
│   ├── Battery Optimization
│   ├── Network Optimization
│   └── UI Scaling Adapter
├── 🔍 Debug Console [GameObject] (统一调试系统)
│   ├── DebugConsole [Script Component] (核心调试控制器)
│   │   ├── 游戏状态监控 [功能]
│   │   ├── 网络调试接口 [功能]
│   │   ├── 性能分析工具 [功能]
│   │   └── 游戏内作弊命令 [功能]
│   ├── 📱 Mobile Debug UI [GameObject] (移动端调试界面)
│   │   ├── Game Performance HUD [GameObject] (游戏性能HUD)
│   │   │   ├── FPS Counter [Unity Component]
│   │   │   ├── Network Latency [Unity Component]
│   │   │   └── Memory Usage [Unity Component]
│   │   ├── Touch Debug Overlay [GameObject] (触摸调试覆盖层)
│   │   │   ├── Touch Points Visualizer [Script Component]
│   │   │   └── Gesture Recognition Debug [Script Component]
│   │   └── Game State Inspector [GameObject] (游戏状态检查器)
│   │       ├── Player State Display [Unity Component]
│   │       ├── Network State Display [Unity Component]
│   │       └── Task Progress Display [Unity Component]
│   ├── 🖥️ Desktop Debug UI [GameObject] (桌面端调试界面)
│   │   ├── Network Debug Panel [GameObject] (网络调试面板)
│   │   │   ├── Connection Status [Unity Component]
│   │   │   ├── Packet Monitor [Unity Component]
│   │   │   ├── RPC Call Tracker [Unity Component]
│   │   │   └── Sync Debug Info [Unity Component]
│   │   ├── Performance Profiler [GameObject] (性能分析器)
│   │   │   ├── CPU Usage Monitor [Unity Component]
│   │   │   ├── Memory Profiler [Unity Component]
│   │   │   ├── Render Statistics [Unity Component]
│   │   │   └── Network Performance [Unity Component]
│   │   ├── Game Debug Panel [GameObject] (游戏调试面板)
│   │   │   ├── Player Debug Info [Unity Component]
│   │   │   ├── Role System Debug [Unity Component]
│   │   │   ├── Task System Debug [Unity Component]
│   │   │   └── Voting System Debug [Unity Component]
│   │   └── Cheat Commands Panel [GameObject] (作弊命令面板 - 开发专用)
│   │       ├── Player Cheats [GameObject Group]
│   │       │   ├── Teleport Commands [Unity Component]
│   │       │   ├── Role Switch Commands [Unity Component]
│   │       │   └── Invincibility Toggle [Unity Component]
│   │       ├── Game Cheats [GameObject Group]
│   │       │   ├── Skip Tasks Commands [Unity Component]
│   │       │   ├── Force Meeting Commands [Unity Component]
│   │       │   └── End Game Commands [Unity Component]
│   │       └── Network Cheats [GameObject Group]
│   │           ├── Simulate Lag Commands [Unity Component]
│   │           ├── Force Disconnect Commands [Unity Component]
│   │           └── Packet Loss Simulation [Unity Component]
│   └── 🔧 Debug System Manager [GameObject] (调试系统管理器)
│       ├── GameDebugManager [Script Component] (游戏调试管理器)
│       │   ├── 游戏状态实时监控 [功能]
│       │   ├── 网络同步调试 [功能]
│       │   ├── 玩家行为追踪 [功能]
│       │   └── 异常检测与报告 [功能]
│       ├── NetworkDebugManager [Script Component] (网络调试管理器)
│       │   ├── 连接质量监控 [功能]
│       │   ├── 数据包分析 [功能]
│       │   ├── 同步问题检测 [功能]
│       │   └── 延迟补偿调试 [功能]
│       └── PerformanceDebugManager [Script Component] (性能调试管理器)
│           ├── 帧率监控与分析 [功能]
│           ├── 内存使用追踪 [功能]
│           ├── 渲染性能分析 [功能]
│           └── 移动端电池优化监控 [功能]
├── 📊 Analytics & Telemetry
│   ├── Game Analytics
│   ├── Performance Metrics
│   └── Player Behavior Tracking
└── 🎮 EventSystem
    ├── EventSystem Component
    └── Standalone Input Module Component
```

## 🔍 统一Debug系统设计说明

### Debug系统架构理念

本项目采用**统一Debug系统**设计，解决了原有Debug结构重复的问题：

#### 🎯 设计目标
- **统一管理**：所有调试功能集中在一个 `Debug Console` 系统中
- **平台自适应**：根据运行平台自动显示适合的调试界面
- **功能分层**：核心调试逻辑与UI表现分离
- **事件驱动**：通过事件系统解耦，避免程序集循环依赖
- **开发友好**：提供丰富的调试工具和命令接口

#### 🏗️ 系统结构
```
Debug Console (统一调试系统)
├── DebugConsole (核心控制器) - 平台无关的调试逻辑
│   ├── 事件系统 (OnLogEntryAdded, OnMobileUIToggled, etc.)
│   ├── 命令系统 (RegisterCommand, ExecuteCommand)
│   └── 日志管理 (LogDebug, 日志级别控制)
├── DebugConsoleUIAdapter (UI适配器) - 监听事件并更新UI
├── Mobile Debug UI - 移动端专用调试界面
├── Desktop Debug UI - 桌面端专用调试界面
└── Debug System Manager - 系统管理和数据收集
```

#### 🔄 事件驱动架构
为了避免程序集循环依赖，系统采用事件驱动架构：
- **Core程序集**: 定义 `DebugConsole` 核心逻辑和事件
- **UI程序集**: 实现 `DebugConsoleUIAdapter` 监听事件并更新UI
- **解耦通信**: 通过静态事件进行跨程序集通信

#### 📱 移动端特性
- **触摸点可视化**：实时显示触摸操作
- **安全区域指示**：显示刘海屏、圆角屏幕的安全区域
- **性能HUD**：轻量级的FPS、内存监控
- **简化日志面板**：适合小屏幕的日志显示

#### 🖥️ 桌面端特性
- **完整控制台**：支持命令行输入和历史记录
- **详细调试面板**：系统信息、网络状态、性能指标
- **开发者工具**：作弊命令、网络调试、性能分析器
- **多窗口界面**：可同时显示多个调试窗口

#### 🔧 使用方式
```csharp
// 获取Debug系统 (单例模式)
var debugConsole = DebugConsole.Instance;

// 移动端：通过手势激活 (四指长按2秒)
debugConsole.ToggleMobileUI();

// 桌面端：通过快捷键激活 (F1)
debugConsole.ToggleDesktopUI();

// 记录调试日志
debugConsole.LogDebug("这是一条调试信息", DebugLogType.Info);
debugConsole.LogDebug("这是一条警告信息", DebugLogType.Warning);
debugConsole.LogDebug("这是一条错误信息", DebugLogType.Error);

// 添加自定义调试命令
debugConsole.RegisterCommand("tp", "传送玩家", TeleportCommand, 2, 2);
debugConsole.RegisterCommand("spawn", "生成对象", SpawnCommand, 1, 2);

// 执行命令
debugConsole.ExecuteCommand("help");
debugConsole.ExecuteCommand("sysinfo");
debugConsole.ExecuteCommand("tp 10 20");
```

#### 📋 内置命令列表
- **help** - 显示所有可用命令
- **clear** - 清除调试日志
- **quit** - 退出应用程序
- **sysinfo** - 显示系统信息
- **fps** - 显示FPS信息
- **memory** - 显示内存信息
- **toggle_mobile** - 切换移动端UI
- **toggle_desktop** - 切换桌面端UI
- **net_info** - 显示网络信息
- **net_debug** - 切换网络调试UI

#### 🎯 快速激活方式
- **桌面端**: 按 `F1` 键切换调试UI，按 `~` 键打开命令行
- **移动端**: 四指同时触摸屏幕并保持2秒激活调试UI

#### 📁 相关文件
- **核心类**: `Assets/Scripts/Core/Debug/DebugConsole.cs`
- **使用示例**: `Assets/Scripts/Core/Debug/DebugConsoleExample.cs`
- **UI适配器**: `Assets/Scripts/UI/Mobile/Components/DebugConsoleUIAdapter.cs`
- **移动端UI**: `Assets/Scripts/UI/Mobile/Components/DebugPanelManager.cs`
- **网络调试**: `Assets/Scripts/CustomNetworking/Debug/NetworkDebugManager.cs`

#### 🔧 设置步骤
1. **在场景中添加DebugConsole**：
   ```csharp
   // 自动创建单例，或手动添加到GameObject
   var debugConsole = DebugConsole.Instance;
   ```

2. **在UI中添加适配器**：
   ```csharp
   // 在包含DebugPanelManager的GameObject上添加DebugConsoleUIAdapter组件
   var adapter = gameObject.AddComponent<DebugConsoleUIAdapter>();
   ```

3. **注册自定义命令**：
   ```csharp
   DebugConsole.Instance.RegisterCommand("my_command", "描述", MyCommandHandler);
   ```

---

## 🎮 Unity标准组件说明

### Unity场景必备组件

#### 1. Main Camera (主摄像机)
**组件**: Camera, Audio Listener
**作用**: 渲染游戏画面，接收音频

**🎥 相机系统组件** (新增):
- **MainMenu Scene**: `SimpleCameraController` 或 `MainMenuCameraController`
- **Game Scene**: `CameraFollow`
- **全局管理**: `CameraManager`

**📷 标准 Camera Component 设置**:
```
MainMenu Scene Camera 推荐设置:
├── Projection: Orthographic
├── Size: 5.0
├── Position: (0, 0, -10)
├── Clear Flags: Solid Color
├── Background: 主题色
├── Culling Mask: Everything
├── Depth: 0
├── HDR: ✓ (桌面端) / ✗ (移动端)
└── MSAA: ✓ (桌面端) / ✗ (移动端)
```

**🚀 快速设置代码**:
```csharp
// MainMenu Scene - 基础动态效果
var controller = mainCamera.AddComponent<SimpleCameraController>();
controller.ToggleFloating(true); // 启用浮动效果

// Game Scene - 玩家跟随
var follow = gameCamera.AddComponent<CameraFollow>();
follow.SetTarget(playerTransform);
follow.SetBounds(mapBounds);

// 全局管理 - 场景自动切换
var manager = new GameObject("CameraManager");
manager.AddComponent<CameraManager>();
DontDestroyOnLoad(manager);
```

**📚 详细文档**: 参见 [相机系统文档](./Docs/CameraSystem/README.md)

## 🎥 相机脚本绑定控制详解

### 1. MainMenu Scene 相机脚本绑定

#### 基础绑定 - SimpleCameraController
```csharp
// 步骤1: 获取主相机
var mainCamera = GameObject.FindWithTag("MainCamera");
if (mainCamera == null)
    mainCamera = Camera.main.gameObject;

// 步骤2: 添加SimpleCameraController组件
var controller = mainCamera.AddComponent<SimpleCameraController>();

// 步骤3: 配置基础参数
controller.orthographicSize = 5f;
controller.backgroundColor = new Color(0.1f, 0.1f, 0.15f, 1f);

// 步骤4: 启用动态效果
controller.enableFloating = true;
controller.floatAmplitude = 0.05f;
controller.floatSpeed = 1.5f;

// 步骤5: 可选的呼吸效果
controller.enableBreathing = false; // 移动端建议关闭
```

#### 高级绑定 - MainMenuCameraController
```csharp
// 步骤1: 添加高级相机控制器
var advancedController = mainCamera.AddComponent<MainMenuCameraController>();

// 步骤2: 配置浮动效果
advancedController.enableFloating = true;
advancedController.floatAmplitude = 0.1f;
advancedController.floatSpeed = 1f;
advancedController.floatDirection = Vector3.up;

// 步骤3: 配置缩放动画 (桌面端)
#if !UNITY_ANDROID && !UNITY_IOS
advancedController.enableZoomAnimation = true;
advancedController.zoomAmplitude = 0.5f;
advancedController.zoomSpeed = 0.8f;
advancedController.minZoom = 4.5f;
advancedController.maxZoom = 5.5f;
#endif

// 步骤4: 配置过渡效果
advancedController.enableTransitionEffects = true;
advancedController.transitionDuration = 2f;

// 步骤5: 启用响应式适配
advancedController.adaptToScreenSize = true;
advancedController.baseAspectRatio = 16f / 9f;
```

### 2. Game Scene 相机脚本绑定

#### 玩家跟随绑定 - CameraFollow
```csharp
// 步骤1: 获取游戏相机
var gameCamera = GameObject.FindWithTag("MainCamera");
var cameraFollow = gameCamera.AddComponent<CameraFollow>();

// 步骤2: 设置跟随目标 (通常在玩家生成后)
public void OnPlayerSpawned(GameObject player)
{
    cameraFollow.SetTarget(player.transform);
}

// 步骤3: 配置跟随参数
cameraFollow.followSpeed = 5f;
cameraFollow.offset = new Vector3(0, 0, -10);
cameraFollow.useSmoothDamping = true;
cameraFollow.smoothTime = 0.3f;
cameraFollow.maxSpeed = 10f;

// 步骤4: 设置地图边界
var mapBounds = new Bounds(Vector3.zero, new Vector3(40, 30, 0));
cameraFollow.SetBounds(mapBounds);
cameraFollow.useBounds = true;

// 步骤5: 配置高级功能
cameraFollow.enablePrediction = true;
cameraFollow.predictionTime = 0.5f;
cameraFollow.useDeadZone = true;
cameraFollow.deadZoneSize = new Vector2(2f, 1f);
```

### 3. 全局相机管理绑定

#### CameraManager 全局绑定
```csharp
// 步骤1: 创建全局相机管理器
public class GlobalCameraSetup : MonoBehaviour
{
    private void Awake()
    {
        // 确保只有一个CameraManager实例
        if (FindObjectsOfType<CameraManager>().Length == 0)
        {
            var managerGO = new GameObject("CameraManager");
            var manager = managerGO.AddComponent<CameraManager>();

            // 配置不同场景的相机设置
            ConfigureCameraSettings(manager);

            DontDestroyOnLoad(managerGO);
        }
    }

    private void ConfigureCameraSettings(CameraManager manager)
    {
        // MainMenu场景设置
        manager.mainMenuSettings = new CameraManager.CameraSettings
        {
            orthographic = true,
            orthographicSize = 5f,
            backgroundColor = Color.black,
            clearFlags = CameraClearFlags.SolidColor,
            allowHDR = !Application.isMobilePlatform,
            allowMSAA = !Application.isMobilePlatform
        };

        // 游戏场景设置
        manager.gameSettings = new CameraManager.CameraSettings
        {
            orthographic = true,
            orthographicSize = 5f,
            backgroundColor = new Color(0.1f, 0.1f, 0.15f, 1f),
            clearFlags = CameraClearFlags.SolidColor,
            cullingMask = ~(1 << LayerMask.NameToLayer("UI")), // 排除UI层
            allowHDR = !Application.isMobilePlatform,
            allowMSAA = !Application.isMobilePlatform
        };
    }
}
```

### 4. 平台特定绑定控制

#### 移动端优化绑定
```csharp
public class MobileCameraSetup : MonoBehaviour
{
    private void Start()
    {
        #if UNITY_ANDROID || UNITY_IOS
        SetupMobileCamera();
        #else
        SetupDesktopCamera();
        #endif
    }

    private void SetupMobileCamera()
    {
        var camera = Camera.main;

        // 使用轻量级控制器
        var controller = camera.gameObject.AddComponent<SimpleCameraController>();
        controller.enableFloating = true;
        controller.floatAmplitude = 0.03f; // 减小幅度
        controller.floatSpeed = 1f;        // 降低速度
        controller.enableBreathing = false; // 关闭呼吸效果

        // 优化相机设置
        camera.allowHDR = false;
        camera.allowMSAA = false;
        camera.allowDynamicResolution = true;
    }

    private void SetupDesktopCamera()
    {
        var camera = Camera.main;

        // 使用高级控制器
        var controller = camera.gameObject.AddComponent<MainMenuCameraController>();
        controller.enableFloating = true;
        controller.enableZoomAnimation = true;
        controller.enableTransitionEffects = true;

        // 高质量设置
        camera.allowHDR = true;
        camera.allowMSAA = true;
    }
}
```

### 5. 动态绑定控制

#### 运行时动态切换
```csharp
public class DynamicCameraController : MonoBehaviour
{
    private SimpleCameraController simpleController;
    private MainMenuCameraController advancedController;

    public void SwitchToSimpleMode()
    {
        // 移除高级控制器
        if (advancedController != null)
        {
            Destroy(advancedController);
            advancedController = null;
        }

        // 添加简单控制器
        if (simpleController == null)
        {
            simpleController = gameObject.AddComponent<SimpleCameraController>();
            simpleController.enableFloating = true;
        }
    }

    public void SwitchToAdvancedMode()
    {
        // 移除简单控制器
        if (simpleController != null)
        {
            Destroy(simpleController);
            simpleController = null;
        }

        // 添加高级控制器
        if (advancedController == null)
        {
            advancedController = gameObject.AddComponent<MainMenuCameraController>();
            advancedController.enableFloating = true;
            advancedController.enableZoomAnimation = true;
        }
    }
}
```

### 6. 事件驱动绑定

#### 基于游戏事件的相机控制
```csharp
public class EventDrivenCameraController : MonoBehaviour
{
    private CameraFollow cameraFollow;
    private MainMenuCameraController menuController;

    private void Start()
    {
        // 监听游戏事件
        GameEvents.OnPlayerSpawned += HandlePlayerSpawned;
        GameEvents.OnGameStateChanged += HandleGameStateChanged;
        GameEvents.OnPlayerDied += HandlePlayerDied;
    }

    private void HandlePlayerSpawned(GameObject player)
    {
        // 动态绑定玩家跟随
        if (cameraFollow == null)
            cameraFollow = Camera.main.gameObject.AddComponent<CameraFollow>();

        cameraFollow.SetTarget(player.transform);
    }

    private void HandleGameStateChanged(GameState newState)
    {
        switch (newState)
        {
            case GameState.MainMenu:
                EnableMenuCamera();
                break;
            case GameState.Playing:
                EnableGameCamera();
                break;
            case GameState.Meeting:
                // 会议时停止跟随
                if (cameraFollow != null)
                    cameraFollow.enabled = false;
                break;
        }
    }

    private void HandlePlayerDied()
    {
        // 玩家死亡时的相机震动效果
        if (cameraFollow != null)
            cameraFollow.Shake(0.5f, 1f);
    }
}
```

## 🎯 相机脚本绑定最佳实践

### 1. 组件选择策略
```csharp
// 根据场景和平台智能选择相机控制器
public static class CameraControllerFactory
{
    public static MonoBehaviour CreateCameraController(Camera camera, SceneType sceneType)
    {
        switch (sceneType)
        {
            case SceneType.MainMenu:
                return Application.isMobilePlatform
                    ? camera.gameObject.AddComponent<SimpleCameraController>()
                    : camera.gameObject.AddComponent<MainMenuCameraController>();

            case SceneType.Lobby:
                return camera.gameObject.AddComponent<SimpleCameraController>();

            case SceneType.Game:
                return camera.gameObject.AddComponent<CameraFollow>();

            default:
                return camera.gameObject.AddComponent<SimpleCameraController>();
        }
    }
}
```

### 2. 安全绑定模式
```csharp
public class SafeCameraBinding : MonoBehaviour
{
    public void SafeBindCameraController<T>() where T : MonoBehaviour
    {
        var camera = Camera.main;
        if (camera == null)
        {
            Debug.LogError("未找到主相机，无法绑定相机控制器");
            return;
        }

        // 检查是否已存在相同类型的组件
        var existingController = camera.GetComponent<T>();
        if (existingController != null)
        {
            Debug.LogWarning($"相机已存在 {typeof(T).Name} 组件，跳过绑定");
            return;
        }

        // 移除其他相机控制器组件
        RemoveOtherCameraControllers(camera);

        // 添加新的控制器
        var newController = camera.gameObject.AddComponent<T>();
        Debug.Log($"成功绑定 {typeof(T).Name} 到主相机");
    }

    private void RemoveOtherCameraControllers(Camera camera)
    {
        var controllers = new System.Type[]
        {
            typeof(SimpleCameraController),
            typeof(MainMenuCameraController),
            typeof(CameraFollow)
        };

        foreach (var controllerType in controllers)
        {
            var component = camera.GetComponent(controllerType);
            if (component != null)
            {
                DestroyImmediate(component);
            }
        }
    }
}
```

### 3. 性能优化绑定
```csharp
public class PerformanceOptimizedCameraBinding : MonoBehaviour
{
    [Header("性能设置")]
    [SerializeField] private bool enablePerformanceMonitoring = true;
    [SerializeField] private float targetFrameRate = 60f;
    [SerializeField] private float performanceCheckInterval = 1f;

    private float lastPerformanceCheck;
    private bool isLowPerformanceMode = false;

    private void Update()
    {
        if (enablePerformanceMonitoring &&
            Time.time - lastPerformanceCheck > performanceCheckInterval)
        {
            CheckPerformanceAndAdjust();
            lastPerformanceCheck = Time.time;
        }
    }

    private void CheckPerformanceAndAdjust()
    {
        float currentFPS = 1f / Time.deltaTime;
        bool shouldUseLowPerformanceMode = currentFPS < targetFrameRate * 0.8f;

        if (shouldUseLowPerformanceMode != isLowPerformanceMode)
        {
            isLowPerformanceMode = shouldUseLowPerformanceMode;
            AdjustCameraControllerForPerformance();
        }
    }

    private void AdjustCameraControllerForPerformance()
    {
        var camera = Camera.main;
        if (camera == null) return;

        if (isLowPerformanceMode)
        {
            // 切换到性能模式
            var advancedController = camera.GetComponent<MainMenuCameraController>();
            if (advancedController != null)
            {
                advancedController.SetZoomAnimationEnabled(false);
                advancedController.SetRotationEnabled(false);
            }

            var simpleController = camera.GetComponent<SimpleCameraController>();
            if (simpleController != null)
            {
                simpleController.ToggleBreathing(false);
            }
        }
        else
        {
            // 恢复正常模式
            var advancedController = camera.GetComponent<MainMenuCameraController>();
            if (advancedController != null)
            {
                advancedController.SetZoomAnimationEnabled(true);
            }
        }
    }
}
```

### 4. 调试和监控绑定
```csharp
public class CameraDebugBinding : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebugInfo = true;
    [SerializeField] private KeyCode debugToggleKey = KeyCode.F1;

    private void Update()
    {
        if (Input.GetKeyDown(debugToggleKey))
        {
            ToggleDebugInfo();
        }
    }

    private void ToggleDebugInfo()
    {
        enableDebugInfo = !enableDebugInfo;
    }

    private void OnGUI()
    {
        if (!enableDebugInfo) return;

        var camera = Camera.main;
        if (camera == null) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("=== 相机调试信息 ===");
        GUILayout.Label($"相机位置: {camera.transform.position}");
        GUILayout.Label($"相机大小: {camera.orthographicSize}");

        // 显示绑定的控制器信息
        var controllers = camera.GetComponents<MonoBehaviour>();
        foreach (var controller in controllers)
        {
            if (controller is SimpleCameraController simple)
            {
                GUILayout.Label($"SimpleCameraController:");
                GUILayout.Label($"  浮动: {simple.enableFloating}");
                GUILayout.Label($"  呼吸: {simple.enableBreathing}");
            }
            else if (controller is MainMenuCameraController advanced)
            {
                GUILayout.Label($"MainMenuCameraController:");
                GUILayout.Label($"  浮动: {advanced.enableFloating}");
                GUILayout.Label($"  缩放: {advanced.enableZoomAnimation}");
                GUILayout.Label($"  旋转: {advanced.enableRotation}");
            }
            else if (controller is CameraFollow follow)
            {
                GUILayout.Label($"CameraFollow:");
                GUILayout.Label($"  目标: {(follow.GetTarget() != null ? follow.GetTarget().name : "无")}");
                GUILayout.Label($"  边界: {follow.useBounds}");
            }
        }
        GUILayout.EndArea();
    }
}
```

## 📋 相机脚本绑定检查清单

### 场景设置检查清单

#### MainMenu Scene ✅
- [ ] 主相机存在且标签为 "MainCamera"
- [ ] 添加了 Audio Listener 组件
- [ ] 选择了合适的相机控制器：
  - [ ] 移动端：SimpleCameraController
  - [ ] 桌面端：MainMenuCameraController (可选)
- [ ] 配置了基础参数 (orthographicSize, backgroundColor)
- [ ] 启用了适当的动态效果
- [ ] 设置了平台特定的优化参数

#### Lobby Scene ✅
- [ ] 相机配置与 MainMenu 类似
- [ ] 添加了 SimpleCameraController
- [ ] 配置了轻微的浮动效果
- [ ] 确保性能优化设置正确

#### Game Scene ✅
- [ ] 添加了 CameraFollow 组件
- [ ] 设置了地图边界 (Bounds)
- [ ] 配置了跟随参数 (followSpeed, smoothTime)
- [ ] 启用了必要的高级功能 (预测移动、死区等)
- [ ] 绑定了玩家跟随目标

#### 全局设置 ✅
- [ ] 创建了 CameraManager 实例
- [ ] 配置了不同场景的相机设置
- [ ] 设置了 DontDestroyOnLoad
- [ ] 配置了平台特定的优化设置

### 代码绑定检查清单

#### 基础绑定 ✅
```csharp
// 检查相机存在
var camera = Camera.main;
Debug.Assert(camera != null, "主相机不存在");

// 检查组件绑定
var controller = camera.GetComponent<SimpleCameraController>();
Debug.Assert(controller != null, "相机控制器未绑定");

// 检查参数配置
Debug.Assert(controller.orthographicSize > 0, "相机大小配置错误");
```

#### 高级绑定 ✅
```csharp
// 检查高级控制器
var advancedController = camera.GetComponent<MainMenuCameraController>();
if (advancedController != null)
{
    Debug.Assert(advancedController.transitionDuration > 0, "过渡时间配置错误");
    Debug.Assert(advancedController.floatAmplitude >= 0, "浮动幅度不能为负");
}
```

#### 跟随绑定 ✅
```csharp
// 检查跟随组件
var follow = camera.GetComponent<CameraFollow>();
if (follow != null)
{
    Debug.Assert(follow.followSpeed > 0, "跟随速度必须大于0");
    Debug.Assert(follow.smoothTime > 0, "平滑时间必须大于0");

    if (follow.useBounds)
    {
        Debug.Assert(follow.cameraBounds.size.magnitude > 0, "相机边界大小错误");
    }
}
```

### 性能优化检查清单

#### 移动端优化 ✅
- [ ] 使用 SimpleCameraController 而非 MainMenuCameraController
- [ ] 关闭了复杂动画效果 (缩放、旋转)
- [ ] 设置了较小的浮动幅度和速度
- [ ] 禁用了 HDR 和 MSAA
- [ ] 启用了动态分辨率

#### 桌面端优化 ✅
- [ ] 可以使用 MainMenuCameraController 的完整功能
- [ ] 启用了 HDR 和 MSAA
- [ ] 配置了合适的动画参数
- [ ] 启用了响应式屏幕适配

### 常见问题排查

#### 问题1: 相机不动或移动异常
```csharp
// 排查步骤
1. 检查相机控制器是否正确绑定
2. 检查 enabled 属性是否为 true
3. 检查参数配置是否合理
4. 检查是否有多个控制器冲突
```

#### 问题2: 性能问题
```csharp
// 排查步骤
1. 检查是否使用了适合平台的控制器
2. 检查动画效果是否过于复杂
3. 检查更新频率是否过高
4. 考虑启用性能监控和自动调整
```

#### 问题3: 跟随不平滑
```csharp
// 排查步骤
1. 检查 followSpeed 和 smoothTime 参数
2. 检查目标对象是否正确设置
3. 检查边界限制是否影响移动
4. 考虑启用预测移动功能
```

---

**相机系统绑定控制完整指南**
**版本**: v1.0
**最后更新**: 2025-08-22
**适用于**: Unity 6000.1.1f1+ / Goose Duck Kill 3

---

## 💡 光照系统脚本绑定控制详解

### 1. MainMenu Scene 光照脚本绑定

#### 基础绑定 - SimpleLightController
```csharp
// 步骤1: 获取或创建 Directional Light
var directionalLight = FindObjectOfType<Light>();
if (directionalLight == null || directionalLight.type != LightType.Directional)
{
    var lightGO = new GameObject("Directional Light");
    directionalLight = lightGO.AddComponent<Light>();
    directionalLight.type = LightType.Directional;
}

// 步骤2: 添加SimpleLightController组件
var lightController = directionalLight.gameObject.AddComponent<SimpleLightController>();

// 步骤3: 配置基础参数
lightController.SetIntensity(1.2f);
lightController.SetColor(Color.white);

// 步骤4: 平台优化设置
#if UNITY_ANDROID || UNITY_IOS
lightController.SetShadowsEnabled(false); // 移动端关闭阴影
#else
lightController.SetShadowsEnabled(true);  // 桌面端启用阴影
#endif
```

#### 高级绑定 - MainMenuLightController
```csharp
// 步骤1: 添加高级光照控制器
var advancedController = directionalLight.gameObject.AddComponent<MainMenuLightController>();

// 步骤2: 配置动态效果
advancedController.enableDynamicLighting = true;
advancedController.enableIntensityPulse = !Application.isMobilePlatform; // 移动端关闭
advancedController.pulseAmplitude = 0.2f;
advancedController.pulseSpeed = 1f;

// 步骤3: 配置颜色变换 (桌面端)
#if !UNITY_ANDROID && !UNITY_IOS
advancedController.enableColorShift = true;
advancedController.colorPalette = new Color[]
{
    Color.white,
    new Color(1f, 0.95f, 0.8f), // 暖白
    new Color(0.9f, 0.9f, 1f)   // 冷白
};
advancedController.colorShiftSpeed = 0.5f;
#endif

// 步骤4: 配置时间模拟 (可选)
advancedController.enableTimeSimulation = false; // 主菜单通常不需要
advancedController.dayDuration = 120f; // 如果启用，2分钟一个周期

// 步骤5: 设置质量等级
var quality = Application.isMobilePlatform ?
    MainMenuLightController.LightingQuality.Low :
    MainMenuLightController.LightingQuality.High;
advancedController.SetQuality(quality);
```

### 2. 全局光照管理绑定

#### LightingManager 全局绑定
```csharp
// 步骤1: 创建全局光照管理器
public class GlobalLightingSetup : MonoBehaviour
{
    private void Awake()
    {
        // 确保只有一个LightingManager实例
        if (FindObjectsOfType<LightingManager>().Length == 0)
        {
            var managerGO = new GameObject("LightingManager");
            var manager = managerGO.AddComponent<LightingManager>();

            // 配置不同场景的光照配置
            ConfigureLightingProfiles(manager);

            DontDestroyOnLoad(managerGO);
        }
    }

    private void ConfigureLightingProfiles(LightingManager manager)
    {
        // MainMenu场景配置
        manager.mainMenuProfile = new LightingManager.LightingProfile
        {
            intensity = 1.2f,
            lightColor = Color.white,
            rotation = new Vector3(50f, -30f, 0f),
            shadowType = Application.isMobilePlatform ? LightShadows.None : LightShadows.Soft,
            shadowStrength = 0.8f,
            ambientSkyColor = new Color(0.54f, 0.58f, 0.66f),
            ambientEquatorColor = new Color(0.38f, 0.38f, 0.35f),
            ambientGroundColor = new Color(0.11f, 0.11f, 0.11f),
            ambientIntensity = 1f,
            enableFog = false
        };

        // Lobby场景配置
        manager.lobbyProfile = new LightingManager.LightingProfile
        {
            intensity = 1.1f,
            lightColor = new Color(1f, 0.95f, 0.8f), // 暖色调
            rotation = new Vector3(45f, -20f, 0f),
            shadowType = Application.isMobilePlatform ? LightShadows.None : LightShadows.Hard,
            shadowStrength = 0.7f,
            ambientSkyColor = new Color(0.5f, 0.55f, 0.6f),
            ambientIntensity = 0.9f,
            enableFog = false
        };

        // 游戏场景配置
        manager.gameProfile = new LightingManager.LightingProfile
        {
            intensity = 1f,
            lightColor = new Color(0.9f, 0.9f, 1f), // 冷色调
            rotation = new Vector3(60f, -45f, 0f),
            shadowType = Application.isMobilePlatform ? LightShadows.None : LightShadows.Soft,
            shadowStrength = 0.9f,
            ambientSkyColor = new Color(0.4f, 0.45f, 0.55f),
            ambientIntensity = 0.8f,
            enableFog = true,
            fogColor = new Color(0.5f, 0.5f, 0.6f),
            fogDensity = 0.005f
        };
    }
}
```

### 3. 自动光照设置绑定

#### AutoLightingSetup 自动配置
```csharp
// 步骤1: 在场景中添加自动设置脚本
public class SceneLightingSetup : MonoBehaviour
{
    private void Start()
    {
        // 创建自动光照设置
        var autoSetup = new GameObject("AutoLightingSetup");
        var setupScript = autoSetup.AddComponent<AutoLightingSetup>();

        // 配置自动设置选项
        setupScript.autoSetupOnStart = true;
        setupScript.createLightingManager = true;
        setupScript.optimizeForPlatform = true;
        setupScript.controllerType = AutoLightingSetup.LightControllerType.Auto;

        // 立即执行设置
        setupScript.SetupLighting();
    }
}
```

#### 智能控制器选择
```csharp
public class SmartLightingSetup : MonoBehaviour
{
    public void SetupSceneLighting()
    {
        string sceneName = SceneManager.GetActiveScene().name.ToLower();

        switch (sceneName)
        {
            case "mainmenu":
                SetupMainMenuLighting();
                break;
            case "lobby":
                SetupLobbyLighting();
                break;
            case "skeld":
            case "mira":
            case "polus":
                SetupGameLighting();
                break;
        }
    }

    private void SetupMainMenuLighting()
    {
        var light = EnsureDirectionalLight();

        if (Application.isMobilePlatform)
        {
            // 移动端使用简单控制器
            var controller = light.gameObject.AddComponent<SimpleLightController>();
            controller.SetShadowsEnabled(false);
            controller.SetIntensity(1f);
        }
        else
        {
            // 桌面端使用高级控制器
            var controller = light.gameObject.AddComponent<MainMenuLightController>();
            controller.enableDynamicLighting = true;
            controller.enableIntensityPulse = true;
            controller.SetQuality(MainMenuLightController.LightingQuality.High);
        }
    }

    private void SetupLobbyLighting()
    {
        var light = EnsureDirectionalLight();
        var controller = light.gameObject.AddComponent<SimpleLightController>();

        // 大厅使用暖色调
        controller.SetColor(new Color(1f, 0.95f, 0.8f));
        controller.SetIntensity(1.1f);
        controller.SetShadowsEnabled(!Application.isMobilePlatform);
    }

    private void SetupGameLighting()
    {
        var light = EnsureDirectionalLight();
        var controller = light.gameObject.AddComponent<SimpleLightController>();

        // 游戏场景使用冷色调
        controller.SetColor(new Color(0.9f, 0.9f, 1f));
        controller.SetIntensity(1f);
        controller.SetShadowsEnabled(!Application.isMobilePlatform);
    }

    private Light EnsureDirectionalLight()
    {
        var light = FindObjectOfType<Light>();
        if (light == null || light.type != LightType.Directional)
        {
            var lightGO = new GameObject("Directional Light");
            light = lightGO.AddComponent<Light>();
            light.type = LightType.Directional;
        }
        return light;
    }
}
```

### 4. 动态光照控制绑定

#### 基于游戏事件的光照控制
```csharp
public class EventDrivenLightingController : MonoBehaviour
{
    private MainMenuLightController menuController;
    private SimpleLightController simpleController;
    private LightingManager lightingManager;

    private void Start()
    {
        // 获取组件引用
        menuController = FindObjectOfType<MainMenuLightController>();
        simpleController = FindObjectOfType<SimpleLightController>();
        lightingManager = FindObjectOfType<LightingManager>();

        // 监听游戏事件
        GameEvents.OnGameStateChanged += HandleGameStateChanged;
        GameEvents.OnPlayerCountChanged += HandlePlayerCountChanged;
        GameEvents.OnSettingsChanged += HandleSettingsChanged;
    }

    private void HandleGameStateChanged(GameState newState)
    {
        switch (newState)
        {
            case GameState.MainMenu:
                // 主菜单：启用动态效果
                if (menuController != null)
                {
                    menuController.SetDynamicLighting(true);
                    menuController.enableIntensityPulse = !Application.isMobilePlatform;
                }
                break;

            case GameState.Lobby:
                // 大厅：使用稳定光照
                if (simpleController != null)
                {
                    simpleController.SetColor(new Color(1f, 0.95f, 0.8f));
                }
                break;

            case GameState.Playing:
                // 游戏中：使用游戏光照配置
                if (lightingManager != null)
                {
                    lightingManager.ApplyLightingProfile(lightingManager.gameProfile);
                }
                break;
        }
    }

    private void HandlePlayerCountChanged(int playerCount)
    {
        // 根据玩家数量调整光照强度
        float intensityMultiplier = Mathf.Lerp(0.8f, 1.2f, playerCount / 10f);

        if (simpleController != null)
        {
            simpleController.SetIntensity(1.1f * intensityMultiplier);
        }
    }

    private void HandleSettingsChanged(GameSettings settings)
    {
        // 根据设置调整光照质量
        bool highQuality = settings.graphicsQuality > 1;

        if (menuController != null)
        {
            var quality = highQuality ?
                MainMenuLightController.LightingQuality.High :
                MainMenuLightController.LightingQuality.Medium;
            menuController.SetQuality(quality);
        }
    }
}
```

## 🌟 光照系统绑定最佳实践

### 1. 组件选择策略
```csharp
// 智能光照控制器工厂
public static class LightControllerFactory
{
    public static MonoBehaviour CreateLightController(Light light, SceneType sceneType)
    {
        // 移除现有控制器
        RemoveExistingControllers(light.gameObject);

        switch (sceneType)
        {
            case SceneType.MainMenu:
                return Application.isMobilePlatform
                    ? light.gameObject.AddComponent<SimpleLightController>()
                    : light.gameObject.AddComponent<MainMenuLightController>();

            case SceneType.Lobby:
                return light.gameObject.AddComponent<SimpleLightController>();

            case SceneType.Game:
                // 游戏场景通常只需要基础光照，由LightingManager管理
                return null;

            default:
                return light.gameObject.AddComponent<SimpleLightController>();
        }
    }

    private static void RemoveExistingControllers(GameObject lightGO)
    {
        var controllers = new System.Type[]
        {
            typeof(SimpleLightController),
            typeof(MainMenuLightController)
        };

        foreach (var controllerType in controllers)
        {
            var component = lightGO.GetComponent(controllerType);
            if (component != null)
            {
                Object.DestroyImmediate(component);
            }
        }
    }
}
```

### 2. 性能优化绑定
```csharp
public class PerformanceOptimizedLightingBinding : MonoBehaviour
{
    [Header("性能监控")]
    [SerializeField] private bool enablePerformanceMonitoring = true;
    [SerializeField] private float targetFrameRate = 60f;
    [SerializeField] private float performanceCheckInterval = 2f;

    private float lastPerformanceCheck;
    private bool isLowPerformanceMode = false;

    private void Update()
    {
        if (enablePerformanceMonitoring &&
            Time.time - lastPerformanceCheck > performanceCheckInterval)
        {
            CheckPerformanceAndAdjustLighting();
            lastPerformanceCheck = Time.time;
        }
    }

    private void CheckPerformanceAndAdjustLighting()
    {
        float currentFPS = 1f / Time.deltaTime;
        bool shouldUseLowPerformanceMode = currentFPS < targetFrameRate * 0.75f;

        if (shouldUseLowPerformanceMode != isLowPerformanceMode)
        {
            isLowPerformanceMode = shouldUseLowPerformanceMode;
            AdjustLightingForPerformance();
        }
    }

    private void AdjustLightingForPerformance()
    {
        var light = FindObjectOfType<Light>();
        if (light == null) return;

        if (isLowPerformanceMode)
        {
            // 切换到性能模式
            var advancedController = light.GetComponent<MainMenuLightController>();
            if (advancedController != null)
            {
                advancedController.SetQuality(MainMenuLightController.LightingQuality.Low);
                advancedController.SetDynamicLighting(false);
            }

            var simpleController = light.GetComponent<SimpleLightController>();
            if (simpleController != null)
            {
                simpleController.SetShadowsEnabled(false);
            }

            // 降低光照质量
            light.shadows = LightShadows.None;
            light.renderMode = LightRenderMode.ForceVertex;
        }
        else
        {
            // 恢复正常模式
            var advancedController = light.GetComponent<MainMenuLightController>();
            if (advancedController != null)
            {
                var quality = Application.isMobilePlatform ?
                    MainMenuLightController.LightingQuality.Medium :
                    MainMenuLightController.LightingQuality.High;
                advancedController.SetQuality(quality);
                advancedController.SetDynamicLighting(true);
            }

            var simpleController = light.GetComponent<SimpleLightController>();
            if (simpleController != null)
            {
                simpleController.SetShadowsEnabled(!Application.isMobilePlatform);
            }
        }
    }
}
```

### 3. 调试和监控绑定
```csharp
public class LightingDebugBinding : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool enableDebugInfo = true;
    [SerializeField] private KeyCode debugToggleKey = KeyCode.F2;

    private void Update()
    {
        if (Input.GetKeyDown(debugToggleKey))
        {
            enableDebugInfo = !enableDebugInfo;
        }
    }

    private void OnGUI()
    {
        if (!enableDebugInfo) return;

        var light = FindObjectOfType<Light>();
        if (light == null) return;

        GUILayout.BeginArea(new Rect(10, 220, 350, 250));
        GUILayout.Label("=== 光照调试信息 ===");
        GUILayout.Label($"光照强度: {light.intensity:F2}");
        GUILayout.Label($"光照颜色: {light.color}");
        GUILayout.Label($"光照角度: {light.transform.eulerAngles}");
        GUILayout.Label($"阴影类型: {light.shadows}");
        GUILayout.Label($"阴影强度: {light.shadowStrength:F2}");

        // 显示绑定的控制器信息
        var controllers = light.GetComponents<MonoBehaviour>();
        foreach (var controller in controllers)
        {
            if (controller is SimpleLightController simple)
            {
                GUILayout.Label($"SimpleLightController:");
                GUILayout.Label($"  阴影启用: {simple.enableShadows}");
                GUILayout.Label($"  自动优化: {simple.autoOptimizeForMobile}");
            }
            else if (controller is MainMenuLightController advanced)
            {
                GUILayout.Label($"MainMenuLightController:");
                GUILayout.Label($"  动态光照: {advanced.enableDynamicLighting}");
                GUILayout.Label($"  强度脉冲: {advanced.enableIntensityPulse}");
                GUILayout.Label($"  颜色变换: {advanced.enableColorShift}");
                GUILayout.Label($"  时间模拟: {advanced.enableTimeSimulation}");
            }
        }

        // 环境光信息
        GUILayout.Label($"环境光模式: {RenderSettings.ambientMode}");
        GUILayout.Label($"环境光强度: {RenderSettings.ambientIntensity:F2}");
        GUILayout.Label($"雾效启用: {RenderSettings.fog}");

        GUILayout.EndArea();
    }
}
```

## 📋 光照系统绑定检查清单

### 场景设置检查清单

#### MainMenu Scene ✅
- [ ] Directional Light 存在且配置正确
- [ ] 选择了合适的光照控制器：
  - [ ] 移动端：SimpleLightController
  - [ ] 桌面端：MainMenuLightController (可选)
- [ ] 配置了基础参数 (intensity, color, rotation)
- [ ] 设置了平台特定的阴影选项
- [ ] 启用了适当的动态效果 (桌面端)
- [ ] 配置了性能优化设置

#### Lobby Scene ✅
- [ ] 光照配置与 MainMenu 类似
- [ ] 使用了 SimpleLightController
- [ ] 配置了暖色调光照 (1f, 0.95f, 0.8f)
- [ ] 设置了合适的强度 (1.1f)
- [ ] 确保性能优化设置正确

#### Game Scene ✅
- [ ] 基础 Directional Light 配置
- [ ] 使用了冷色调光照 (0.9f, 0.9f, 1f)
- [ ] 配置了游戏环境光照
- [ ] 启用了雾效 (如果需要)
- [ ] 设置了合适的阴影参数

#### 全局设置 ✅
- [ ] 创建了 LightingManager 实例
- [ ] 配置了不同场景的光照配置文件
- [ ] 设置了 DontDestroyOnLoad
- [ ] 配置了环境光和雾效设置
- [ ] 设置了平台特定的优化参数

### 代码绑定检查清单

#### 基础绑定 ✅
```csharp
// 检查光照存在
var light = FindObjectOfType<Light>();
Debug.Assert(light != null, "Directional Light 不存在");
Debug.Assert(light.type == LightType.Directional, "光照类型不正确");

// 检查组件绑定
var controller = light.GetComponent<SimpleLightController>();
Debug.Assert(controller != null, "光照控制器未绑定");

// 检查参数配置
Debug.Assert(light.intensity > 0, "光照强度配置错误");
Debug.Assert(light.color != Color.black, "光照颜色配置错误");
```

#### 高级绑定 ✅
```csharp
// 检查高级控制器
var advancedController = light.GetComponent<MainMenuLightController>();
if (advancedController != null)
{
    Debug.Assert(advancedController.intensity > 0, "强度配置错误");
    Debug.Assert(advancedController.pulseAmplitude >= 0, "脉冲幅度不能为负");

    if (advancedController.enableColorShift)
    {
        Debug.Assert(advancedController.colorPalette.Length > 1, "颜色调色板至少需要2种颜色");
    }
}
```

#### 全局管理绑定 ✅
```csharp
// 检查全局管理器
var manager = FindObjectOfType<LightingManager>();
if (manager != null)
{
    Debug.Assert(manager.mainMenuProfile != null, "主菜单光照配置未设置");
    Debug.Assert(manager.lobbyProfile != null, "大厅光照配置未设置");
    Debug.Assert(manager.gameProfile != null, "游戏光照配置未设置");
}
```

### 性能优化检查清单

#### 移动端优化 ✅
- [ ] 使用 SimpleLightController 而非 MainMenuLightController
- [ ] 关闭了阴影 (shadows = LightShadows.None)
- [ ] 使用了顶点光照 (renderMode = ForceVertex)
- [ ] 关闭了复杂动态效果
- [ ] 降低了光照强度以节省性能

#### 桌面端优化 ✅
- [ ] 可以使用 MainMenuLightController 的完整功能
- [ ] 启用了软阴影 (shadows = LightShadows.Soft)
- [ ] 使用了像素光照 (renderMode = Auto/ForcePixel)
- [ ] 启用了动态效果
- [ ] 配置了合适的质量等级

### 常见问题排查

#### 问题1: 光照不显示或异常
```csharp
// 排查步骤
1. 检查 Directional Light 是否存在且类型正确
2. 检查光照强度是否大于0
3. 检查光照颜色是否不为黑色
4. 检查光照角度是否合理
5. 检查是否有多个光照控制器冲突
```

#### 问题2: 性能问题
```csharp
// 排查步骤
1. 检查是否使用了适合平台的控制器
2. 检查阴影设置是否过于复杂
3. 检查动态效果是否过多
4. 考虑启用性能监控和自动调整
```

#### 问题3: 动态效果不工作
```csharp
// 排查步骤
1. 检查 enableDynamicLighting 是否为 true
2. 检查具体效果的启用开关
3. 检查参数配置是否合理
4. 检查平台限制是否影响效果
```

---

**光照系统绑定控制完整指南**
**版本**: v1.0
**最后更新**: 2025-08-22
**适用于**: Unity 6000.1.1f1+ / Goose Duck Kill 3

---

## 📱 Mobile UI Root 脚本绑定控制详解

### 1. Mobile UI Root 完整结构

**Mobile UI Root 不是空的 GameObject**，它是一个包含完整移动端UI系统的复杂结构：

```
📱 Mobile UI Root
├── 🎮 MobileUIRoot.cs (核心控制器)
├── 🛡️ Safe Area Root (子GameObject)
│   └── SafeAreaHandler.cs (安全区域处理)
├── 🎮 Mobile Controls Root (子GameObject)
│   ├── MobileControlsManager.cs (控制管理器)
│   ├── Virtual Joystick (子GameObject)
│   ├── Action Buttons (子GameObject群组)
│   ├── GestureRecognizer.cs (手势识别)
│   └── HapticFeedbackController.cs (触觉反馈)
└── 📊 Mobile HUD Root (子GameObject)
    └── MobileHUDManager.cs (HUD管理器)
```

### 2. MainMenu Scene Mobile UI Root 绑定

#### 自动创建完整结构
```csharp
// 步骤1: 在Canvas下创建Mobile UI Root
public class MainMenuMobileUISetup : MonoBehaviour
{
    private void Start()
    {
        // 只在移动平台上执行
        if (!Application.isMobilePlatform) return;

        CreateMobileUIRoot();
    }

    private void CreateMobileUIRoot()
    {
        var canvas = FindObjectOfType<Canvas>();
        if (canvas == null) return;

        // 创建Mobile UI Root主节点
        var mobileUIRootGO = new GameObject("Mobile UI Root");
        mobileUIRootGO.transform.SetParent(canvas.transform, false);

        // 添加RectTransform并设置为全屏
        var rectTransform = mobileUIRootGO.AddComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;

        // 添加核心控制器
        var mobileUIRoot = mobileUIRootGO.AddComponent<MobileUIRoot>();

        // 配置自适应设置
        mobileUIRoot.enableSafeAreaAdaptation = true;
        mobileUIRoot.enableOrientationAdaptation = true;
        mobileUIRoot.enableDynamicScaling = true;
        mobileUIRoot.enablePerformanceOptimization = true;

        Debug.Log("Mobile UI Root 结构创建完成");
    }
}
```

#### 手动配置详细结构
```csharp
public class DetailedMobileUISetup : MonoBehaviour
{
    [Header("预制体引用")]
    [SerializeField] private GameObject virtualJoystickPrefab;
    [SerializeField] private GameObject actionButtonPrefab;

    public void SetupDetailedMobileUI()
    {
        var canvas = FindObjectOfType<Canvas>();
        var mobileUIRoot = CreateMobileUIRootStructure(canvas);

        ConfigureMobileUIRoot(mobileUIRoot);
        ConfigureSafeAreaHandler(mobileUIRoot);
        ConfigureMobileControls(mobileUIRoot);
        ConfigureMobileHUD(mobileUIRoot);
    }

    private MobileUIRoot CreateMobileUIRootStructure(Canvas canvas)
    {
        // 创建主节点
        var rootGO = new GameObject("Mobile UI Root");
        rootGO.transform.SetParent(canvas.transform, false);

        var rectTransform = rootGO.AddComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;

        // 添加核心控制器
        var mobileUIRoot = rootGO.AddComponent<MobileUIRoot>();

        // 创建子结构
        CreateSafeAreaRoot(rootGO);
        CreateMobileControlsRoot(rootGO);
        CreateMobileHUDRoot(rootGO);

        return mobileUIRoot;
    }

    private void CreateSafeAreaRoot(GameObject parent)
    {
        var safeAreaGO = new GameObject("Safe Area Root");
        safeAreaGO.transform.SetParent(parent.transform, false);

        var rectTransform = safeAreaGO.AddComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;

        // 添加安全区域处理器
        var safeAreaHandler = safeAreaGO.AddComponent<SafeAreaHandler>();
        safeAreaHandler.enableSafeArea = true;
        safeAreaHandler.adaptToNotch = true;
        safeAreaHandler.adaptToHomeIndicator = true;
        safeAreaHandler.adaptToRoundedCorners = true;
    }

    private void CreateMobileControlsRoot(GameObject parent)
    {
        var controlsGO = new GameObject("Mobile Controls Root");
        controlsGO.transform.SetParent(parent.transform, false);

        var rectTransform = controlsGO.AddComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 0);
        rectTransform.anchorMax = new Vector2(1, 0.3f); // 占屏幕下方30%
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;

        // 添加控制管理器
        var controlsManager = controlsGO.AddComponent<MobileControlsManager>();

        // 配置虚拟摇杆
        controlsManager.enableVirtualJoystick = true;
        controlsManager.virtualJoystickPrefab = virtualJoystickPrefab;
        controlsManager.joystickPosition = new Vector2(150, 150);
        controlsManager.joystickSize = 120f;

        // 配置动作按钮
        controlsManager.enableActionButtons = true;
        controlsManager.actionButtonPrefab = actionButtonPrefab;

        // 配置主菜单专用按钮
        controlsManager.actionButtons = new MobileControlsManager.ActionButtonConfig[]
        {
            new MobileControlsManager.ActionButtonConfig
            {
                buttonName = "Settings Button",
                position = new Vector2(120, 120),
                size = 80f,
                color = Color.white,
                isVisible = true,
                keyCode = KeyCode.Escape
            },
            new MobileControlsManager.ActionButtonConfig
            {
                buttonName = "Menu Button",
                position = new Vector2(220, 120),
                size = 80f,
                color = Color.cyan,
                isVisible = true,
                keyCode = KeyCode.Tab
            }
        };

        // 配置手势和触觉反馈
        controlsManager.enableGestureRecognition = true;
        controlsManager.enableHapticFeedback = true;
        controlsManager.swipeThreshold = 50f;
        controlsManager.tapTimeThreshold = 0.3f;
    }

    private void CreateMobileHUDRoot(GameObject parent)
    {
        var hudGO = new GameObject("Mobile HUD Root");
        hudGO.transform.SetParent(parent.transform, false);

        var rectTransform = hudGO.AddComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;

        // 添加HUD管理器
        var hudManager = hudGO.AddComponent<MobileHUDManager>();
        hudManager.enableStatusIndicators = true;
        hudManager.enableNotificationSystem = true;
        hudManager.enableDebugPanel = false; // 发布版本关闭
    }
}
```

### 3. 组件配置详解

#### MobileUIRoot 核心配置
```csharp
public class MobileUIRootConfiguration : MonoBehaviour
{
    public void ConfigureMobileUIRoot()
    {
        var mobileUIRoot = GetComponent<MobileUIRoot>();
        if (mobileUIRoot == null) return;

        // 基础自适应设置
        mobileUIRoot.enableSafeAreaAdaptation = true;
        mobileUIRoot.enableOrientationAdaptation = true;
        mobileUIRoot.enableDynamicScaling = true;

        // 布局设置
        mobileUIRoot.referenceResolution = new Vector2(1920, 1080);
        mobileUIRoot.minScaleFactor = 0.5f;
        mobileUIRoot.maxScaleFactor = 2f;

        // 性能优化
        mobileUIRoot.enablePerformanceOptimization = true;
        mobileUIRoot.targetFrameRate = 60;

        // 强制更新布局
        mobileUIRoot.ForceUpdateLayout();
    }
}
```

#### SafeAreaHandler 配置
```csharp
public class SafeAreaConfiguration : MonoBehaviour
{
    public void ConfigureSafeArea()
    {
        var safeAreaHandler = GetComponent<SafeAreaHandler>();
        if (safeAreaHandler == null) return;

        // 启用安全区域适配
        safeAreaHandler.enableSafeArea = true;

        // 适配选项
        safeAreaHandler.adaptToNotch = true;           // 刘海屏适配
        safeAreaHandler.adaptToHomeIndicator = true;   // Home指示器适配
        safeAreaHandler.adaptToRoundedCorners = true;  // 圆角屏幕适配

        // 额外边距设置
        safeAreaHandler.SetAdditionalMargins(
            top: 10f,    // 顶部额外边距
            bottom: 10f, // 底部额外边距
            left: 10f,   // 左侧额外边距
            right: 10f   // 右侧额外边距
        );

        // 启用调试可视化 (开发阶段)
        #if DEVELOPMENT_BUILD || UNITY_EDITOR
        safeAreaHandler.SetDebugVisualizationEnabled(true);
        #endif

        // 获取安全区域信息
        var safeAreaInfo = safeAreaHandler.GetSafeAreaInfo();
        Debug.Log($"安全区域信息: {safeAreaInfo}");
    }
}
```

#### MobileControlsManager 配置
```csharp
public class MobileControlsConfiguration : MonoBehaviour
{
    public void ConfigureMobileControls()
    {
        var controlsManager = GetComponent<MobileControlsManager>();
        if (controlsManager == null) return;

        // 虚拟摇杆配置
        controlsManager.enableVirtualJoystick = true;
        controlsManager.joystickPosition = new Vector2(150, 150);
        controlsManager.joystickSize = 120f;

        // 动作按钮配置
        controlsManager.enableActionButtons = true;

        // 手势识别配置
        controlsManager.enableGestureRecognition = true;
        controlsManager.swipeThreshold = 50f;
        controlsManager.tapTimeThreshold = 0.3f;

        // 触觉反馈配置
        controlsManager.enableHapticFeedback = true;
        controlsManager.defaultHapticType = MobileControlsManager.HapticFeedbackType.Light;

        // 性能设置
        controlsManager.updateFrequency = 1f; // 每秒更新1次
        controlsManager.enableVisualEffects = !Application.isMobilePlatform ||
                                             SystemInfo.processorCount > 4; // 高端设备启用视觉效果

        // 初始化控制元素
        controlsManager.InitializeControls();
    }
}
```

### 4. 自动化设置脚本

#### 一键式Mobile UI设置
```csharp
public class AutoMobileUISetup : MonoBehaviour
{
    [Header("自动设置选项")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool onlyOnMobilePlatform = true;

    [Header("预制体引用")]
    [SerializeField] private GameObject mobileUIRootPrefab;

    private void Start()
    {
        if (autoSetupOnStart)
        {
            SetupMobileUI();
        }
    }

    public void SetupMobileUI()
    {
        // 平台检查
        if (onlyOnMobilePlatform && !Application.isMobilePlatform)
        {
            Debug.Log("非移动平台，跳过Mobile UI设置");
            return;
        }

        var canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            Debug.LogError("未找到Canvas，无法创建Mobile UI");
            return;
        }

        // 检查是否已存在
        if (canvas.transform.Find("Mobile UI Root") != null)
        {
            Debug.Log("Mobile UI Root 已存在，跳过创建");
            return;
        }

        // 创建Mobile UI Root
        GameObject mobileUIRootGO;

        if (mobileUIRootPrefab != null)
        {
            mobileUIRootGO = Instantiate(mobileUIRootPrefab, canvas.transform);
        }
        else
        {
            mobileUIRootGO = CreateDefaultMobileUIRoot(canvas);
        }

        mobileUIRootGO.name = "Mobile UI Root";

        // 配置组件
        ConfigureAllComponents(mobileUIRootGO);

        Debug.Log("Mobile UI Root 自动设置完成");
    }

    private GameObject CreateDefaultMobileUIRoot(Canvas canvas)
    {
        var rootGO = new GameObject("Mobile UI Root");
        rootGO.transform.SetParent(canvas.transform, false);

        // 设置RectTransform
        var rectTransform = rootGO.AddComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;

        // 添加核心组件
        rootGO.AddComponent<MobileUIRoot>();

        return rootGO;
    }

    private void ConfigureAllComponents(GameObject mobileUIRootGO)
    {
        var mobileUIRoot = mobileUIRootGO.GetComponent<MobileUIRoot>();
        if (mobileUIRoot != null)
        {
            // 启用所有自适应功能
            mobileUIRoot.enableSafeAreaAdaptation = true;
            mobileUIRoot.enableOrientationAdaptation = true;
            mobileUIRoot.enableDynamicScaling = true;
            mobileUIRoot.enablePerformanceOptimization = true;

            // 设置参考分辨率
            mobileUIRoot.referenceResolution = new Vector2(1920, 1080);
            mobileUIRoot.targetFrameRate = 60;
        }
    }

    /// <summary>
    /// 编辑器快捷菜单
    /// </summary>
    [ContextMenu("Setup Mobile UI Now")]
    private void SetupMobileUIContextMenu()
    {
        SetupMobileUI();
    }

    [ContextMenu("Remove Mobile UI")]
    private void RemoveMobileUIContextMenu()
    {
        var canvas = FindObjectOfType<Canvas>();
        if (canvas != null)
        {
            var mobileUIRoot = canvas.transform.Find("Mobile UI Root");
            if (mobileUIRoot != null)
            {
                DestroyImmediate(mobileUIRoot.gameObject);
                Debug.Log("Mobile UI Root 已移除");
            }
        }
    }
}
```

#### 2. Directional Light (方向光)
**组件**: Light
**作用**: 提供场景主要光照
```csharp
// 动态光照控制
public class LightingController : MonoBehaviour
{
    [Header("Lighting Settings")]
    public Light directionalLight;
    public Color normalLightColor = Color.white;
    public Color emergencyLightColor = Color.red;
    public float lightTransitionSpeed = 2f;

    private bool isEmergencyMode = false;

    public void SetEmergencyLighting(bool emergency)
    {
        isEmergencyMode = emergency;
        StartCoroutine(TransitionLighting());
    }

    private IEnumerator TransitionLighting()
    {
        Color targetColor = isEmergencyMode ? emergencyLightColor : normalLightColor;
        Color startColor = directionalLight.color;

        float elapsed = 0f;
        while (elapsed < 1f)
        {
            elapsed += Time.deltaTime * lightTransitionSpeed;
            directionalLight.color = Color.Lerp(startColor, targetColor, elapsed);
            yield return null;
        }
    }
}
```

#### 3. EventSystem (事件系统)
**组件**: EventSystem, Standalone Input Module
**作用**: 处理UI交互事件
```csharp
// EventSystem配置
public class UIEventSystemSetup : MonoBehaviour
{
    private void Start()
    {
        // 确保场景中只有一个EventSystem
        var eventSystems = FindObjectsOfType<EventSystem>();
        if (eventSystems.Length > 1)
        {
            for (int i = 1; i < eventSystems.Length; i++)
            {
                Destroy(eventSystems[i].gameObject);
            }
        }

        // 配置输入模块
        var inputModule = FindObjectOfType<StandaloneInputModule>();
        if (inputModule != null)
        {
            inputModule.horizontalAxis = "Horizontal";
            inputModule.verticalAxis = "Vertical";
            inputModule.submitButton = "Submit";
            inputModule.cancelButton = "Cancel";
        }
    }
}
```

## 🔗 场景间通信与数据流

### 场景切换流程
```mermaid
graph TD
    A[MainMenu Scene] -->|Connect to Server| B[Lobby Scene]
    B -->|Start Game| C[Map Scene]
    C -->|Game End| D[Results Screen]
    D -->|Return to Lobby| B
    D -->|Disconnect| A

    C -->|Meeting Called| E[Meeting State]
    E -->|Meeting End| C

    C -->|Sabotage| F[Sabotage State]
    F -->|Repair Complete| C
```

### 数据持久化
- **PlayerPrefs**: 玩家设置、用户名、音频设置
- **Network State**: 游戏状态、玩家数据、房间信息
- **Session Data**: 当前游戏会话的临时数据

### 网络同步架构
```
Client A ←→ NetworkRunner ←→ Server ←→ NetworkRunner ←→ Client B
    ↓           ↓                ↓           ↓
GameManager GameManager    GameManager GameManager
    ↓           ↓                ↓           ↓
Local UI    Local UI        Local UI    Local UI
```

## 📱 移动端适配设计

### UI适配策略
- **Safe Area**: 自动适配刘海屏和圆角屏幕
- **Touch Controls**: 虚拟摇杆和触摸按钮
- **Responsive Layout**: 基于屏幕尺寸的动态布局
- **Performance Scaling**: 根据设备性能自动调整质量

### 移动端特殊组件
```
Mobile UI Root
├── Safe Area Handler
├── Virtual Joystick Controller
├── Touch Button Manager
├── Gesture Recognition System
├── Haptic Feedback Controller
└── Mobile Performance Monitor
```

## 🎯 最佳实践与设计原则

### 场景组织原则
1. **模块化设计**: 每个功能模块独立组织
2. **层次清晰**: 使用空GameObject作为组织容器
3. **命名规范**: 统一的命名约定便于维护
4. **预制体复用**: 最大化预制体的使用

### 性能优化策略
1. **对象池**: 频繁创建/销毁的对象使用对象池
2. **LOD系统**: 根据距离调整渲染质量
3. **批处理**: 合并相似的渲染调用
4. **异步加载**: 大型资源异步加载避免卡顿

### 网络优化
1. **状态同步**: 只同步必要的状态变化
2. **RPC优化**: 合理使用RPC的可靠性和优先级
3. **带宽管理**: 根据网络条件调整同步频率
4. **权限管理**: 明确的客户端/服务器权限划分

## 🛠️ 场景实现指南

### 1. 创建新场景的步骤

#### 基础场景设置
```csharp
// 1. 创建场景基础结构
public class SceneSetup : MonoBehaviour
{
    [MenuItem("Tools/Create Game Scene")]
    public static void CreateGameScene()
    {
        // 创建基础GameObject层次结构
        CreateSceneHierarchy();

        // 设置网络组件
        SetupNetworkComponents();

        // 配置UI Canvas
        SetupUICanvas();

        // 添加音频系统
        SetupAudioSystem();
    }

    private static void CreateSceneHierarchy()
    {
        // Environment Root
        var envRoot = new GameObject("Environment Root");

        // Players Root
        var playersRoot = new GameObject("Players Root");

        // Managers Root
        var managersRoot = new GameObject("Game Managers");

        // UI Root
        var uiRoot = new GameObject("UI Canvas");
    }
}
```

#### 网络组件配置
```csharp
// 2. 配置网络管理器
public class GameSceneNetworkSetup : MonoBehaviour
{
    private void Start()
    {
        SetupNetworkRunner();
        SetupGameManager();
        SetupPlayerSpawning();
    }

    private void SetupNetworkRunner()
    {
        var runner = FindObjectOfType<NetworkRunner>();
        if (runner == null)
        {
            var runnerGO = new GameObject("NetworkRunner");
            runner = runnerGO.AddComponent<NetworkRunner>();
        }
    }

    private void SetupGameManager()
    {
        var gameManager = FindObjectOfType<GameManager>();
        if (gameManager == null)
        {
            var gmGO = new GameObject("GameManager");
            gameManager = gmGO.AddComponent<GameManager>();
        }
    }
}
```

### 2. UI系统集成

#### Canvas配置
```csharp
// UI Canvas 标准配置
public class UICanvasSetup : MonoBehaviour
{
    [Header("Canvas Settings")]
    public RenderMode renderMode = RenderMode.ScreenSpaceOverlay;
    public int sortingOrder = 0;

    [Header("Canvas Scaler Settings")]
    public CanvasScaler.ScaleMode scaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
    public Vector2 referenceResolution = new Vector2(1920, 1080);
    public float matchWidthOrHeight = 0.5f;

    private void Start()
    {
        SetupCanvas();
        SetupCanvasScaler();
        SetupGraphicRaycaster();
    }

    private void SetupCanvas()
    {
        var canvas = GetComponent<Canvas>();
        canvas.renderMode = renderMode;
        canvas.sortingOrder = sortingOrder;
    }
}
```

#### 移动端UI适配
```csharp
// 移动端UI适配器
public class MobileUIAdapter : MonoBehaviour
{
    [Header("Mobile UI Settings")]
    public GameObject mobileUIRoot;
    public GameObject desktopUIRoot;
    public VirtualJoystick virtualJoystick;
    public TouchButton[] touchButtons;

    private void Start()
    {
        bool isMobile = Application.isMobilePlatform;

        if (mobileUIRoot != null)
            mobileUIRoot.SetActive(isMobile);

        if (desktopUIRoot != null)
            desktopUIRoot.SetActive(!isMobile);

        if (isMobile)
        {
            SetupMobileControls();
        }
    }

    private void SetupMobileControls()
    {
        // 配置虚拟摇杆
        if (virtualJoystick != null)
        {
            virtualJoystick.joystickType = VirtualJoystick.JoystickType.Dynamic;
            virtualJoystick.joystickRange = 50f;
        }

        // 配置触摸按钮
        foreach (var button in touchButtons)
        {
            button.enableHapticFeedback = true;
            button.animationDuration = 0.1f;
        }
    }
}
```

### 3. 任务系统集成

#### 任务站点配置
```csharp
// 任务站点组件
public class TaskStation : NetworkBehaviour
{
    [Header("Task Configuration")]
    public string taskId;
    public string taskName;
    public TaskType taskType;
    public float taskDuration = 5f;
    public bool isVisualTask = false;

    [Header("Interaction")]
    public float interactionRange = 2f;
    public LayerMask playerLayer = 1;

    [Networked] public bool IsCompleted { get; set; }
    [Networked] public PlayerRef AssignedPlayer { get; set; }

    public override void Spawned()
    {
        base.Spawned();

        // 初始化任务站点
        InitializeTaskStation();
    }

    private void InitializeTaskStation()
    {
        // 设置交互区域
        var trigger = gameObject.AddComponent<SphereCollider>();
        trigger.isTrigger = true;
        trigger.radius = interactionRange;

        // 注册到任务管理器
        var taskManager = FindObjectOfType<TaskManager>();
        taskManager?.RegisterTaskStation(this);
    }

    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public void StartTask(PlayerRef player)
    {
        if (HasStateAuthority && !IsCompleted)
        {
            AssignedPlayer = player;
            // 开始任务逻辑
        }
    }
}
```

### 4. 性能优化配置

#### 对象池系统
```csharp
// 对象池管理器
public class ObjectPoolManager : MonoBehaviour
{
    [System.Serializable]
    public class PoolInfo
    {
        public string poolName;
        public GameObject prefab;
        public int poolSize = 10;
        public bool expandable = true;
    }

    [Header("Pool Configuration")]
    public PoolInfo[] pools;

    private Dictionary<string, Queue<GameObject>> poolDictionary;

    private void Start()
    {
        InitializePools();
    }

    private void InitializePools()
    {
        poolDictionary = new Dictionary<string, Queue<GameObject>>();

        foreach (var pool in pools)
        {
            var objectQueue = new Queue<GameObject>();

            for (int i = 0; i < pool.poolSize; i++)
            {
                var obj = Instantiate(pool.prefab);
                obj.SetActive(false);
                objectQueue.Enqueue(obj);
            }

            poolDictionary.Add(pool.poolName, objectQueue);
        }
    }

    public GameObject SpawnFromPool(string poolName, Vector3 position, Quaternion rotation)
    {
        if (!poolDictionary.ContainsKey(poolName))
        {
            Debug.LogWarning($"Pool {poolName} doesn't exist!");
            return null;
        }

        var objectToSpawn = poolDictionary[poolName].Dequeue();
        objectToSpawn.SetActive(true);
        objectToSpawn.transform.position = position;
        objectToSpawn.transform.rotation = rotation;

        poolDictionary[poolName].Enqueue(objectToSpawn);

        return objectToSpawn;
    }
}
```

### 5. 调试和开发工具

#### 场景调试器
```csharp
// 场景调试工具
public class SceneDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool showDebugInfo = true;
    public bool enableCheatCommands = false;
    public KeyCode debugToggleKey = KeyCode.F1;

    private bool debugUIVisible = false;

    private void Update()
    {
        if (Input.GetKeyDown(debugToggleKey))
        {
            debugUIVisible = !debugUIVisible;
        }

        if (enableCheatCommands)
        {
            HandleCheatCommands();
        }
    }

    private void OnGUI()
    {
        if (!showDebugInfo || !debugUIVisible) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 400));
        GUILayout.Label("=== Scene Debug Info ===");

        // 显示网络状态
        var runner = FindObjectOfType<NetworkRunner>();
        if (runner != null)
        {
            GUILayout.Label($"Network State: {runner.State}");
            GUILayout.Label($"Is Server: {runner.IsServer}");
            GUILayout.Label($"Player Count: {runner.ActivePlayers.Count()}");
        }

        // 显示游戏状态
        var gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            GUILayout.Label($"Game Active: {gameManager.IsGameActive}");
            GUILayout.Label($"Current Round: {gameManager.CurrentRound}");
        }

        GUILayout.EndArea();
    }

    private void HandleCheatCommands()
    {
        if (Input.GetKeyDown(KeyCode.F2))
        {
            // 快速开始游戏
            GameManager.Instance?.StartGame();
        }

        if (Input.GetKeyDown(KeyCode.F3))
        {
            // 召开紧急会议
            var localPlayer = NetworkRunner.LocalPlayer;
            GameManager.Instance?.CallMeeting(localPlayer, false);
        }
    }
}
```

## 📊 场景性能监控

### 性能指标监控
```csharp
// 性能监控器
public class ScenePerformanceMonitor : MonoBehaviour
{
    [Header("Performance Settings")]
    public bool enableMonitoring = true;
    public float updateInterval = 1f;

    private float frameCount = 0;
    private float dt = 0f;
    private float fps = 0f;
    private float ms = 0f;

    private void Update()
    {
        if (!enableMonitoring) return;

        frameCount++;
        dt += Time.deltaTime;

        if (dt > updateInterval)
        {
            fps = frameCount / dt;
            ms = dt / frameCount * 1000f;
            frameCount = 0;
            dt -= updateInterval;

            // 记录性能数据
            LogPerformanceData();
        }
    }

    private void LogPerformanceData()
    {
        var memoryUsage = System.GC.GetTotalMemory(false) / 1024 / 1024; // MB

        Debug.Log($"Performance: FPS={fps:F1}, MS={ms:F1}, Memory={memoryUsage}MB");

        // 如果性能过低，触发优化
        if (fps < 30f)
        {
            TriggerPerformanceOptimization();
        }
    }

    private void TriggerPerformanceOptimization()
    {
        var optimizer = FindObjectOfType<MobilePerformanceOptimizer>();
        optimizer?.OptimizeForLowPerformance();
    }
}
```

---

## 📚 相关文档

- [项目结构与代码绑定使用文档](./项目结构与代码绑定使用文档.md)
- [快速参考指南](./快速参考指南.md)
- [README](./README.md)

## 🔧 工具和资源

### Unity编辑器工具
- **Scene Hierarchy Organizer**: 自动整理场景层次结构
- **Network Debug Visualizer**: 可视化网络状态和RPC调用
- **Performance Profiler Integration**: 集成Unity Profiler数据

### 预制体模板
- **GameScene Template**: 标准游戏场景模板
- **UI Canvas Template**: 标准UI画布模板
- **Network Manager Template**: 网络管理器模板

### 配置文件
- **Scene Settings**: 场景特定的配置文件
- **Performance Profiles**: 不同平台的性能配置
- **Network Settings**: 网络相关配置

---

## 📊 GameObject 与组件分类总结

### 🎯 GameObject 类型分类

#### 1. 核心场景对象 [GameObject]
- **Main Camera** - 主摄像机对象
- **Directional Light** - 方向光对象
- **Canvas** - UI画布对象
- **EventSystem** - 事件系统对象

#### 2. UI容器对象 [GameObject]
- **Mobile UI Root** - 移动端UI根容器
- **Safe Area Root** - 安全区域容器
- **Mobile Controls Root** - 移动控制容器
- **Mobile HUD Root** - 移动HUD容器
- **Desktop UI Root** - 桌面端UI根容器

#### 3. 功能性对象 [GameObject]
- **Virtual Joystick** - 虚拟摇杆对象
- **Action Buttons** - 动作按钮对象
- **Status Indicators** - 状态指示器对象
- **Notification System** - 通知系统对象
- **Debug Panel** - 调试面板对象

#### 4. 管理器对象 [GameObject]
- **Network Manager** - 网络管理器对象
- **Audio Manager** - 音频管理器对象
- **UI Manager** - UI管理器对象
- **Game Manager** - 游戏管理器对象

### 🔧 组件类型分类

#### 1. Unity 内置组件 [Unity Component]
- **Camera** - 摄像机组件
- **Light** - 光照组件
- **Canvas** - 画布组件
- **RectTransform** - UI变换组件
- **Image** - 图像组件
- **Text** - 文本组件
- **Button** - 按钮组件
- **Slider** - 滑动条组件
- **ScrollRect** - 滚动视图组件
- **Audio Source** - 音频源组件
- **MeshRenderer** - 网格渲染器组件
- **Collider** - 碰撞器组件
- **Animator** - 动画器组件
- **ParticleSystem** - 粒子系统组件

#### 2. 自定义脚本组件 [Script Component]

##### 摄像机控制脚本
- **SimpleCameraController** - 简单摄像机控制器
- **MainMenuCameraController** - 主菜单摄像机控制器
- **GameCameraController** - 游戏摄像机控制器
- **CameraFollow** - 摄像机跟随脚本
- **CameraManager** - 摄像机管理器

##### 光照控制脚本
- **SimpleLightController** - 简单光照控制器
- **MainMenuLightController** - 主菜单光照控制器
- **LightingManager** - 光照管理器
- **AutoLightingSetup** - 自动光照设置

##### 移动端UI脚本
- **MobileUIRoot** - 移动端UI根控制器
- **SafeAreaHandler** - 安全区域处理器
- **MobileControlsManager** - 移动控制管理器
- **SimpleVirtualJoystick** - 简单虚拟摇杆
- **MobileActionButton** - 移动动作按钮
- **GestureRecognizer** - 手势识别器
- **HapticFeedbackController** - 触觉反馈控制器

##### HUD系统脚本
- **MobileHUDManager** - 移动HUD管理器
- **NetworkIndicator** - 网络状态指示器
- **FPSCounter** - FPS计数器
- **BatteryIndicator** - 电池指示器
- **PerformanceMonitor** - 性能监控器
- **NotificationManager** - 通知管理器
- **DebugPanelManager** - 调试面板管理器

##### 系统管理脚本
- **NetworkRunner** - 网络运行器
- **NetworkManager** - 网络管理器
- **AuthManager** - 认证管理器
- **AudioManager** - 音频管理器
- **GameManager** - 游戏管理器
- **SceneManager** - 场景管理器
- **PlayerManager** - 玩家管理器

#### 3. 功能特性 [功能]
- **自适应布局管理** - 动态调整UI布局
- **屏幕方向适配** - 横竖屏自动适配
- **动态缩放控制** - 根据屏幕尺寸缩放
- **性能优化监控** - 实时性能监控和优化
- **刘海屏适配** - 异形屏幕适配
- **触摸检测** - 触摸输入处理
- **手势识别** - 多种手势识别
- **触觉反馈** - 震动反馈控制
- **动态光照效果** - 实时光照变化
- **平台优化** - 不同平台的优化策略

### 🏗️ 层级结构说明

#### GameObject 层级关系
```
Scene [Unity Scene]
├── Core Objects [GameObject] (摄像机、光照等)
├── UI Canvas [GameObject]
│   ├── UI Containers [GameObject] (各种UI根容器)
│   │   ├── UI Elements [GameObject] (具体UI元素)
│   │   └── Interactive Objects [GameObject] (交互对象)
│   └── Management Objects [GameObject] (管理器对象)
└── System Objects [GameObject] (系统级对象)
```

#### 组件附加关系
```
GameObject
├── Transform/RectTransform [Unity Component] (必需)
├── Unity Components [Unity Component] (Unity内置)
├── Script Components [Script Component] (自定义脚本)
└── Features [功能] (脚本提供的功能)
```

### 📋 实际使用指南

#### 创建 GameObject 的步骤
1. 在 Hierarchy 中右键创建 GameObject
2. 重命名为对应的名称 (如 "Mobile UI Root")
3. 添加必要的 Unity Component (如 RectTransform)
4. 添加自定义 Script Component (如 MobileUIRoot.cs)
5. 配置组件参数和功能选项

#### 组件配置的原则
1. **Unity Component** - 提供基础功能，通常自动添加
2. **Script Component** - 提供自定义逻辑，需要手动添加和配置
3. **功能特性** - 通过脚本参数控制，在 Inspector 中配置

#### 调试和维护
1. **GameObject** - 在 Hierarchy 中可见，可以启用/禁用
2. **Unity Component** - 在 Inspector 中显示，有标准界面
3. **Script Component** - 在 Inspector 中显示自定义界面
4. **功能特性** - 通过脚本日志和调试面板监控

---

**文档版本**: v2.0 (详细标识版)
**最后更新**: 2025-08-22
**适用版本**: Unity 6000.1.1f1+ (Unity 6) / Goose Duck Kill 3
