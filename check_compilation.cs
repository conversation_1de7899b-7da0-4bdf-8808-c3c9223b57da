// 快速编译检查脚本
using UnityEngine;
using CustomNetworking.Debug;
using CustomNetworking.Tests;

public class QuickCompilationCheck
{
    public void TestCompilation()
    {
        // 测试NetworkDebugStats结构体使用
        var stats = new NetworkDebugStats();
        Debug.Log($"Stats FPS: {stats.CurrentFPS}");
        
        // 测试NetworkDebugManager访问
        var manager = NetworkDebugManager.Instance;
        var managerStats = manager.GetDebugStats();
        
        // 验证结构体不与null比较
        bool isValid = managerStats.CurrentFPS >= 0f;
        Debug.Log($"Stats valid: {isValid}");
        
        // 测试测试类存在
        var playModeTestType = typeof(NetworkDebugManagerPlayModeTests);
        var advancedTestType = typeof(NetworkDebugManagerAdvancedPlayModeTests);
        var runnerType = typeof(PlayModeTestRunner);
        
        Debug.Log($"PlayMode测试类: {playModeTestType.Name}");
        Debug.Log($"高级测试类: {advancedTestType.Name}");
        Debug.Log($"测试运行器: {runnerType.Name}");
        
        Debug.Log("编译检查完成 - 所有类型都可访问");
    }
}
