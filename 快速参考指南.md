# Goose Duck Kill 3 快速参考指南

## 🚀 快速开始

### 1. 项目启动
```bash
# 克隆项目
git clone <repository-url>
cd goose-duck-kill3

# 使用Unity 6000.1.1f1+ (Unity 6)打开项目
# 等待包管理器下载依赖
```

### 2. 核心组件快速访问

#### GameManager - 游戏管理器
```csharp
// 获取实例
var gameManager = GameManager.Instance;

// 开始游戏
gameManager.StartGame();

// 监听事件
gameManager.OnGameStarted += () => Debug.Log("游戏开始");
```

#### NetworkRunner - 网络运行器
```csharp
// 启动网络
var args = new StartGameArgs
{
    GameMode = NetworkRunner.GameMode.Host,
    SessionName = "MyRoom",
    MaxPlayers = 10
};
await runner.StartGame(args);
```

#### PlayerController - 玩家控制器
```csharp
// 改变玩家状态
playerController.ChangeState(PlayerState.Dead);

// 监听状态变化
playerController.OnPlayerStateChanged += HandleStateChange;
```

## 📋 常用代码模板

### 创建NetworkBehaviour
```csharp
using CustomNetworking.Core;

public class MyNetworkBehaviour : NetworkBehaviour
{
    [Networked] public int MyProperty { get; set; }

    public override void Spawned()
    {
        base.Spawned();
        // 初始化逻辑
    }

    public override void FixedUpdateNetwork()
    {
        if (HasStateAuthority)
        {
            // 服务器逻辑
        }
    }

    [Rpc(RpcSources.All, RpcTargets.All)]
    public void MyRpc(string message)
    {
        Debug.Log($"RPC: {message}");
    }
}
```

### 移动端UI绑定
```csharp
public class MobileController : MonoBehaviour
{
    [SerializeField] private VirtualJoystick joystick;
    [SerializeField] private TouchButton actionButton;

    private void Start()
    {
        actionButton.OnButtonPressed += HandleAction;
    }

    private void Update()
    {
        Vector2 input = joystick.InputVector;
        // 处理移动输入
    }
}
```

### 网络事件监听
```csharp
private void OnEnable()
{
    NetworkEvents.OnConnected += HandleConnected;
    NetworkEvents.OnPlayerJoined += HandlePlayerJoined;
}

private void OnDisable()
{
    NetworkEvents.OnConnected -= HandleConnected;
    NetworkEvents.OnPlayerJoined -= HandlePlayerJoined;
}
```

## 🔧 常用配置

### 移动端性能优化
```csharp
// 自动性能适配
var optimizer = MobilePerformanceOptimizer.Instance;
optimizer.EnableAutoOptimization(true);

// 手动设置性能配置
var profile = new PerformanceProfile
{
    targetFrameRate = 30,
    qualityLevel = 1,
    renderScale = 0.8f
};
optimizer.ApplyProfile(profile);
```

### 网络调试
```csharp
// 启用调试
NetworkDebugManager.Instance.EnableDebugLogging(true);
NetworkDebugManager.Instance.ShowNetworkStats(true);
```

## 📁 重要文件位置

### 核心脚本
- `Assets/Scripts/Core/GameManager.cs` - 游戏管理器
- `Assets/Scripts/CustomNetworking/Core/NetworkRunner.cs` - 网络运行器
- `Assets/Scripts/Player/PlayerController.cs` - 玩家控制器
- `Assets/Scripts/Network/NetworkManager.cs` - 网络管理器

### 配置文件
- `Packages/manifest.json` - Unity包依赖
- `Directory.Build.props` - 项目构建设置
- `omnisharp.json` - OmniSharp配置

### 程序集定义
- `Assets/Scripts/Core/GooseDuckKill.Core.asmdef`
- `Assets/Scripts/CustomNetworking/CustomNetworking.asmdef`
- `Assets/Scripts/Player/GooseDuckKill.Player.asmdef`

## 🐛 常见问题解决

### 网络连接失败
```csharp
// 检查网络状态并重连
if (NetworkRunner.State == NetworkRunner.States.Failed)
{
    await NetworkRunner.Shutdown();
    await NetworkRunner.StartGame(lastStartArgs);
}
```

### 编译错误
```csharp
// 使用编辑器工具修复
// 菜单: Tools -> Fix Burst Compilation Issues
BurstCompilationFix.FixBurstCompilationIssues();
```

### 移动端性能问题
```csharp
// 动态调整质量
if (Application.targetFrameRate > 0)
{
    float fps = 1.0f / Time.deltaTime;
    if (fps < Application.targetFrameRate * 0.8f)
    {
        QualitySettings.DecreaseLevel();
    }
}
```

## 🧪 测试快速运行

### EditMode测试
```bash
# Unity Test Runner窗口
# Window -> General -> Test Runner
# 选择EditMode标签页，点击Run All
```

### PlayMode测试
```bash
# Unity Test Runner窗口
# 选择PlayMode标签页，点击Run All
# 注意：需要进入Play模式运行
```

### 命令行测试
```bash
# 从命令行运行测试
Unity.exe -batchmode -runTests -testPlatform EditMode -testResults results.xml
```

## 📱 移动端快速配置

### Android设置
1. File -> Build Settings -> Android
2. Player Settings -> XR Settings -> Initialize XR on Startup (取消勾选)
3. Player Settings -> Configuration -> Scripting Backend -> IL2CPP
4. Player Settings -> Configuration -> Target Architectures -> ARM64

### iOS设置
1. File -> Build Settings -> iOS
2. Player Settings -> Configuration -> Scripting Backend -> IL2CPP
3. Player Settings -> Configuration -> Target Device Family -> iPhone & iPad

## 🔗 快速链接

### 项目文档
- [完整项目文档](./项目结构与代码绑定使用文档.md)
- [README](./README.md)
- [迁移文档](./MIGRATION_DOCUMENTATION.md)

### Unity官方文档
- [Unity Netcode](https://docs.unity3d.com/Packages/com.unity.netcode.gameobjects@latest)
- [Input System](https://docs.unity3d.com/Packages/com.unity.inputsystem@latest)
- [URP](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@latest)

## 🎯 开发检查清单

### 新功能开发
- [ ] 创建对应的NetworkBehaviour
- [ ] 添加必要的网络属性同步
- [ ] 实现RPC方法
- [ ] 添加单元测试
- [ ] 测试移动端兼容性

### 发布前检查
- [ ] 所有测试通过
- [ ] 网络连接稳定
- [ ] 移动端性能优化
- [ ] 构建配置正确
- [ ] 版本号更新

## 💡 开发技巧

### 性能优化
- 使用对象池管理频繁创建/销毁的对象
- 合理设置网络同步频率
- 移动端启用性能优化配置

### 调试技巧
- 使用NetworkDebugManager查看网络状态
- 利用Unity Profiler分析性能
- 查看Console日志定位问题

### 代码组织
- 遵循程序集依赖关系
- 使用事件系统解耦组件
- 将平台特定代码放在对应程序集

---

**快速参考版本**: v1.0
**最后更新**: 2025-08-22
**对应项目版本**: Unity 6000.1.1f1+ (Unity 6)
