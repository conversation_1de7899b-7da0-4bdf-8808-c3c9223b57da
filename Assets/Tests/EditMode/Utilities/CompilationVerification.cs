using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using CustomNetworking.Debug;
// NetworkDebugManagerTests已移动到PlayMode，EditMode测试不再引用
using GooseDuckKill.Tests.EditMode.Integration;

namespace GooseDuckKill.Tests.EditMode.Utilities
{
    /// <summary>
    /// 编译验证测试 - 验证代码能正确编译和基本类型访问
    /// 注意：此类只包含不依赖Unity运行时的纯编译验证测试
    /// </summary>
    public class CompilationVerification
    {
        [Test]
        public void CompilationTest_NetworkDebugStats_StructUsage()
        {
            // 测试NetworkDebugStats结构体的正确使用（不依赖单例）
            var stats = new NetworkDebugStats();

            // 验证结构体字段访问（不与null比较）
            Assert.GreaterOrEqual(stats.CurrentFPS, 0f);
            Assert.GreaterOrEqual(stats.TotalLogEntries, 0);
            Assert.IsNull(stats.ConnectionState); // string字段可以为null

            // 验证结构体可以正常赋值
            stats.CurrentFPS = 60f;
            stats.TotalLogEntries = 10;
            Assert.AreEqual(60f, stats.CurrentFPS);
            Assert.AreEqual(10, stats.TotalLogEntries);
        }

        [Test]
        public void CompilationTest_ReflectionAccess_ShouldWork()
        {
            // 验证反射访问私有成员的代码能正确编译（不依赖单例实例）
            // 测试访问私有字段
            var instanceField = typeof(NetworkDebugManager).GetField("_instance",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            Assert.IsNotNull(instanceField, "应该能通过反射访问私有静态字段");

            // 测试访问私有方法（使用实际存在的私有方法）
            var initMethod = typeof(NetworkDebugManager).GetMethod("InitializeDebugManager",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            Assert.IsNotNull(initMethod, "应该能通过反射访问私有实例方法");

            // 测试访问公共方法
            var publicToggleMethod = typeof(NetworkDebugManager).GetMethod("ToggleDebugUI",
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            Assert.IsNotNull(publicToggleMethod, "应该能通过反射访问公共实例方法");

            // 验证类型存在
            Assert.IsNotNull(typeof(NetworkDebugManager));
        }

        [Test]
        public void CompilationTest_AllTestClassesExist_ShouldCompile()
        {
            // 验证重组后的EditMode测试类都能正确编译
            Assert.IsNotNull(typeof(NetworkDebugManagerIntegrationTests), "网络调试管理器集成测试类应该存在");

            // 验证测试类有正确的命名空间
            Assert.AreEqual("GooseDuckKill.Tests.EditMode.Integration", typeof(NetworkDebugManagerIntegrationTests).Namespace);

            // NetworkDebugManagerTests已移动到PlayMode程序集中
            // PlayModeTestRunner在PlayMode程序集中，EditMode测试无法直接引用
            // 这是正确的程序集分离设计
        }

        [Test]
        public void CompilationTest_TypesExist()
        {
            // 验证关键类型都存在且可访问
            Assert.IsNotNull(typeof(NetworkDebugManager));
            Assert.IsNotNull(typeof(NetworkDebugStats));
            Assert.IsNotNull(typeof(CompilationVerification));
        }

        // 注意：依赖NetworkDebugManager.Instance的UnityTest已移动到PlayMode程序集
        // EditMode测试不应该使用DontDestroyOnLoad相关的功能
    }
}
