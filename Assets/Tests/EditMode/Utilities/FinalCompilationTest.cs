using System.Collections;
using System.Linq;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using CustomNetworking.Debug;
// NetworkDebugManagerTests已移动到PlayMode，EditMode测试不再引用
using GooseDuckKill.Tests.EditMode.Integration;

namespace GooseDuckKill.Tests.EditMode.Utilities
{
    /// <summary>
    /// 最终编译测试 - 验证所有PlayMode测试修复完成
    /// </summary>
    public class FinalCompilationTest
    {
        [Test]
        public void FinalTest_NetworkDebugStats_StructUsage()
        {
            // 验证NetworkDebugStats作为结构体的正确使用
            var stats = new NetworkDebugStats();

            // 结构体字段访问
            Assert.GreaterOrEqual(stats.CurrentFPS, 0f);
            Assert.GreaterOrEqual(stats.TotalLogEntries, 0);

            // 验证不会与null比较（编译时检查）
            bool isValidFPS = stats.CurrentFPS >= 0f || stats.CurrentFPS < 0f;
            Assert.IsTrue(isValidFPS);
        }

        [Test]
        public void FinalTest_NetworkDebugStats_StructHandling()
        {
            // 验证NetworkDebugStats结构体的正确处理（不依赖单例）
            var stats = new NetworkDebugStats();

            // 正确的结构体验证方式
            Assert.IsTrue(stats.CurrentFPS >= 0f || stats.CurrentFPS < 0f);
            Assert.IsTrue(stats.TotalLogEntries >= 0);

            // 验证结构体赋值
            stats.CurrentFPS = 30f;
            stats.TotalLogEntries = 5;
            Assert.AreEqual(30f, stats.CurrentFPS);
            Assert.AreEqual(5, stats.TotalLogEntries);
        }

        // 注意：依赖NetworkDebugManager.Instance的UnityTest已移动到PlayMode程序集
        // EditMode测试不应该使用DontDestroyOnLoad相关的功能

        [Test]
        public void FinalTest_TestClasses_Exist()
        {
            // 验证重组后的EditMode测试类都存在且可访问
            Assert.IsNotNull(typeof(NetworkDebugManagerIntegrationTests));
            Assert.IsNotNull(typeof(CompilationVerification));
            // NetworkDebugManagerTests已移动到PlayMode程序集中
            // PlayModeTestRunner在PlayMode程序集中，EditMode测试无法直接引用
        }

        [Test]
        public void FinalTest_Reflection_Access()
        {
            // 验证反射访问代码正确（不依赖单例实例）
            var instanceField = typeof(NetworkDebugManager).GetField("_instance",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            Assert.IsNotNull(instanceField);

            // ToggleDebugUI是public方法，使用默认查找（不指定绑定标志）
            var toggleMethod = typeof(NetworkDebugManager).GetMethod("ToggleDebugUI");
            Assert.IsNotNull(toggleMethod, "ToggleDebugUI method not found");

            // 验证类型存在
            Assert.IsNotNull(typeof(NetworkDebugManager));
        }

        // 注意：UnityTest方法已移动到PlayMode程序集，因为它们需要Unity运行时环境
    }
}
