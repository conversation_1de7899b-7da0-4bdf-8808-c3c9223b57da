using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using CustomNetworking.Debug;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;

namespace GooseDuckKill.Tests.Shared.Helpers
{
    /// <summary>
    /// 测试辅助工具类 - 提供测试中常用的辅助方法
    /// </summary>
    public static class TestUtilities
    {
        #region 反射辅助方法

        /// <summary>
        /// 获取私有字段值
        /// </summary>
        public static T GetPrivateField<T>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName,
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (field == null)
                throw new ArgumentException($"Field '{fieldName}' not found in type '{obj.GetType().Name}'");

            return (T)field.GetValue(obj);
        }

        /// <summary>
        /// 设置私有字段值
        /// </summary>
        public static void SetPrivateField(object obj, string fieldName, object value)
        {
            var field = obj.GetType().GetField(fieldName,
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (field == null)
                throw new ArgumentException($"Field '{fieldName}' not found in type '{obj.GetType().Name}'");

            field.SetValue(obj, value);
        }

        /// <summary>
        /// 获取静态私有字段值
        /// </summary>
        public static T GetPrivateStaticField<T>(Type type, string fieldName)
        {
            var field = type.GetField(fieldName,
                BindingFlags.NonPublic | BindingFlags.Static);

            if (field == null)
                throw new ArgumentException($"Static field '{fieldName}' not found in type '{type.Name}'");

            return (T)field.GetValue(null);
        }

        /// <summary>
        /// 设置静态私有字段值
        /// </summary>
        public static void SetPrivateStaticField(Type type, string fieldName, object value)
        {
            var field = type.GetField(fieldName,
                BindingFlags.NonPublic | BindingFlags.Static);

            if (field == null)
                throw new ArgumentException($"Static field '{fieldName}' not found in type '{type.Name}'");

            field.SetValue(null, value);
        }

        /// <summary>
        /// 调用私有方法
        /// </summary>
        public static T InvokePrivateMethod<T>(object obj, string methodName, params object[] parameters)
        {
            var method = obj.GetType().GetMethod(methodName,
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (method == null)
                throw new ArgumentException($"Method '{methodName}' not found in type '{obj.GetType().Name}'");

            return (T)method.Invoke(obj, parameters);
        }

        #endregion

        #region NetworkDebugManager 辅助方法

        /// <summary>
        /// 重置NetworkDebugManager单例
        /// </summary>
        public static void ResetNetworkDebugManagerSingleton()
        {
            SetPrivateStaticField(typeof(NetworkDebugManager), "_instance", null);
        }

        /// <summary>
        /// 获取调试UI状态
        /// </summary>
        public static bool GetDebugUIState(NetworkDebugManager debugManager)
        {
            return GetPrivateField<bool>(debugManager, "_showDebugUI");
        }

        /// <summary>
        /// 获取日志条目数量
        /// </summary>
        public static int GetLogEntryCount(NetworkDebugManager debugManager)
        {
            var logEntries = GetPrivateField<IList>(debugManager, "_logEntries");
            return logEntries?.Count ?? 0;
        }

        /// <summary>
        /// 获取日志条目列表
        /// </summary>
        public static List<DebugLogEntry> GetLogEntries(NetworkDebugManager debugManager)
        {
            var logEntries = GetPrivateField<List<DebugLogEntry>>(debugManager, "_logEntries");
            return new List<DebugLogEntry>(logEntries);
        }

        /// <summary>
        /// 获取最大日志条目数
        /// </summary>
        public static int GetMaxLogEntries(NetworkDebugManager debugManager)
        {
            return GetPrivateField<int>(debugManager, "maxLogEntries");
        }

        /// <summary>
        /// 获取当前FPS
        /// </summary>
        public static float GetCurrentFPS(NetworkDebugManager debugManager)
        {
            return GetPrivateField<float>(debugManager, "_currentFPS");
        }

        /// <summary>
        /// 设置当前FPS (用于测试)
        /// </summary>
        public static void SetCurrentFPS(NetworkDebugManager debugManager, float fps)
        {
            SetPrivateField(debugManager, "_currentFPS", fps);
        }

        #endregion

        #region 模拟数据生成

        /// <summary>
        /// 创建模拟网络诊断数据
        /// </summary>
        public static CustomNetworking.ErrorHandling.NetworkDiagnostics CreateMockNetworkDiagnostics(
            ConnectionState state = ConnectionState.Connected,
            NetworkQuality quality = NetworkQuality.Good,
            float latency = 50f,
            float packetLoss = 0.01f,
            float bandwidth = 25f,
            float stability = 0.95f)
        {
            return new CustomNetworking.ErrorHandling.NetworkDiagnostics
            {
                State = state,
                Quality = quality,
                Latency = latency,
                PacketLoss = packetLoss,
                BandwidthUsage = bandwidth,
                Stability = stability,
                LastUpdateTime = Time.time
            };
        }

        /// <summary>
        /// 创建模拟网络错误
        /// </summary>
        public static NetworkError CreateMockNetworkError(
            ErrorType type = ErrorType.ConnectionTimeout,
            string message = "Test error",
            ErrorSeverity severity = ErrorSeverity.Medium)
        {
            return new NetworkError
            {
                Type = type,
                Message = message,
                Timestamp = Time.time,
                Severity = severity
            };
        }

        /// <summary>
        /// 创建模拟调试日志条目
        /// </summary>
        public static DebugLogEntry CreateMockLogEntry(
            string message = "Test log",
            DebugLogType type = DebugLogType.Info)
        {
            return new DebugLogEntry
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.Now
            };
        }

        #endregion

        #region 测试环境设置

        /// <summary>
        /// 创建测试用的NetworkDebugManager
        /// </summary>
        public static NetworkDebugManager CreateTestNetworkDebugManager()
        {
            var gameObject = new GameObject("TestNetworkDebugManager");
            return gameObject.AddComponent<NetworkDebugManager>();
        }

        /// <summary>
        /// 清理测试GameObject
        /// </summary>
        public static void CleanupTestGameObject(GameObject gameObject)
        {
            if (gameObject != null)
            {
                UnityEngine.Object.DestroyImmediate(gameObject);
            }
        }

        /// <summary>
        /// 重置所有单例 (用于测试清理)
        /// </summary>
        public static void ResetAllSingletons()
        {
            // 重置NetworkDebugManager
            ResetNetworkDebugManagerSingleton();

            // 重置NetworkSynchronizationManager
            try
            {
                SetPrivateStaticField(typeof(CustomNetworking.Synchronization.NetworkSynchronizationManager), "_instance", null);
            }
            catch { /* 忽略错误 */ }

            // 重置NetworkErrorHandler
            try
            {
                SetPrivateStaticField(typeof(NetworkErrorHandler), "_instance", null);
            }
            catch { /* 忽略错误 */ }
        }

        #endregion

        #region 断言辅助方法

        /// <summary>
        /// 断言浮点数近似相等
        /// </summary>
        public static bool ApproximatelyEqual(float a, float b, float tolerance = 0.001f)
        {
            return Mathf.Abs(a - b) <= tolerance;
        }

        /// <summary>
        /// 断言集合包含指定元素
        /// </summary>
        public static bool CollectionContains<T>(IEnumerable<T> collection, T item)
        {
            foreach (var element in collection)
            {
                if (EqualityComparer<T>.Default.Equals(element, item))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 断言字符串包含指定子串 (忽略大小写)
        /// </summary>
        public static bool StringContainsIgnoreCase(string source, string substring)
        {
            return source?.IndexOf(substring, StringComparison.OrdinalIgnoreCase) >= 0;
        }

        #endregion

        #region 性能测试辅助

        /// <summary>
        /// 测量代码执行时间
        /// </summary>
        public static float MeasureExecutionTime(Action action)
        {
            var startTime = Time.realtimeSinceStartup;
            action?.Invoke();
            return Time.realtimeSinceStartup - startTime;
        }

        /// <summary>
        /// 测量协程执行时间
        /// </summary>
        public static IEnumerator MeasureCoroutineTime(IEnumerator coroutine, Action<float> onComplete)
        {
            var startTime = Time.realtimeSinceStartup;
            yield return coroutine;
            var duration = Time.realtimeSinceStartup - startTime;
            onComplete?.Invoke(duration);
        }

        /// <summary>
        /// 生成随机测试数据
        /// </summary>
        public static List<T> GenerateRandomTestData<T>(int count, Func<T> generator)
        {
            var data = new List<T>();
            for (int i = 0; i < count; i++)
            {
                data.Add(generator());
            }
            return data;
        }

        #endregion
    }
}
