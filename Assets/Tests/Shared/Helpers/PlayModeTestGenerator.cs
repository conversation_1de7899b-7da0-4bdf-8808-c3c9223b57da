using UnityEngine;
using System.IO;

namespace GooseDuckKill.Tests.Shared.Helpers
{
    /// <summary>
    /// PlayMode测试生成器 - 自动生成完整的测试脚本
    /// </summary>
    public class PlayModeTestGenerator : MonoBehaviour
    {
        [Header("测试生成配置")]
        [SerializeField] private string testClassName = "MyPlayModeTest";
        [SerializeField] private string testDescription = "自定义PlayMode测试";
        [SerializeField] private bool includeSetupTeardown = true;
        [SerializeField] private bool includeBasicTests = true;
        [SerializeField] private bool includePerformanceTests = true;
        [SerializeField] private bool includeExceptionTests = true;

        [Header("目标组件")]
        [SerializeField] private string targetComponentName = "NetworkDebugManager";
        [SerializeField] private string targetNamespace = "CustomNetworking.Debug";

        /// <summary>
        /// 生成完整的PlayMode测试脚本
        /// </summary>
        [ContextMenu("Generate PlayMode Test Script")]
        public void GeneratePlayModeTestScript()
        {
            string scriptContent = GenerateTestScriptContent();
            string fileName = $"{testClassName}.cs";
            string filePath = Path.Combine(Application.dataPath, "Scripts/CustomNetworking/Core/Tests", fileName);

            try
            {
                File.WriteAllText(filePath, scriptContent);
                UnityEngine.Debug.Log($"[TestGenerator] PlayMode测试脚本已生成: {filePath}");

                #if UNITY_EDITOR
                UnityEditor.AssetDatabase.Refresh();
                #endif
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[TestGenerator] 生成测试脚本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成测试脚本内容
        /// </summary>
        private string GenerateTestScriptContent()
        {
            var content = new System.Text.StringBuilder();

            // 添加using语句
            content.AppendLine("using System.Collections;");
            content.AppendLine("using NUnit.Framework;");
            content.AppendLine("using UnityEngine;");
            content.AppendLine("using UnityEngine.TestTools;");
            content.AppendLine($"using {targetNamespace};");
            content.AppendLine("using CustomNetworking.Core;");
            content.AppendLine();

            // 添加命名空间
            content.AppendLine("namespace CustomNetworking.Tests");
            content.AppendLine("{");

            // 添加类注释和声明
            content.AppendLine("    /// <summary>");
            content.AppendLine($"    /// {testDescription}");
            content.AppendLine("    /// 自动生成的PlayMode测试脚本");
            content.AppendLine("    /// </summary>");
            content.AppendLine($"    public class {testClassName}");
            content.AppendLine("    {");

            // 添加字段
            content.AppendLine("        private GameObject testGameObject;");
            content.AppendLine($"        private {targetComponentName} targetComponent;");
            content.AppendLine();

            // 添加SetUp和TearDown
            if (includeSetupTeardown)
            {
                content.AppendLine(GenerateSetupTeardownMethods());
            }

            // 添加基础测试
            if (includeBasicTests)
            {
                content.AppendLine(GenerateBasicTests());
            }

            // 添加性能测试
            if (includePerformanceTests)
            {
                content.AppendLine(GeneratePerformanceTests());
            }

            // 添加异常测试
            if (includeExceptionTests)
            {
                content.AppendLine(GenerateExceptionTests());
            }

            // 添加辅助方法
            content.AppendLine(GenerateHelperMethods());

            // 结束类和命名空间
            content.AppendLine("    }");
            content.AppendLine("}");

            return content.ToString();
        }

        private string GenerateSetupTeardownMethods()
        {
            return @"        [SetUp]
        public void SetUp()
        {
            // 创建测试环境
            testGameObject = new GameObject(""TestObject"");
            targetComponent = testGameObject.AddComponent<" + targetComponentName + @">();

            UnityEngine.Debug.Log(""[" + testClassName + @"] 测试环境已设置"");
        }

        [TearDown]
        public void TearDown()
        {
            // 清理测试环境
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }

            ResetSingleton();
            UnityEngine.Debug.Log(""[" + testClassName + @"] 测试环境已清理"");
        }

";
        }

        private string GenerateBasicTests()
        {
            return @"        #region 基础功能测试

        [UnityTest]
        public IEnumerator BasicTest_ComponentInitialization_ShouldWork()
        {
            // Arrange
            var instance = " + targetComponentName + @".Instance;

            // Act
            yield return null;

            // Assert
            Assert.IsNotNull(instance, ""组件实例应该被创建"");
            Assert.IsTrue(instance.gameObject.activeInHierarchy, ""GameObject应该是激活状态"");

            UnityEngine.Debug.Log(""[" + testClassName + @"] 组件初始化测试通过"");
        }

        [UnityTest]
        public IEnumerator BasicTest_FunctionalityUpdate_ShouldWork()
        {
            // Arrange
            var instance = " + targetComponentName + @".Instance;
            yield return null;

            // Act
            yield return new WaitForSeconds(1.0f);

            // Assert
            Assert.IsNotNull(instance, ""更新后实例应该仍然存在"");

            UnityEngine.Debug.Log(""[" + testClassName + @"] 功能更新测试通过"");
        }

        #endregion

";
        }

        private string GeneratePerformanceTests()
        {
            return @"        #region 性能测试

        [UnityTest]
        public IEnumerator PerformanceTest_UnderLoad_ShouldMaintain()
        {
            // Arrange
            var instance = " + targetComponentName + @".Instance;
            yield return null;

            // Act - 创建负载
            for (int i = 0; i < 50; i++)
            {
                UnityEngine.Debug.Log($""性能测试负载 {i}"");
                if (i % 10 == 0)
                {
                    yield return null;
                }
            }

            yield return new WaitForSeconds(1f);

            // Assert
            Assert.IsNotNull(instance, ""负载测试后实例应该仍然存在"");

            UnityEngine.Debug.Log(""[" + testClassName + @"] 性能测试通过"");
        }

        #endregion

";
        }

        private string GenerateExceptionTests()
        {
            return @"        #region 异常处理测试

        [UnityTest]
        public IEnumerator ExceptionTest_ErrorRecovery_ShouldWork()
        {
            // Arrange
            var instance = " + targetComponentName + @".Instance;
            yield return null;

            bool exceptionHandled = false;

            // Act
            try
            {
                UnityEngine.Debug.LogError(""模拟测试异常"");
                exceptionHandled = true;
            }
            catch (System.Exception)
            {
                exceptionHandled = false;
            }

            yield return new WaitForSeconds(0.5f);

            // Assert
            Assert.IsNotNull(instance, ""异常后实例应该仍然存在"");

            UnityEngine.Debug.Log($""[" + testClassName + @"] 异常处理测试完成"");
        }

        #endregion

";
        }

        private string GenerateHelperMethods()
        {
            return @"        #region 辅助方法

        /// <summary>
        /// 重置单例
        /// </summary>
        private void ResetSingleton()
        {
            var instanceField = typeof(" + targetComponentName + @").GetField(""_instance"",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            instanceField?.SetValue(null, null);
        }

        /// <summary>
        /// 等待条件满足
        /// </summary>
        private IEnumerator WaitForCondition(System.Func<bool> condition, float timeout = 5f)
        {
            float elapsed = 0f;
            while (!condition() && elapsed < timeout)
            {
                elapsed += Time.deltaTime;
                yield return null;
            }
        }

        #endregion
";
        }

        /// <summary>
        /// 生成简单的PlayMode测试模板
        /// </summary>
        [ContextMenu("Generate Simple PlayMode Template")]
        public void GenerateSimpleTemplate()
        {
            string simpleTemplate = @"using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using CustomNetworking.Debug;

namespace CustomNetworking.Tests
{
    public class SimplePlayModeTest
    {
        [UnityTest]
        public IEnumerator SimpleTest_BasicFunctionality()
        {
            // Arrange - 准备测试数据
            var manager = NetworkDebugManager.Instance;

            // Act - 执行测试操作
            yield return null; // 等待一帧

            // Assert - 验证结果
            Assert.IsNotNull(manager, ""NetworkDebugManager应该存在"");

            UnityEngine.Debug.Log(""简单PlayMode测试完成"");
        }

        [UnityTest]
        public IEnumerator SimpleTest_TimeBasedOperation()
        {
            // 时间相关的测试
            var startTime = Time.time;

            yield return new WaitForSeconds(1f);

            var elapsed = Time.time - startTime;
            Assert.GreaterOrEqual(elapsed, 0.9f, ""应该等待了至少0.9秒"");

            UnityEngine.Debug.Log($""时间测试完成，耗时: {elapsed}秒"");
        }
    }
}";

            string fileName = "SimplePlayModeTest.cs";
            string filePath = Path.Combine(Application.dataPath, "Scripts/CustomNetworking/Core/Tests", fileName);

            try
            {
                File.WriteAllText(filePath, simpleTemplate);
                UnityEngine.Debug.Log($"[TestGenerator] 简单PlayMode测试模板已生成: {filePath}");

                #if UNITY_EDITOR
                UnityEditor.AssetDatabase.Refresh();
                #endif
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"[TestGenerator] 生成简单模板失败: {ex.Message}");
            }
        }
    }
}
