{"name": "GooseDuckKill.Tests.PlayMode", "rootNamespace": "GooseDuckKill.Tests", "references": ["GooseDuckKill.Tests.Shared", "CustomNetworking", "GooseDuckKill.Core", "GooseDuckKill.Network"], "optionalUnityReferences": ["TestAssemblies"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}