using System.Collections;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using CustomNetworking.Debug;
using CustomNetworking.Core;

namespace GooseDuckKill.Tests.PlayMode.Functional.Network
{
    /// <summary>
    /// NetworkDebugManager 完整PlayMode测试
    /// 专门针对Unity Test Runner创建空脚本问题的完整解决方案
    /// </summary>
    public class NetworkDebugManagerCompletePlayModeTest
    {
        private GameObject testGameObject;
        private NetworkDebugManager debugManager;

        [SetUp]
        public void SetUp()
        {
            // 创建测试环境
            testGameObject = new GameObject("NetworkDebugManagerTest");
            debugManager = testGameObject.AddComponent<NetworkDebugManager>();

            UnityEngine.Debug.Log("[NetworkDebugManagerTest] 测试环境已设置");
        }

        [TearDown]
        public void TearDown()
        {
            // 清理测试环境
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }

            // 重置单例
            ResetNetworkDebugManagerSingleton();

            UnityEngine.Debug.Log("[NetworkDebugManagerTest] 测试环境已清理");
        }

        #region 基础功能测试

        [UnityTest]
        public IEnumerator Test_NetworkDebugManager_Initialization()
        {
            // Arrange & Act
            var instance = NetworkDebugManager.Instance;
            yield return null; // 等待Awake和Start执行

            // Assert
            Assert.IsNotNull(instance, "NetworkDebugManager实例应该被创建");
            Assert.IsTrue(instance.gameObject.activeInHierarchy, "GameObject应该是激活状态");

            var stats = instance.GetDebugStats();
            Assert.GreaterOrEqual(stats.CurrentFPS, 0f, "FPS应该被初始化");
            Assert.GreaterOrEqual(stats.TotalLogEntries, 0, "日志条目数应该被初始化");

            UnityEngine.Debug.Log("[Test] NetworkDebugManager初始化测试通过");
        }

        [UnityTest]
        public IEnumerator Test_SingletonPattern_ShouldWork()
        {
            // Arrange
            var firstInstance = NetworkDebugManager.Instance;
            yield return null;

            // Act - 尝试创建第二个实例
            var secondGameObject = new GameObject("SecondDebugManager");
            var secondComponent = secondGameObject.AddComponent<NetworkDebugManager>();
            yield return null;

            // Assert
            Assert.AreSame(firstInstance, NetworkDebugManager.Instance, "单例模式应该保持同一实例");

            // 清理
            Object.DestroyImmediate(secondGameObject);
            yield return null;

            UnityEngine.Debug.Log("[Test] 单例模式测试通过");
        }

        [UnityTest]
        public IEnumerator Test_FPS_Calculation_ShouldUpdate()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            var initialStats = instance.GetDebugStats();
            var initialFPS = initialStats.CurrentFPS;

            // Act - 等待FPS计算更新
            yield return new WaitForSeconds(1.5f);

            var updatedStats = instance.GetDebugStats();

            // Assert
            Assert.GreaterOrEqual(updatedStats.CurrentFPS, 0f, "FPS应该被正确计算");
            Assert.LessOrEqual(updatedStats.CurrentFPS, 10000f, "FPS应该在合理范围内（允许编辑器高帧率）");

            // 验证FPS值是有意义的（不是NaN或无穷大）
            Assert.IsFalse(float.IsNaN(updatedStats.CurrentFPS), "FPS不应该是NaN");
            Assert.IsFalse(float.IsInfinity(updatedStats.CurrentFPS), "FPS不应该是无穷大");

            UnityEngine.Debug.Log($"[Test] FPS计算测试通过: {initialFPS} -> {updatedStats.CurrentFPS}");
        }

        #endregion

        #region UI交互测试

        [UnityTest]
        public IEnumerator Test_DebugUI_Toggle_ShouldWork()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            // Act - 通过反射调用ToggleDebugUI方法（ToggleDebugUI是public方法）
            var toggleMethod = typeof(NetworkDebugManager).GetMethod("ToggleDebugUI",
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            Assert.IsNotNull(toggleMethod, "ToggleDebugUI方法应该存在");

            // 第一次切换
            toggleMethod.Invoke(instance, null);
            yield return null;

            // 第二次切换
            toggleMethod.Invoke(instance, null);
            yield return null;

            // Assert
            Assert.IsNotNull(instance, "UI切换后实例应该仍然存在");

            UnityEngine.Debug.Log("[Test] UI切换测试通过");
        }

        [UnityTest]
        public IEnumerator Test_KeyInput_Simulation()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            // Act - 模拟F1按键（通过反射调用HandleInput）
            var handleInputMethod = typeof(NetworkDebugManager).GetMethod("HandleInput",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (handleInputMethod != null)
            {
                // 模拟按键处理
                handleInputMethod.Invoke(instance, null);
                yield return null;
            }

            // Assert
            Assert.IsNotNull(instance, "按键处理后实例应该仍然存在");

            UnityEngine.Debug.Log("[Test] 按键输入模拟测试通过");
        }

        #endregion

        #region 性能监控测试

        [UnityTest]
        public IEnumerator Test_PerformanceMonitoring_ShouldTrackMetrics()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            // Act - 运行一段时间让性能监控收集数据
            float testDuration = 2f;
            float elapsed = 0f;

            while (elapsed < testDuration)
            {
                elapsed += Time.deltaTime;
                yield return null;
            }

            var stats = instance.GetDebugStats();

            // Assert
            Assert.GreaterOrEqual(stats.CurrentFPS, 0f, "FPS监控应该工作");
            Assert.GreaterOrEqual(stats.TotalLogEntries, 0, "日志条目计数应该工作");

            UnityEngine.Debug.Log("[Test] 性能监控测试通过");
        }

        [UnityTest]
        public IEnumerator Test_StressTest_HighFrequencyLogging()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            var initialStats = instance.GetDebugStats();

            // Act - 高频率日志测试
            for (int i = 0; i < 50; i++)
            {
                UnityEngine.Debug.Log($"压力测试日志 {i}");
                if (i % 10 == 0)
                {
                    yield return null; // 每10条日志等待一帧
                }
            }

            yield return new WaitForSeconds(1f);

            var finalStats = instance.GetDebugStats();

            // Assert
            Assert.GreaterOrEqual(finalStats.CurrentFPS, 0f, "高频日志后FPS应该仍然有效");
            Assert.IsNotNull(instance, "压力测试后实例应该仍然存在");

            UnityEngine.Debug.Log("[Test] 压力测试通过");
        }

        #endregion

        #region 网络集成测试

        [UnityTest]
        public IEnumerator Test_NetworkIntegration_WithoutNetworkManager()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            // Act - 在没有网络管理器的情况下获取统计
            var stats = instance.GetDebugStats();

            // Assert - 应该优雅处理缺失的网络组件
            Assert.GreaterOrEqual(stats.CurrentFPS, 0f, "即使没有网络管理器FPS也应该有效");
            Assert.AreEqual(0f, stats.CurrentLatency, "没有网络管理器时延迟应该为0");
            Assert.AreEqual(0f, stats.CurrentBandwidth, "没有网络管理器时带宽应该为0");

            UnityEngine.Debug.Log("[Test] 网络集成测试通过");
        }

        #endregion

        #region 异常处理测试

        [UnityTest]
        public IEnumerator Test_ExceptionHandling_ShouldRecover()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            yield return null;

            bool exceptionOccurred = false;

            // Act - 触发一些可能的异常情况
            try
            {
                // 使用LogAssert.Expect来处理预期的错误和警告日志
                LogAssert.Expect(LogType.Error, "模拟网络异常");
                LogAssert.Expect(LogType.Warning, "模拟连接警告");

                UnityEngine.Debug.LogError("模拟网络异常");
                UnityEngine.Debug.LogWarning("模拟连接警告");
            }
            catch (System.Exception)
            {
                exceptionOccurred = true;
            }

            yield return new WaitForSeconds(0.5f);

            // Assert
            var stats = instance.GetDebugStats();
            Assert.GreaterOrEqual(stats.CurrentFPS, 0f, "异常后系统应该仍然正常工作");
            Assert.IsNotNull(instance, "异常后实例应该仍然存在");

            UnityEngine.Debug.Log($"[Test] 异常处理测试完成，异常发生: {exceptionOccurred}");
        }

        #endregion

        #region 生命周期测试

        [UnityTest]
        public IEnumerator Test_ComponentLifecycle_DestroyAndRecreate()
        {
            // Arrange
            var instance = NetworkDebugManager.Instance;
            var gameObject = instance.gameObject;
            yield return null;

            // Act - 销毁组件
            Object.Destroy(gameObject);
            yield return null; // 等待销毁完成

            // 创建新实例
            var newInstance = NetworkDebugManager.Instance;
            yield return null;

            // Assert
            Assert.IsNotNull(newInstance, "销毁后应该能创建新实例");
            Assert.AreNotSame(instance, newInstance, "新实例应该与旧实例不同");

            UnityEngine.Debug.Log("[Test] 组件生命周期测试通过");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 重置NetworkDebugManager单例
        /// </summary>
        private void ResetNetworkDebugManagerSingleton()
        {
            var instanceField = typeof(NetworkDebugManager).GetField("_instance",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            instanceField?.SetValue(null, null);
        }

        /// <summary>
        /// 等待条件满足或超时
        /// </summary>
        private IEnumerator WaitForCondition(System.Func<bool> condition, float timeout = 5f)
        {
            float elapsed = 0f;
            while (!condition() && elapsed < timeout)
            {
                elapsed += Time.deltaTime;
                yield return null;
            }

            if (elapsed >= timeout)
            {
                UnityEngine.Debug.LogWarning($"[Test] 等待条件超时: {timeout}秒");
            }
        }

        #endregion
    }
}
