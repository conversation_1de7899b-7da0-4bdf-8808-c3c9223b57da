using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using CustomNetworking.Debug;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;

namespace GooseDuckKill.Tests.PlayMode.Functional.Network
{
    /// <summary>
    /// NetworkDebugManager 单元测试
    /// 测试网络调试管理器的核心功能
    /// </summary>
    public class NetworkDebugManagerTests
    {
        private GameObject testGameObject;
        private NetworkDebugManager debugManager;

        [SetUp]
        public void SetUp()
        {
            // 创建测试用的GameObject
            testGameObject = new GameObject("TestNetworkDebugManager");
            debugManager = testGameObject.AddComponent<NetworkDebugManager>();

            // {{ AURA-X: Add - 在测试环境中禁用控制台日志输出以避免Unity测试运行器报错. Approval: 寸止(ID:1703174400). }}
            // 通过反射禁用控制台日志输出
            var enableConsoleLoggingField = typeof(NetworkDebugManager).GetField("enableConsoleLogging",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            enableConsoleLoggingField.SetValue(debugManager, false);
        }

        [TearDown]
        public void TearDown()
        {
            // 清理测试对象
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }

            // 重置单例
            ResetSingleton();
        }

        #region 单例模式测试

        [Test]
        public void Instance_ShouldReturnSameInstance()
        {
            // Arrange & Act
            var instance1 = NetworkDebugManager.Instance;
            var instance2 = NetworkDebugManager.Instance;

            // Assert
            Assert.IsNotNull(instance1);
            Assert.AreSame(instance1, instance2);
        }

        [Test]
        public void Instance_ShouldCreateInstanceIfNotExists()
        {
            // Arrange
            ResetSingleton();

            // Act
            var instance = NetworkDebugManager.Instance;

            // Assert
            Assert.IsNotNull(instance);
            Assert.IsTrue(instance.gameObject.name.Contains("NetworkDebugManager"));
        }

        [Test]
        public void Awake_ShouldDestroyDuplicateInstances()
        {
            // Arrange
            var firstInstance = NetworkDebugManager.Instance;
            var firstGameObject = firstInstance.gameObject;

            // Act - 创建第二个实例
            var secondGameObject = new GameObject("SecondDebugManager");
            var secondInstance = secondGameObject.AddComponent<NetworkDebugManager>();

            // {{ AURA-X: Modify - 验证单例模式正确工作：第一个实例保持不变，第二个GameObject被标记为销毁. Approval: 寸止(ID:1703174400). }}
            // Assert - 验证单例行为
            Assert.AreSame(firstInstance, NetworkDebugManager.Instance, "单例实例应该保持不变");
            Assert.AreSame(firstGameObject, NetworkDebugManager.Instance.gameObject, "单例GameObject应该保持不变");

            // 清理测试创建的对象
            if (secondGameObject != null)
            {
                Object.DestroyImmediate(secondGameObject);
            }
        }

        #endregion

        #region UI控制测试

        [Test]
        public void ToggleDebugUI_ShouldChangeVisibility()
        {
            // Arrange
            var initialState = GetDebugUIState();

            // Act
            debugManager.ToggleDebugUI();
            var newState = GetDebugUIState();

            // Assert
            Assert.AreNotEqual(initialState, newState);
        }

        [Test]
        public void ShowDebugUI_ShouldSetVisibilityTrue()
        {
            // Act
            debugManager.ShowDebugUI();

            // Assert
            Assert.IsTrue(GetDebugUIState());
        }

        [Test]
        public void HideDebugUI_ShouldSetVisibilityFalse()
        {
            // Act
            debugManager.HideDebugUI();

            // Assert
            Assert.IsFalse(GetDebugUIState());
        }

        #endregion

        #region 日志功能测试

        [Test]
        public void LogDebug_ShouldAddEntryToList()
        {
            // Arrange
            var initialCount = GetLogEntryCount();
            var testMessage = "Test log message";

            // Act
            debugManager.LogDebug(testMessage, DebugLogType.Info);

            // Assert
            var newCount = GetLogEntryCount();
            Assert.AreEqual(initialCount + 1, newCount);
        }

        [Test]
        public void LogDebug_WithDifferentTypes_ShouldAddCorrectEntries()
        {
            // Arrange
            var initialCount = GetLogEntryCount(); // 考虑初始化时的日志条目
            var infoMessage = "Info message";
            var warningMessage = "Warning message";
            var errorMessage = "Error message";

            // Act
            debugManager.LogDebug(infoMessage, DebugLogType.Info);
            debugManager.LogDebug(warningMessage, DebugLogType.Warning);
            debugManager.LogDebug(errorMessage, DebugLogType.Error);

            // Assert
            Assert.AreEqual(initialCount + 3, GetLogEntryCount());
        }

        [Test]
        public void ClearLogs_ShouldEmptyLogList()
        {
            // Arrange
            debugManager.LogDebug("Test message 1", DebugLogType.Info);
            debugManager.LogDebug("Test message 2", DebugLogType.Warning);
            Assert.Greater(GetLogEntryCount(), 0);

            // Act
            debugManager.ClearLogs();

            // Assert
            // 注意：ClearLogs会添加一条"日志已清除"的消息
            Assert.AreEqual(1, GetLogEntryCount());
        }

        [Test]
        public void LogDebug_ShouldLimitMaxEntries()
        {
            // Arrange
            var maxEntries = GetMaxLogEntries();

            // Act - 添加超过最大数量的日志
            for (int i = 0; i < maxEntries + 10; i++)
            {
                debugManager.LogDebug($"Test message {i}", DebugLogType.Info);
            }

            // Assert
            Assert.LessOrEqual(GetLogEntryCount(), maxEntries);
        }

        #endregion

        #region 性能监控测试

        [Test]
        public void GetDebugStats_ShouldReturnValidStats()
        {
            // Act
            var stats = debugManager.GetDebugStats();

            // Assert
            Assert.IsNotNull(stats);
            Assert.GreaterOrEqual(stats.CurrentFPS, 0);
            Assert.GreaterOrEqual(stats.TotalLogEntries, 0);
        }

        [UnityTest]
        public IEnumerator UpdatePerformanceMonitoring_ShouldCalculateFPS()
        {
            // Arrange
            var initialStats = debugManager.GetDebugStats();

            // Act - 等待几帧让性能监控更新
            yield return new WaitForSeconds(1f);

            var updatedStats = debugManager.GetDebugStats();

            // Assert
            // FPS应该被计算并更新
            Assert.GreaterOrEqual(updatedStats.CurrentFPS, 0);
        }

        #endregion

        #region 网络诊断测试

        [Test]
        public void GetDebugStats_WithoutNetworkManager_ShouldNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var stats = debugManager.GetDebugStats();
            });
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取调试UI的显示状态
        /// </summary>
        private bool GetDebugUIState()
        {
            var field = typeof(NetworkDebugManager).GetField("_showDebugUI",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (bool)field.GetValue(debugManager);
        }

        /// <summary>
        /// 获取日志条目数量
        /// </summary>
        private int GetLogEntryCount()
        {
            var field = typeof(NetworkDebugManager).GetField("_logEntries",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var logEntries = field.GetValue(debugManager) as System.Collections.IList;
            return logEntries?.Count ?? 0;
        }

        /// <summary>
        /// 获取最大日志条目数
        /// </summary>
        private int GetMaxLogEntries()
        {
            var field = typeof(NetworkDebugManager).GetField("maxLogEntries",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (int)field.GetValue(debugManager);
        }

        /// <summary>
        /// 重置单例实例
        /// </summary>
        private void ResetSingleton()
        {
            var field = typeof(NetworkDebugManager).GetField("_instance",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            field.SetValue(null, null);
        }

        #endregion
    }
}
