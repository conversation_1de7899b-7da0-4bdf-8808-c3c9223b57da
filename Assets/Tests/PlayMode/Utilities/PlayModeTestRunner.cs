using System.Collections;
using UnityEngine;
using CustomNetworking.Debug;

namespace GooseDuckKill.Tests.PlayMode.Utilities
{
    /// <summary>
    /// PlayMode测试运行器
    /// 提供在Play模式下运行测试的便捷方法
    /// </summary>
    public class PlayModeTestRunner : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool autoRunOnStart = false;
        [SerializeField] private bool enableDetailedLogging = true;
        [SerializeField] private float testTimeout = 30f;

        [Header("测试选择")]
        [SerializeField] private bool runBasicTests = true;
        [SerializeField] private bool runAdvancedTests = true;
        [SerializeField] private bool runStressTests = false;

        private int totalTests = 0;
        private int passedTests = 0;
        private int failedTests = 0;

        void Start()
        {
            if (autoRunOnStart)
            {
                StartCoroutine(RunAllPlayModeTests());
            }
        }

        /// <summary>
        /// 运行所有PlayMode测试
        /// </summary>
        [ContextMenu("Run All PlayMode Tests")]
        public void RunAllTests()
        {
            StartCoroutine(RunAllPlayModeTests());
        }

        /// <summary>
        /// 运行基础PlayMode测试
        /// </summary>
        [ContextMenu("Run Basic PlayMode Tests")]
        public void RunBasicTests()
        {
            StartCoroutine(RunBasicPlayModeTests());
        }

        /// <summary>
        /// 运行高级PlayMode测试
        /// </summary>
        [ContextMenu("Run Advanced PlayMode Tests")]
        public void RunAdvancedTests()
        {
            StartCoroutine(RunAdvancedPlayModeTests());
        }

        private IEnumerator RunAllPlayModeTests()
        {
            LogTest("=== 开始PlayMode测试套件 ===");

            totalTests = 0;
            passedTests = 0;
            failedTests = 0;

            if (runBasicTests)
            {
                yield return RunBasicPlayModeTests();
            }

            if (runAdvancedTests)
            {
                yield return RunAdvancedPlayModeTests();
            }

            DisplayFinalResults();
        }

        private IEnumerator RunBasicPlayModeTests()
        {
            LogTest("--- 运行基础PlayMode测试 ---");

            // 测试1: 初始化测试
            yield return RunTest("DebugManager初始化", TestDebugManagerInitialization());

            // 测试2: FPS计算测试
            yield return RunTest("FPS计算", TestFPSCalculation());

            // 测试3: UI切换测试
            yield return RunTest("UI切换", TestUIToggle());

            // 测试4: 性能监控测试
            yield return RunTest("性能监控", TestPerformanceMonitoring());

            // 测试5: 日志功能测试
            yield return RunTest("日志功能", TestLoggingFunctionality());

            LogTest("--- 基础PlayMode测试完成 ---");
        }

        private IEnumerator RunAdvancedPlayModeTests()
        {
            LogTest("--- 运行高级PlayMode测试 ---");

            // 测试1: 单例模式测试
            yield return RunTest("单例模式", TestSingletonPattern());

            // 测试2: 异常处理测试
            yield return RunTest("异常处理", TestExceptionHandling());

            // 测试3: 配置更改测试
            yield return RunTest("配置更改", TestConfigurationChanges());

            if (runStressTests)
            {
                // 测试4: 压力测试
                yield return RunTest("压力测试", TestStressHandling());

                // 测试5: 长期运行测试
                yield return RunTest("长期运行", TestLongRunning());
            }

            LogTest("--- 高级PlayMode测试完成 ---");
        }

        private IEnumerator RunTest(string testName, IEnumerator testCoroutine)
        {
            totalTests++;
            LogTest($"开始测试: {testName}");

            bool testPassed = true;
            string errorMessage = "";

            // 不能在try-catch中使用yield return，所以分开处理
            var testRunner = StartCoroutine(testCoroutine);
            yield return testRunner;

            // 检查协程是否成功完成
            if (testRunner == null)
            {
                testPassed = false;
                errorMessage = "测试协程执行失败";
            }

            if (testPassed)
            {
                passedTests++;
                LogTest($"✓ {testName} - 通过");
            }
            else
            {
                failedTests++;
                LogTest($"✗ {testName} - 失败: {errorMessage}");
            }
        }

        #region 具体测试实现

        private IEnumerator TestDebugManagerInitialization()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            if (instance == null)
                throw new System.Exception("NetworkDebugManager实例为null");

            if (!instance.gameObject.activeInHierarchy)
                throw new System.Exception("DebugManager GameObject未激活");

            var stats = instance.GetDebugStats();
            // NetworkDebugStats是结构体，不能为null，检查其有效性
            if (stats.CurrentFPS < 0 && stats.TotalLogEntries < 0)
                throw new System.Exception("调试统计未正确初始化");
        }

        private IEnumerator TestFPSCalculation()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            var initialStats = instance.GetDebugStats();
            yield return new WaitForSeconds(1.5f);

            var updatedStats = instance.GetDebugStats();

            if (updatedStats.CurrentFPS < 0)
                throw new System.Exception("FPS计算异常");
        }

        private IEnumerator TestUIToggle()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            var toggleMethod = typeof(NetworkDebugManager).GetMethod("ToggleDebugUI",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (toggleMethod == null)
                throw new System.Exception("找不到ToggleDebugUI方法");

            // 测试切换
            toggleMethod.Invoke(instance, null);
            yield return null;

            toggleMethod.Invoke(instance, null);
            yield return null;

            if (instance == null)
                throw new System.Exception("UI切换后实例丢失");
        }

        private IEnumerator TestPerformanceMonitoring()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            yield return new WaitForSeconds(2f);

            var stats = instance.GetDebugStats();

            if (stats.CurrentFPS < 0)
                throw new System.Exception("性能监控FPS异常");

            if (stats.TotalLogEntries < 0)
                throw new System.Exception("日志条目计数异常");
        }

        private IEnumerator TestLoggingFunctionality()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            var initialStats = instance.GetDebugStats();

            UnityEngine.Debug.Log("PlayMode测试日志");
            UnityEngine.Debug.LogWarning("PlayMode测试警告");

            yield return new WaitForSeconds(0.5f);

            var updatedStats = instance.GetDebugStats();

            // NetworkDebugStats是结构体，检查其有效性而不是null
            if (updatedStats.CurrentFPS < 0 && updatedStats.TotalLogEntries < 0)
                throw new System.Exception("日志后无法获取有效的统计信息");
        }

        private IEnumerator TestSingletonPattern()
        {
            var firstInstance = NetworkDebugManager.Instance;
            yield return null;

            var secondGameObject = new GameObject("TestSecondDebugManager");
            var secondComponent = secondGameObject.AddComponent<NetworkDebugManager>();
            yield return null;

            if (!ReferenceEquals(firstInstance, NetworkDebugManager.Instance))
                throw new System.Exception("单例模式失败");

            Object.DestroyImmediate(secondGameObject);
            yield return null;
        }

        private IEnumerator TestExceptionHandling()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            UnityEngine.Debug.LogError("测试异常处理");
            yield return new WaitForSeconds(0.5f);

            var stats = instance.GetDebugStats();
            // 检查统计信息的有效性而不是null
            if (stats.CurrentFPS < 0 && stats.TotalLogEntries < 0)
                throw new System.Exception("异常后无法获取有效的统计信息");
        }

        private IEnumerator TestConfigurationChanges()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            // 测试配置更改不会破坏系统
            yield return new WaitForSeconds(0.5f);

            var stats = instance.GetDebugStats();
            // 检查统计信息的有效性而不是null
            if (stats.CurrentFPS < 0 && stats.TotalLogEntries < 0)
                throw new System.Exception("配置更改后无法获取有效的统计信息");
        }

        private IEnumerator TestStressHandling()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            // 高频日志测试
            for (int i = 0; i < 50; i++)
            {
                UnityEngine.Debug.Log($"压力测试 {i}");
                if (i % 10 == 0) yield return null;
            }

            yield return new WaitForSeconds(1f);

            var stats = instance.GetDebugStats();
            // 检查统计信息的有效性而不是null
            if (stats.CurrentFPS < 0 && stats.TotalLogEntries < 0)
                throw new System.Exception("压力测试后无法获取有效的统计信息");
        }

        private IEnumerator TestLongRunning()
        {
            var instance = NetworkDebugManager.Instance;
            yield return null;

            float testDuration = 3f;
            float elapsed = 0f;

            while (elapsed < testDuration)
            {
                elapsed += Time.deltaTime;
                if (elapsed > testTimeout)
                    throw new System.Exception("长期运行测试超时");
                yield return null;
            }

            var stats = instance.GetDebugStats();
            // 检查统计信息的有效性而不是null
            if (stats.CurrentFPS < 0 && stats.TotalLogEntries < 0)
                throw new System.Exception("长期运行后无法获取有效的统计信息");
        }

        #endregion

        private void DisplayFinalResults()
        {
            LogTest("=== PlayMode测试结果 ===");
            LogTest($"总测试数: {totalTests}");
            LogTest($"通过: {passedTests}");
            LogTest($"失败: {failedTests}");
            LogTest($"成功率: {(totalTests > 0 ? (passedTests * 100f / totalTests) : 0):F1}%");
            LogTest("========================");
        }

        private void LogTest(string message)
        {
            if (enableDetailedLogging)
            {
                UnityEngine.Debug.Log($"[PlayModeTestRunner] {message}");
            }
        }
    }
}
