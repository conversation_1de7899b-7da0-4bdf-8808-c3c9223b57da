using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.ErrorHandling
{
    /// <summary>
    /// 网络连接监控器 - 实时监控网络连接状态和质量
    /// </summary>
    public class NetworkConnectionMonitor : MonoBehaviour
    {
        [Header("监控设置")]
        [SerializeField] private float monitorInterval = 1f;
        [SerializeField] private float heartbeatInterval = 5f;
        [SerializeField] private float connectionTimeout = 10f;
        [SerializeField] private int latencyHistorySize = 10;

        [Header("质量阈值")]
        [SerializeField] private float excellentLatency = 50f;
        [SerializeField] private float goodLatency = 100f;
        [SerializeField] private float fairLatency = 200f;
        [SerializeField] private float poorLatency = 500f;
        [SerializeField] private float maxPacketLoss = 0.05f; // 最大允许丢包率

        [Header("调试显示")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool logConnectionEvents = true;

        // 监控状态
        private NetworkDiagnostics _currentDiagnostics;
        private Queue<float> _latencyHistory = new Queue<float>();
        private Queue<float> _packetLossHistory = new Queue<float>();
        private float _lastHeartbeatTime;
        private float _lastMonitorTime;
        private bool _isMonitoring;

        // 连接统计
        private int _totalPacketsSent;
        private int _totalPacketsReceived;
        private int _totalPacketsLost;
        private float _totalBandwidthSent;
        private float _totalBandwidthReceived;

        // 事件
        public event Action<NetworkDiagnostics> OnDiagnosticsUpdated;
        public event Action<ConnectionState> OnConnectionStateChanged;
        public event Action<NetworkQuality> OnNetworkQualityChanged;
        public event Action OnHeartbeatSent;
        public event Action OnHeartbeatReceived;
        public event Action OnConnectionStable;
        public event Action OnConnectionUnstable;

        private void Start()
        {
            InitializeMonitor();
        }

        private void Update()
        {
            if (_isMonitoring)
            {
                UpdateMonitoring();
            }
        }

        /// <summary>
        /// 初始化监控器
        /// </summary>
        private void InitializeMonitor()
        {
            _currentDiagnostics = new NetworkDiagnostics
            {
                State = ConnectionState.Disconnected,
                Quality = NetworkQuality.VeryPoor,
                Latency = 0f,
                PacketLoss = 0f,
                BandwidthUsage = 0f,
                Stability = 0f,
                LastUpdateTime = Time.time
            };

            _lastMonitorTime = Time.time;
            _lastHeartbeatTime = Time.time;
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            if (_isMonitoring) return;

            _isMonitoring = true;
            _lastMonitorTime = Time.time;
            _lastHeartbeatTime = Time.time;

            if (logConnectionEvents)
            {
                UnityEngine.Debug.Log("[NetworkMonitor] 开始监控网络连接");
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            _isMonitoring = false;

            if (logConnectionEvents)
            {
                UnityEngine.Debug.Log("[NetworkMonitor] 停止监控网络连接");
            }
        }

        /// <summary>
        /// 更新监控
        /// </summary>
        private void UpdateMonitoring()
        {
            float currentTime = Time.time;

            // 定期更新诊断信息
            if (currentTime - _lastMonitorTime >= monitorInterval)
            {
                UpdateDiagnostics();
                _lastMonitorTime = currentTime;
            }

            // 定期发送心跳
            if (currentTime - _lastHeartbeatTime >= heartbeatInterval)
            {
                SendHeartbeat();
                _lastHeartbeatTime = currentTime;
            }
        }

        /// <summary>
        /// 更新诊断信息
        /// </summary>
        private void UpdateDiagnostics()
        {
            var previousState = _currentDiagnostics.State;
            var previousQuality = _currentDiagnostics.Quality;

            // 更新连接状态
            UpdateConnectionState();

            // 更新网络质量
            UpdateNetworkQuality();

            // 更新带宽使用
            UpdateBandwidthUsage();

            // 更新稳定性
            UpdateConnectionStability();

            // 更新时间戳
            _currentDiagnostics.LastUpdateTime = Time.time;

            // 触发事件
            OnDiagnosticsUpdated?.Invoke(_currentDiagnostics);

            if (previousState != _currentDiagnostics.State)
            {
                OnConnectionStateChanged?.Invoke(_currentDiagnostics.State);

                if (logConnectionEvents)
                {
                    UnityEngine.Debug.Log($"[NetworkMonitor] 连接状态变化: {previousState} -> {_currentDiagnostics.State}");
                }
            }

            if (previousQuality != _currentDiagnostics.Quality)
            {
                OnNetworkQualityChanged?.Invoke(_currentDiagnostics.Quality);

                if (logConnectionEvents)
                {
                    UnityEngine.Debug.Log($"[NetworkMonitor] 网络质量变化: {previousQuality} -> {_currentDiagnostics.Quality}");
                }
            }
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        private void UpdateConnectionState()
        {
            var networkManager = GetNetworkManager();

            if (networkManager == null)
            {
                _currentDiagnostics.State = ConnectionState.Disconnected;
                return;
            }

            if (networkManager.IsConnected)
            {
                // 检查连接稳定性
                if (_currentDiagnostics.Stability > 0.8f)
                {
                    _currentDiagnostics.State = ConnectionState.Connected;
                }
                else
                {
                    _currentDiagnostics.State = ConnectionState.Unstable;
                }
            }
            else if (networkManager.IsConnecting)
            {
                _currentDiagnostics.State = ConnectionState.Connecting;
            }
            else
            {
                _currentDiagnostics.State = ConnectionState.Disconnected;
            }
        }

        /// <summary>
        /// 获取网络管理器实例
        /// </summary>
        private INetworkManager GetNetworkManager()
        {
            // 查找实现了INetworkManager接口的组件
            var allComponents = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None); // 使用FindObjectsByType替代已弃用的FindObjectsOfType
            foreach (var component in allComponents)
            {
                if (component is INetworkManager networkManager)
                {
                    return networkManager;
                }
            }
            return null;
        }

        /// <summary>
        /// 更新网络质量
        /// </summary>
        private void UpdateNetworkQuality()
        {
            float avgLatency = GetAverageLatency();
            float avgPacketLoss = GetAveragePacketLoss();

            // 基于延迟和丢包率计算网络质量
            if (avgLatency <= excellentLatency && avgPacketLoss <= 0.01f)
            {
                _currentDiagnostics.Quality = NetworkQuality.Excellent;
            }
            else if (avgLatency <= goodLatency && avgPacketLoss <= 0.02f)
            {
                _currentDiagnostics.Quality = NetworkQuality.Good;
            }
            else if (avgLatency <= fairLatency && avgPacketLoss <= 0.05f)
            {
                _currentDiagnostics.Quality = NetworkQuality.Fair;
            }
            else if (avgLatency <= poorLatency && avgPacketLoss <= 0.1f)
            {
                _currentDiagnostics.Quality = NetworkQuality.Poor;
            }
            else
            {
                _currentDiagnostics.Quality = NetworkQuality.VeryPoor;
            }

            _currentDiagnostics.Latency = avgLatency;
            _currentDiagnostics.PacketLoss = avgPacketLoss;
        }

        /// <summary>
        /// 更新带宽使用
        /// </summary>
        private void UpdateBandwidthUsage()
        {
            // 计算当前带宽使用 (简化实现)
            float timeDelta = Time.deltaTime;
            if (timeDelta > 0)
            {
                _currentDiagnostics.BandwidthUsage = (_totalBandwidthSent + _totalBandwidthReceived) / timeDelta;
            }

            // 重置计数器
            _totalBandwidthSent = 0f;
            _totalBandwidthReceived = 0f;
        }

        /// <summary>
        /// 更新连接稳定性
        /// </summary>
        private void UpdateConnectionStability()
        {
            // 基于延迟变化和丢包率计算稳定性
            float latencyVariance = GetLatencyVariance();
            float packetLoss = _currentDiagnostics.PacketLoss;

            // 稳定性评分 (0-1)
            float stabilityScore = 1f;

            // 延迟变化影响
            if (latencyVariance > 50f)
            {
                stabilityScore -= 0.3f;
            }
            else if (latencyVariance > 20f)
            {
                stabilityScore -= 0.1f;
            }

            // 丢包率影响 - 使用maxPacketLoss阈值
            if (packetLoss > maxPacketLoss)
            {
                stabilityScore -= (packetLoss - maxPacketLoss) * 5f; // 超过阈值时加重惩罚
            }
            stabilityScore -= packetLoss * 2f;

            _currentDiagnostics.Stability = Mathf.Clamp01(stabilityScore);

            // 触发稳定性事件
            if (_currentDiagnostics.Stability > 0.8f)
            {
                OnConnectionStable?.Invoke();
            }
            else if (_currentDiagnostics.Stability < 0.5f)
            {
                OnConnectionUnstable?.Invoke();
            }
        }

        /// <summary>
        /// 发送心跳
        /// </summary>
        private async void SendHeartbeat()
        {
            var networkManager = GetNetworkManager();
            if (networkManager != null && networkManager.IsConnected)
            {
                try
                {
                    float sendTime = Time.time;
                    await networkManager.SendHeartbeat();
                    OnHeartbeatSent?.Invoke();

                    // 记录心跳发送时间用于延迟计算
                    StartCoroutine(WaitForHeartbeatResponse(sendTime));
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogError($"[NetworkMonitor] 发送心跳失败: {ex.Message}");
                    NetworkErrorHandler.Instance.HandleError(ErrorType.ConnectionTimeout, "心跳发送失败", ex);
                }
            }
        }

        /// <summary>
        /// 等待心跳响应
        /// </summary>
        private IEnumerator WaitForHeartbeatResponse(float sendTime)
        {
            float timeout = connectionTimeout;
            float elapsed = 0f;

            while (elapsed < timeout)
            {
                elapsed += Time.deltaTime;
                yield return null;

                // 这里应该检查是否收到心跳响应
                // 简化实现：假设总是收到响应
                if (elapsed > 0.1f) // 模拟网络延迟
                {
                    HandleHeartbeatReceived();
                    yield break;
                }
            }

            // 心跳超时
            UnityEngine.Debug.LogWarning("[NetworkMonitor] 心跳超时");
            NetworkErrorHandler.Instance.HandleError(ErrorType.ConnectionTimeout, "心跳超时");
        }

        /// <summary>
        /// 处理心跳响应
        /// </summary>
        public void HandleHeartbeatReceived()
        {
            float receiveTime = Time.time;
            float latency = (receiveTime - _lastHeartbeatTime) * 1000f; // 转换为毫秒

            // 记录延迟
            RecordLatency(latency);

            OnHeartbeatReceived?.Invoke();
        }

        /// <summary>
        /// 记录延迟
        /// </summary>
        public void RecordLatency(float latency)
        {
            _latencyHistory.Enqueue(latency);

            if (_latencyHistory.Count > latencyHistorySize)
            {
                _latencyHistory.Dequeue();
            }
        }

        /// <summary>
        /// 记录丢包
        /// </summary>
        public void RecordPacketLoss(float lossRate)
        {
            _packetLossHistory.Enqueue(lossRate);

            if (_packetLossHistory.Count > latencyHistorySize)
            {
                _packetLossHistory.Dequeue();
            }
        }

        /// <summary>
        /// 记录数据包发送
        /// </summary>
        public void RecordPacketSent(int size)
        {
            _totalPacketsSent++;
            _totalBandwidthSent += size / 1024f; // 转换为KB
        }

        /// <summary>
        /// 记录数据包接收
        /// </summary>
        public void RecordPacketReceived(int size)
        {
            _totalPacketsReceived++;
            _totalBandwidthReceived += size / 1024f; // 转换为KB
        }

        /// <summary>
        /// 记录数据包丢失
        /// </summary>
        public void RecordPacketLost()
        {
            _totalPacketsLost++;
        }

        /// <summary>
        /// 获取平均延迟
        /// </summary>
        private float GetAverageLatency()
        {
            if (_latencyHistory.Count == 0) return 0f;

            float sum = 0f;
            foreach (float latency in _latencyHistory)
            {
                sum += latency;
            }

            return sum / _latencyHistory.Count;
        }

        /// <summary>
        /// 获取平均丢包率
        /// </summary>
        private float GetAveragePacketLoss()
        {
            if (_packetLossHistory.Count == 0)
            {
                // 基于发送和丢失的包计算丢包率
                int totalPackets = _totalPacketsSent + _totalPacketsLost;
                if (totalPackets == 0) return 0f;

                return (float)_totalPacketsLost / totalPackets;
            }

            float sum = 0f;
            foreach (float loss in _packetLossHistory)
            {
                sum += loss;
            }

            return sum / _packetLossHistory.Count;
        }

        /// <summary>
        /// 获取延迟方差
        /// </summary>
        private float GetLatencyVariance()
        {
            if (_latencyHistory.Count < 2) return 0f;

            float average = GetAverageLatency();
            float variance = 0f;

            foreach (float latency in _latencyHistory)
            {
                variance += (latency - average) * (latency - average);
            }

            return variance / _latencyHistory.Count;
        }

        /// <summary>
        /// 获取当前诊断信息
        /// </summary>
        public NetworkDiagnostics GetCurrentDiagnostics()
        {
            return _currentDiagnostics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _latencyHistory.Clear();
            _packetLossHistory.Clear();
            _totalPacketsSent = 0;
            _totalPacketsReceived = 0;
            _totalPacketsLost = 0;
            _totalBandwidthSent = 0f;
            _totalBandwidthReceived = 0f;
        }

        /// <summary>
        /// 屏幕显示调试信息
        /// </summary>
        private void OnGUI()
        {
            if (!showDebugInfo) return;

            GUILayout.BeginArea(new Rect(10, 200, 300, 200));
            GUILayout.BeginVertical("box");

            GUILayout.Label("网络连接监控", GUI.skin.label);
            GUILayout.Label($"状态: {_currentDiagnostics.GetStateDescription()}");
            GUILayout.Label($"质量: {_currentDiagnostics.GetQualityDescription()}");
            GUILayout.Label($"延迟: {_currentDiagnostics.Latency:F1}ms");
            GUILayout.Label($"丢包率: {_currentDiagnostics.PacketLoss * 100:F1}%");
            GUILayout.Label($"带宽: {_currentDiagnostics.BandwidthUsage:F1} KB/s");
            GUILayout.Label($"稳定性: {_currentDiagnostics.Stability * 100:F1}%");

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
