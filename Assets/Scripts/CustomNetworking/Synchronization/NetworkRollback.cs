using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Synchronization
{
    /// <summary>
    /// 网络回滚系统 - 服务器权威的回滚和重播机制
    /// </summary>
    public class NetworkRollback : MonoBehaviour
    {
        [Header("回滚设置")]
        [SerializeField] private bool enableRollback = true;
        [SerializeField] private int maxRollbackFrames = 60; // 1秒的回滚历史
        [SerializeField] private float rollbackThreshold = 0.1f;
        [SerializeField] private bool enableRollbackSmoothing = true;

        [Header("状态管理")]
        [SerializeField] private int stateHistorySize = 120; // 2秒的状态历史
        [SerializeField] private bool compressOldStates = true;
        [SerializeField] private int compressionInterval = 10; // 每10帧压缩一次

        [Header("冲突解决")]
        [SerializeField] private ConflictResolutionMode conflictResolution = ConflictResolutionMode.ServerAuthority;
        [SerializeField] private float conflictDetectionThreshold = 0.5f;
        [SerializeField] private bool enableConflictLogging = true;

        // 状态历史
        private Dictionary<int, RollbackState> _stateHistory = new Dictionary<int, RollbackState>();
        private Dictionary<int, List<RollbackInput>> _inputHistory = new Dictionary<int, List<RollbackInput>>();
        private int _currentTick;
        private int _confirmedTick;

        // 组件引用
        private Transform _transform;
        private NetworkBehaviour _networkBehaviour;
        private Rigidbody _rigidbody;

        // 回滚状态
        private bool _isRollingBack;
        private int _rollbackToTick;
        private RollbackState _rollbackState;
        private Queue<RollbackOperation> _pendingRollbacks = new Queue<RollbackOperation>();

        // 性能优化
        private const float FIXED_TIMESTEP = 1f / 60f;
        private int _lastCompressionTick;

        public enum ConflictResolutionMode
        {
            ServerAuthority,    // 服务器权威
            ClientPrediction,   // 客户端预测
            Hybrid             // 混合模式
        }

        public event Action<int, RollbackState> OnRollbackStarted;
        public event Action<int, RollbackState> OnRollbackCompleted;
        public event Action<RollbackConflict> OnConflictDetected;

        private void Awake()
        {
            _transform = transform;
            _networkBehaviour = GetComponent<NetworkBehaviour>();
            _rigidbody = GetComponent<Rigidbody>();

            InitializeRollback();
        }

        private void FixedUpdate()
        {
            if (enableRollback)
            {
                ProcessRollback();
                CleanupOldStates();
            }
        }

        /// <summary>
        /// 初始化回滚系统
        /// </summary>
        private void InitializeRollback()
        {
            _currentTick = 0;
            _confirmedTick = 0;
            _lastCompressionTick = 0;

            // 保存初始状态
            var initialState = CreateCurrentState();
            _stateHistory[0] = initialState;
        }

        /// <summary>
        /// 创建当前状态快照
        /// </summary>
        private RollbackState CreateCurrentState()
        {
            return new RollbackState
            {
                Tick = _currentTick,
                Position = _transform.position,
                Rotation = _transform.rotation,
                Velocity = _rigidbody ? _rigidbody.linearVelocity : Vector3.zero,
                AngularVelocity = _rigidbody ? _rigidbody.angularVelocity : Vector3.zero,
                Timestamp = Time.time,
                IsConfirmed = false
            };
        }

        /// <summary>
        /// 保存当前状态
        /// </summary>
        public void SaveCurrentState()
        {
            var state = CreateCurrentState();
            _stateHistory[_currentTick] = state;
            _currentTick++;
        }

        /// <summary>
        /// 添加输入到历史
        /// </summary>
        public void AddInput(RollbackInput input)
        {
            if (!_inputHistory.ContainsKey(input.Tick))
            {
                _inputHistory[input.Tick] = new List<RollbackInput>();
            }

            _inputHistory[input.Tick].Add(input);
        }

        /// <summary>
        /// 确认状态
        /// </summary>
        public void ConfirmState(int tick, RollbackState authorityState)
        {
            if (_stateHistory.ContainsKey(tick))
            {
                var localState = _stateHistory[tick];

                // 检查是否需要回滚
                float deviation = Vector3.Distance(localState.Position, authorityState.Position);

                // 使用conflictDetectionThreshold进行冲突检测
                if (deviation > conflictDetectionThreshold)
                {
                    // 需要回滚
                    if (enableConflictLogging)
                    {
                        UnityEngine.Debug.LogWarning($"[NetworkRollback] 检测到状态冲突，Tick: {tick}, 偏差: {deviation:F3}");
                    }

                    var conflict = new RollbackConflict
                    {
                        Tick = tick,
                        LocalState = localState,
                        AuthorityState = authorityState,
                        Deviation = deviation
                    };

                    OnConflictDetected?.Invoke(conflict);

                    // 根据conflictResolution策略执行回滚
                    if (conflictResolution == ConflictResolutionMode.ServerAuthority)
                    {
                        PerformRollback(tick, authorityState);
                    }
                    else if (conflictResolution == ConflictResolutionMode.ClientPrediction && deviation > rollbackThreshold)
                    {
                        // 客户端预测模式下，只有偏差很大时才回滚
                        PerformRollback(tick, authorityState);
                    }
                }
                else
                {
                    // 确认状态
                    authorityState.IsConfirmed = true;
                    _stateHistory[tick] = authorityState;
                    _confirmedTick = Mathf.Max(_confirmedTick, tick);
                }
            }
        }

        /// <summary>
        /// 执行回滚
        /// </summary>
        private void PerformRollback(int toTick, RollbackState authorityState)
        {
            // 检查回滚范围是否超过maxRollbackFrames限制
            int rollbackDistance = _currentTick - toTick;
            if (rollbackDistance > maxRollbackFrames)
            {
                UnityEngine.Debug.LogWarning($"[NetworkRollback] 回滚距离({rollbackDistance})超过最大限制({maxRollbackFrames})，跳过回滚");
                return;
            }

            if (_isRollingBack)
            {
                // 如果已经在回滚，加入队列
                _pendingRollbacks.Enqueue(new RollbackOperation
                {
                    ToTick = toTick,
                    AuthorityState = authorityState
                });
                return;
            }

            _isRollingBack = true;
            _rollbackToTick = toTick;
            _rollbackState = authorityState;

            OnRollbackStarted?.Invoke(toTick, authorityState);

            // 回滚到指定状态
            RollbackToState(authorityState);

            // 重播后续输入
            ReplayInputsFromTick(toTick + 1);

            OnRollbackCompleted?.Invoke(toTick, authorityState);

            _isRollingBack = false;

            // 处理待处理的回滚
            ProcessPendingRollbacks();
        }

        /// <summary>
        /// 回滚到指定状态
        /// </summary>
        private void RollbackToState(RollbackState state)
        {
            // 应用状态
            if (enableRollbackSmoothing)
            {
                // 平滑回滚
                StartCoroutine(SmoothRollbackCoroutine(state));
            }
            else
            {
                // 直接回滚
                ApplyState(state);
            }

            // 更新状态历史
            _stateHistory[state.Tick] = state;
        }

        /// <summary>
        /// 平滑回滚协程
        /// </summary>
        private System.Collections.IEnumerator SmoothRollbackCoroutine(RollbackState targetState)
        {
            Vector3 startPosition = _transform.position;
            Quaternion startRotation = _transform.rotation;
            Vector3 startVelocity = _rigidbody ? _rigidbody.linearVelocity : Vector3.zero;

            float duration = 0.1f; // 回滚动画时间
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;

                // 插值到目标状态
                _transform.position = Vector3.Lerp(startPosition, targetState.Position, t);
                _transform.rotation = Quaternion.Lerp(startRotation, targetState.Rotation, t);

                if (_rigidbody != null)
                {
                    _rigidbody.linearVelocity = Vector3.Lerp(startVelocity, targetState.Velocity, t);
                }

                yield return null;
            }

            // 确保最终状态正确
            ApplyState(targetState);
        }

        /// <summary>
        /// 应用状态
        /// </summary>
        private void ApplyState(RollbackState state)
        {
            _transform.position = state.Position;
            _transform.rotation = state.Rotation;

            if (_rigidbody != null)
            {
                _rigidbody.linearVelocity = state.Velocity;
                _rigidbody.angularVelocity = state.AngularVelocity;
            }
        }

        /// <summary>
        /// 从指定tick重播输入
        /// </summary>
        private void ReplayInputsFromTick(int fromTick)
        {
            // 获取需要重播的输入
            var inputsToReplay = new List<(int tick, List<RollbackInput> inputs)>();

            foreach (var kvp in _inputHistory)
            {
                if (kvp.Key >= fromTick && kvp.Key <= _currentTick)
                {
                    inputsToReplay.Add((kvp.Key, kvp.Value));
                }
            }

            // 按tick排序
            inputsToReplay.Sort((a, b) => a.tick.CompareTo(b.tick));

            // 重播输入
            foreach (var (tick, inputs) in inputsToReplay)
            {
                foreach (var input in inputs)
                {
                    ProcessRollbackInput(input);
                }

                // 保存重播后的状态
                var newState = CreateCurrentState();
                newState.Tick = tick;
                _stateHistory[tick] = newState;
            }
        }

        /// <summary>
        /// 处理回滚输入
        /// </summary>
        private void ProcessRollbackInput(RollbackInput input)
        {
            // 根据输入类型处理
            switch (input.Type)
            {
                case InputType.Movement:
                    ProcessMovementInput(input);
                    break;

                case InputType.Action:
                    ProcessActionInput(input);
                    break;

                case InputType.Interaction:
                    ProcessInteractionInput(input);
                    break;
            }
        }

        /// <summary>
        /// 处理移动输入
        /// </summary>
        private void ProcessMovementInput(RollbackInput input)
        {
            if (input.Data is Vector2 movement)
            {
                Vector3 moveVector = new Vector3(movement.x, 0, movement.y);
                Vector3 newPosition = _transform.position + moveVector * 8f * FIXED_TIMESTEP; // 移动速度

                _transform.position = newPosition;

                if (_rigidbody != null)
                {
                    _rigidbody.linearVelocity = moveVector * 8f;
                }
            }
        }

        /// <summary>
        /// 处理动作输入
        /// </summary>
        private void ProcessActionInput(RollbackInput input)
        {
            // 处理动作输入（如跳跃、攻击等）
            // 这里可以根据具体游戏逻辑实现
        }

        /// <summary>
        /// 处理交互输入
        /// </summary>
        private void ProcessInteractionInput(RollbackInput input)
        {
            // 处理交互输入（如使用物品、开门等）
            // 这里可以根据具体游戏逻辑实现
        }

        /// <summary>
        /// 处理回滚
        /// </summary>
        private void ProcessRollback()
        {
            // 保存当前帧状态
            SaveCurrentState();

            // 处理待处理的回滚
            if (!_isRollingBack && _pendingRollbacks.Count > 0)
            {
                ProcessPendingRollbacks();
            }
        }

        /// <summary>
        /// 处理待处理的回滚
        /// </summary>
        private void ProcessPendingRollbacks()
        {
            if (_pendingRollbacks.Count > 0)
            {
                var rollback = _pendingRollbacks.Dequeue();
                PerformRollback(rollback.ToTick, rollback.AuthorityState);
            }
        }

        /// <summary>
        /// 清理旧状态
        /// </summary>
        private void CleanupOldStates()
        {
            // 清理超出历史大小的状态
            var ticksToRemove = new List<int>();

            foreach (var tick in _stateHistory.Keys)
            {
                if (tick < _currentTick - stateHistorySize)
                {
                    ticksToRemove.Add(tick);
                }
            }

            foreach (var tick in ticksToRemove)
            {
                _stateHistory.Remove(tick);
                _inputHistory.Remove(tick);
            }

            // 压缩旧状态
            if (compressOldStates && _currentTick - _lastCompressionTick >= compressionInterval)
            {
                CompressOldStates();
                _lastCompressionTick = _currentTick;
            }
        }

        /// <summary>
        /// 压缩旧状态
        /// </summary>
        private void CompressOldStates()
        {
            // 简化实现：移除一些中间状态，只保留关键帧
            var ticksToCompress = new List<int>();

            foreach (var tick in _stateHistory.Keys)
            {
                if (tick < _confirmedTick && tick % compressionInterval != 0)
                {
                    ticksToCompress.Add(tick);
                }
            }

            foreach (var tick in ticksToCompress)
            {
                _stateHistory.Remove(tick);
            }
        }

        /// <summary>
        /// 获取状态历史
        /// </summary>
        public Dictionary<int, RollbackState> GetStateHistory()
        {
            return new Dictionary<int, RollbackState>(_stateHistory);
        }

        /// <summary>
        /// 获取指定tick的状态
        /// </summary>
        public RollbackState? GetStateAtTick(int tick)
        {
            return _stateHistory.ContainsKey(tick) ? _stateHistory[tick] : null;
        }

        /// <summary>
        /// 获取当前tick
        /// </summary>
        public int GetCurrentTick()
        {
            return _currentTick;
        }

        /// <summary>
        /// 获取确认的tick
        /// </summary>
        public int GetConfirmedTick()
        {
            return _confirmedTick;
        }

        /// <summary>
        /// 是否正在回滚
        /// </summary>
        public bool IsRollingBack()
        {
            return _isRollingBack;
        }

        /// <summary>
        /// 强制同步到指定状态
        /// </summary>
        public void ForceSync(RollbackState state)
        {
            // 清空所有历史
            _stateHistory.Clear();
            _inputHistory.Clear();
            _pendingRollbacks.Clear();

            // 应用状态
            ApplyState(state);

            // 重置tick
            _currentTick = state.Tick;
            _confirmedTick = state.Tick;

            // 保存状态
            _stateHistory[state.Tick] = state;

            _isRollingBack = false;
        }
    }

    /// <summary>
    /// 回滚状态
    /// </summary>
    [Serializable]
    public struct RollbackState
    {
        public int Tick;
        public Vector3 Position;
        public Quaternion Rotation;
        public Vector3 Velocity;
        public Vector3 AngularVelocity;
        public float Timestamp;
        public bool IsConfirmed;

        public override string ToString()
        {
            return $"RollbackState[Tick: {Tick}, Pos: {Position}, Confirmed: {IsConfirmed}]";
        }
    }

    /// <summary>
    /// 回滚输入
    /// </summary>
    [Serializable]
    public struct RollbackInput
    {
        public int Tick;
        public InputType Type;
        public object Data;
        public float Timestamp;

        public override string ToString()
        {
            return $"RollbackInput[Tick: {Tick}, Type: {Type}, Time: {Timestamp:F3}]";
        }
    }

    /// <summary>
    /// 输入类型
    /// </summary>
    public enum InputType
    {
        Movement,
        Action,
        Interaction
    }

    /// <summary>
    /// 回滚操作
    /// </summary>
    public struct RollbackOperation
    {
        public int ToTick;
        public RollbackState AuthorityState;
    }

    /// <summary>
    /// 回滚冲突
    /// </summary>
    public struct RollbackConflict
    {
        public int Tick;
        public RollbackState LocalState;
        public RollbackState AuthorityState;
        public float Deviation;
    }
}
