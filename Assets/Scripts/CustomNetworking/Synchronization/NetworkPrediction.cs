using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Synchronization
{
    /// <summary>
    /// 网络预测系统 - 客户端预测和服务器权威验证
    /// </summary>
    public class NetworkPrediction : MonoBehaviour
    {
        [Header("预测设置")]
        [SerializeField] private bool enablePrediction = true;
        [SerializeField] private float predictionTime = 0.1f;
        [SerializeField] private int maxPredictionSteps = 10;
        [SerializeField] private float reconciliationThreshold = 0.1f;

        [Header("输入缓冲")]
        [SerializeField] private int inputBufferSize = 60;
        [SerializeField] private bool enableInputSmoothing = true;
        [SerializeField] private float inputSmoothingFactor = 5f;

        [Header("状态验证")]
        [SerializeField] private bool enableStateValidation = true;
        [SerializeField] private float validationInterval = 0.5f;
        [SerializeField] private float maxStateDeviation = 1f;

        // 预测状态
        private Queue<PredictionInput> _inputBuffer = new Queue<PredictionInput>();
        private Queue<PredictionState> _stateHistory = new Queue<PredictionState>();
        private PredictionState _currentState;
        private PredictionState _authorityState;

        // 组件引用
        private Transform _transform;
        private NetworkBehaviour _networkBehaviour;
        private Rigidbody _rigidbody;

        // 预测参数
        private int _currentTick;
        private float _lastValidationTime;
        private bool _needsReconciliation;
        private Vector3 _predictedVelocity;

        // 性能优化
        private const float FIXED_TIMESTEP = 1f / 60f; // 60 FPS
        private const int MAX_STATE_HISTORY = 120; // 2秒历史

        public event Action<PredictionState, PredictionState> OnStateReconciliation;
        public event Action<int> OnInputProcessed;

        private void Awake()
        {
            _transform = transform;
            _networkBehaviour = GetComponent<NetworkBehaviour>();
            _rigidbody = GetComponent<Rigidbody>();

            InitializePrediction();
        }

        private void FixedUpdate()
        {
            if (enablePrediction && _networkBehaviour != null && _networkBehaviour.HasInputAuthority)
            {
                ProcessPrediction();
            }
        }

        private void Update()
        {
            if (enableStateValidation)
            {
                CheckStateValidation();
            }
        }

        /// <summary>
        /// 初始化预测系统
        /// </summary>
        private void InitializePrediction()
        {
            _currentState = new PredictionState
            {
                Position = _transform.position,
                Rotation = _transform.rotation,
                Velocity = _rigidbody ? _rigidbody.linearVelocity : Vector3.zero,
                Tick = 0,
                Timestamp = Time.time
            };

            _authorityState = _currentState;
            _currentTick = 0;
            _lastValidationTime = Time.time;
        }

        /// <summary>
        /// 添加输入到缓冲区
        /// </summary>
        public void AddInput(Vector2 movementInput, bool[] buttonInputs)
        {
            var input = new PredictionInput
            {
                MovementInput = movementInput,
                ButtonInputs = buttonInputs,
                Tick = _currentTick,
                Timestamp = Time.time
            };

            _inputBuffer.Enqueue(input);

            // 限制缓冲区大小
            while (_inputBuffer.Count > inputBufferSize)
            {
                _inputBuffer.Dequeue();
            }

            _currentTick++;
        }

        /// <summary>
        /// 处理预测
        /// </summary>
        private void ProcessPrediction()
        {
            // 处理输入缓冲区中的输入，使用maxPredictionSteps限制处理数量
            int processedSteps = 0;
            while (_inputBuffer.Count > 0 && processedSteps < maxPredictionSteps)
            {
                var input = _inputBuffer.Dequeue();
                ProcessInput(input);
                processedSteps++;
            }

            // 应用预测状态
            ApplyPredictedState();
        }

        /// <summary>
        /// 处理单个输入
        /// </summary>
        private void ProcessInput(PredictionInput input)
        {
            // 保存当前状态到历史
            SaveStateToHistory(_currentState);

            // 执行预测逻辑
            var newState = PredictNextState(_currentState, input);

            // 验证预测状态
            if (ValidatePredictedState(newState))
            {
                _currentState = newState;
                OnInputProcessed?.Invoke(input.Tick);
            }
            else
            {
                // 预测失败，回滚到上一个有效状态
                UnityEngine.Debug.LogWarning("[NetworkPrediction] 预测状态无效，回滚");
            }
        }

        /// <summary>
        /// 预测下一个状态
        /// </summary>
        private PredictionState PredictNextState(PredictionState currentState, PredictionInput input)
        {
            var newState = currentState;
            newState.Tick = input.Tick;
            newState.Timestamp = input.Timestamp;

            // 使用predictionTime作为时间步长
            float deltaTime = predictionTime;

            // 处理移动输入
            if (input.MovementInput.magnitude > 0.01f)
            {
                Vector3 movement = ProcessMovementInput(input.MovementInput, currentState);
                newState.Position = currentState.Position + movement * FIXED_TIMESTEP;
                newState.Velocity = movement;
            }
            else
            {
                // 应用摩擦力
                newState.Velocity = Vector3.Lerp(currentState.Velocity, Vector3.zero, 0.1f);
                newState.Position = currentState.Position + newState.Velocity * FIXED_TIMESTEP;
            }

            // 处理按钮输入
            ProcessButtonInputs(input.ButtonInputs, ref newState);

            // 应用物理约束
            ApplyPhysicsConstraints(ref newState);

            return newState;
        }

        /// <summary>
        /// 处理移动输入
        /// </summary>
        private Vector3 ProcessMovementInput(Vector2 input, PredictionState currentState)
        {
            // 获取移动参数
            float moveSpeed = GetMoveSpeed();
            float acceleration = GetAcceleration();

            // 计算目标速度
            Vector3 targetVelocity = new Vector3(input.x, 0, input.y) * moveSpeed;

            // 应用加速度
            Vector3 velocityChange = targetVelocity - currentState.Velocity;
            velocityChange = Vector3.ClampMagnitude(velocityChange, acceleration * FIXED_TIMESTEP);

            return currentState.Velocity + velocityChange;
        }

        /// <summary>
        /// 处理按钮输入
        /// </summary>
        private void ProcessButtonInputs(bool[] buttonInputs, ref PredictionState state)
        {
            if (buttonInputs == null) return;

            // 处理跳跃
            if (buttonInputs.Length > 0 && buttonInputs[0] && IsGrounded(state.Position))
            {
                state.Velocity += Vector3.up * GetJumpForce();
            }

            // 处理其他按钮输入...
        }

        /// <summary>
        /// 应用物理约束
        /// </summary>
        private void ApplyPhysicsConstraints(ref PredictionState state)
        {
            // 重力
            if (!IsGrounded(state.Position))
            {
                state.Velocity += Physics.gravity * FIXED_TIMESTEP;
            }

            // 地面碰撞
            if (state.Position.y < 0f)
            {
                state.Position.y = 0f;
                if (state.Velocity.y < 0f)
                {
                    state.Velocity.y = 0f;
                }
            }

            // 边界约束
            state.Position.x = Mathf.Clamp(state.Position.x, -50f, 50f);
            state.Position.z = Mathf.Clamp(state.Position.z, -50f, 50f);
        }

        /// <summary>
        /// 验证预测状态
        /// </summary>
        private bool ValidatePredictedState(PredictionState state)
        {
            // 检查位置是否合理
            if (float.IsNaN(state.Position.x) || float.IsNaN(state.Position.y) || float.IsNaN(state.Position.z))
            {
                return false;
            }

            // 检查速度是否合理
            if (state.Velocity.magnitude > 50f) // 最大速度限制
            {
                return false;
            }

            // 检查位置变化是否过大
            float positionDelta = Vector3.Distance(state.Position, _currentState.Position);
            if (positionDelta > 10f) // 最大位置变化限制
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 应用预测状态
        /// </summary>
        private void ApplyPredictedState()
        {
            if (enableInputSmoothing)
            {
                // 平滑应用预测状态
                _transform.position = Vector3.Lerp(_transform.position, _currentState.Position, inputSmoothingFactor * Time.deltaTime);
                _transform.rotation = Quaternion.Lerp(_transform.rotation, _currentState.Rotation, inputSmoothingFactor * Time.deltaTime);
            }
            else
            {
                // 直接应用预测状态
                _transform.position = _currentState.Position;
                _transform.rotation = _currentState.Rotation;
            }

            // 更新Rigidbody
            if (_rigidbody != null)
            {
                _rigidbody.linearVelocity = _currentState.Velocity;
            }
        }

        /// <summary>
        /// 接收服务器权威状态
        /// </summary>
        public void ReceiveAuthorityState(PredictionState authorityState)
        {
            _authorityState = authorityState;

            // 检查是否需要和解
            float deviation = Vector3.Distance(_currentState.Position, authorityState.Position);
            if (deviation > reconciliationThreshold)
            {
                _needsReconciliation = true;
                PerformReconciliation(authorityState);
            }
        }

        /// <summary>
        /// 执行状态和解
        /// </summary>
        private void PerformReconciliation(PredictionState authorityState)
        {
            UnityEngine.Debug.Log($"[NetworkPrediction] 执行状态和解，偏差: {Vector3.Distance(_currentState.Position, authorityState.Position):F3}");

            // 保存当前预测状态
            var oldPredictedState = _currentState;

            // 回滚到权威状态
            _currentState = authorityState;

            // 重新应用权威状态之后的输入
            ReplayInputsFromTick(authorityState.Tick);

            // 触发和解事件
            OnStateReconciliation?.Invoke(oldPredictedState, _currentState);

            _needsReconciliation = false;
        }

        /// <summary>
        /// 从指定tick重新播放输入
        /// </summary>
        private void ReplayInputsFromTick(int fromTick)
        {
            // 查找状态历史中对应的状态
            var stateArray = _stateHistory.ToArray();
            PredictionState? replayState = null;

            foreach (var state in stateArray)
            {
                if (state.Tick == fromTick)
                {
                    replayState = state;
                    break;
                }
            }

            if (!replayState.HasValue) return;

            // 从该状态开始重新预测
            var tempState = replayState.Value;

            // 重新应用后续的输入（这里简化处理）
            // 实际实现中需要保存输入历史并重新应用
        }

        /// <summary>
        /// 保存状态到历史
        /// </summary>
        private void SaveStateToHistory(PredictionState state)
        {
            _stateHistory.Enqueue(state);

            // 限制历史大小
            while (_stateHistory.Count > MAX_STATE_HISTORY)
            {
                _stateHistory.Dequeue();
            }
        }

        /// <summary>
        /// 检查状态验证
        /// </summary>
        private void CheckStateValidation()
        {
            float currentTime = Time.time;

            if (currentTime - _lastValidationTime >= validationInterval)
            {
                ValidateCurrentState();
                _lastValidationTime = currentTime;
            }
        }

        /// <summary>
        /// 验证当前状态
        /// </summary>
        private void ValidateCurrentState()
        {
            // 检查与权威状态的偏差
            float deviation = Vector3.Distance(_currentState.Position, _authorityState.Position);

            if (deviation > maxStateDeviation)
            {
                UnityEngine.Debug.LogWarning($"[NetworkPrediction] 状态偏差过大: {deviation:F3}，触发强制同步");
                ForceSync(_authorityState);
            }
        }

        /// <summary>
        /// 强制同步到指定状态
        /// </summary>
        public void ForceSync(PredictionState state)
        {
            _currentState = state;
            _authorityState = state;

            // 清空缓冲区和历史
            _inputBuffer.Clear();
            _stateHistory.Clear();

            // 应用状态
            _transform.position = state.Position;
            _transform.rotation = state.Rotation;

            if (_rigidbody != null)
            {
                _rigidbody.linearVelocity = state.Velocity;
            }

            _needsReconciliation = false;
        }

        /// <summary>
        /// 获取移动速度
        /// </summary>
        private float GetMoveSpeed()
        {
            // 这里可以根据游戏逻辑返回不同的移动速度
            return 8f;
        }

        /// <summary>
        /// 获取加速度
        /// </summary>
        private float GetAcceleration()
        {
            return 20f;
        }

        /// <summary>
        /// 获取跳跃力度
        /// </summary>
        private float GetJumpForce()
        {
            return 10f;
        }

        /// <summary>
        /// 检查是否在地面
        /// </summary>
        private bool IsGrounded(Vector3 position)
        {
            return position.y <= 0.1f;
        }

        /// <summary>
        /// 获取预测状态
        /// </summary>
        public PredictionState GetCurrentState()
        {
            return _currentState;
        }

        /// <summary>
        /// 获取权威状态
        /// </summary>
        public PredictionState GetAuthorityState()
        {
            return _authorityState;
        }

        /// <summary>
        /// 获取预测偏差
        /// </summary>
        public float GetPredictionDeviation()
        {
            return Vector3.Distance(_currentState.Position, _authorityState.Position);
        }

        /// <summary>
        /// 是否需要和解
        /// </summary>
        public bool NeedsReconciliation()
        {
            return _needsReconciliation;
        }

        /// <summary>
        /// 启用/禁用预测
        /// </summary>
        public void SetPredictionEnabled(bool enabled)
        {
            enablePrediction = enabled;

            if (!enabled)
            {
                // 清空预测数据
                _inputBuffer.Clear();
                _stateHistory.Clear();
            }
        }
    }

    /// <summary>
    /// 预测输入
    /// </summary>
    [Serializable]
    public struct PredictionInput
    {
        public Vector2 MovementInput;
        public bool[] ButtonInputs;
        public int Tick;
        public float Timestamp;

        public override string ToString()
        {
            return $"Input[Move: {MovementInput}, Tick: {Tick}, Time: {Timestamp:F3}]";
        }
    }

    /// <summary>
    /// 预测状态
    /// </summary>
    [Serializable]
    public struct PredictionState
    {
        public Vector3 Position;
        public Quaternion Rotation;
        public Vector3 Velocity;
        public int Tick;
        public float Timestamp;

        public override string ToString()
        {
            return $"State[Pos: {Position}, Vel: {Velocity}, Tick: {Tick}, Time: {Timestamp:F3}]";
        }
    }
}
