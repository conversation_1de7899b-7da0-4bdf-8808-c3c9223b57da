using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Synchronization
{
    /// <summary>
    /// 网络插值系统 - 平滑网络对象的移动和旋转
    /// </summary>
    public class NetworkInterpolation : MonoBehaviour
    {
        [Header("插值设置")]
        [SerializeField] private InterpolationMode interpolationMode = InterpolationMode.Linear;
        [SerializeField] private float interpolationTime = 0.1f;
        [SerializeField] private float extrapolationLimit = 0.5f;
        [SerializeField] private bool enableSmoothing = true;
        [SerializeField] private float smoothingFactor = 10f;

        [Header("位置插值")]
        [SerializeField] private bool interpolatePosition = true;
        [SerializeField] private float positionThreshold = 0.01f;
        [SerializeField] private float maxPositionError = 5f;

        [Header("旋转插值")]
        [SerializeField] private bool interpolateRotation = true;
        [SerializeField] private float rotationThreshold = 1f;
        [SerializeField] private float maxRotationError = 180f;

        [Header("缩放插值")]
        [SerializeField] private bool interpolateScale = false;
        [SerializeField] private float scaleThreshold = 0.01f;

        // 插值数据
        private Queue<NetworkSnapshot> _snapshots = new Queue<NetworkSnapshot>();
        private NetworkSnapshot _currentSnapshot;
        private NetworkSnapshot _targetSnapshot;
        private float _interpolationProgress;

        // 组件引用
        private Transform _transform;
        private NetworkBehaviour _networkBehaviour;

        // 插值状态
        private bool _isInterpolating;
        private float _lastUpdateTime;
        private Vector3 _velocity;
        private Vector3 _angularVelocity;

        // 性能优化
        private const int MAX_SNAPSHOTS = 10;
        private const float MIN_TIME_DELTA = 0.001f;

        public enum InterpolationMode
        {
            None,           // 无插值
            Linear,         // 线性插值
            Cubic,          // 三次插值
            Hermite,        // 埃尔米特插值
            Adaptive        // 自适应插值
        }

        private void Awake()
        {
            _transform = transform;
            _networkBehaviour = GetComponent<NetworkBehaviour>();

            InitializeInterpolation();
        }

        private void Update()
        {
            if (_isInterpolating && interpolationMode != InterpolationMode.None)
            {
                UpdateInterpolation();
            }
        }

        /// <summary>
        /// 初始化插值系统
        /// </summary>
        private void InitializeInterpolation()
        {
            _currentSnapshot = new NetworkSnapshot
            {
                Position = _transform.position,
                Rotation = _transform.rotation,
                Scale = _transform.localScale,
                Timestamp = Time.time
            };

            _targetSnapshot = _currentSnapshot;
            _lastUpdateTime = Time.time;
        }

        /// <summary>
        /// 添加网络快照
        /// </summary>
        public void AddSnapshot(Vector3 position, Quaternion rotation, Vector3 scale, float timestamp)
        {
            var snapshot = new NetworkSnapshot
            {
                Position = position,
                Rotation = rotation,
                Scale = scale,
                Timestamp = timestamp
            };

            AddSnapshot(snapshot);
        }

        /// <summary>
        /// 添加网络快照
        /// </summary>
        public void AddSnapshot(NetworkSnapshot snapshot)
        {
            // 验证时间戳
            if (snapshot.Timestamp <= _lastUpdateTime)
            {
                return; // 忽略过期的快照
            }

            // 添加到队列
            _snapshots.Enqueue(snapshot);

            // 限制队列大小
            while (_snapshots.Count > MAX_SNAPSHOTS)
            {
                _snapshots.Dequeue();
            }

            // 更新目标快照
            UpdateTargetSnapshot();
            _lastUpdateTime = snapshot.Timestamp;
        }

        /// <summary>
        /// 更新目标快照
        /// </summary>
        private void UpdateTargetSnapshot()
        {
            if (_snapshots.Count == 0) return;

            float currentTime = Time.time;
            float interpolationDelay = interpolationTime;
            float targetTime = currentTime - interpolationDelay;

            // 查找合适的快照进行插值
            NetworkSnapshot? fromSnapshot = null;
            NetworkSnapshot? toSnapshot = null;

            var snapshotArray = _snapshots.ToArray();

            for (int i = 0; i < snapshotArray.Length - 1; i++)
            {
                if (snapshotArray[i].Timestamp <= targetTime && snapshotArray[i + 1].Timestamp >= targetTime)
                {
                    fromSnapshot = snapshotArray[i];
                    toSnapshot = snapshotArray[i + 1];
                    break;
                }
            }

            // 如果没有找到合适的快照，使用最新的
            if (!fromSnapshot.HasValue || !toSnapshot.HasValue)
            {
                if (snapshotArray.Length > 0)
                {
                    _targetSnapshot = snapshotArray[snapshotArray.Length - 1];
                    _isInterpolating = true;
                }
                return;
            }

            // 计算插值参数
            float timeDelta = toSnapshot.Value.Timestamp - fromSnapshot.Value.Timestamp;
            if (timeDelta < MIN_TIME_DELTA) return;

            float t = (targetTime - fromSnapshot.Value.Timestamp) / timeDelta;
            t = Mathf.Clamp01(t);

            // 执行插值
            _targetSnapshot = InterpolateSnapshots(fromSnapshot.Value, toSnapshot.Value, t);
            _isInterpolating = true;
        }

        /// <summary>
        /// 插值两个快照
        /// </summary>
        private NetworkSnapshot InterpolateSnapshots(NetworkSnapshot from, NetworkSnapshot to, float t)
        {
            var result = new NetworkSnapshot();

            switch (interpolationMode)
            {
                case InterpolationMode.Linear:
                    result = LinearInterpolation(from, to, t);
                    break;

                case InterpolationMode.Cubic:
                    result = CubicInterpolation(from, to, t);
                    break;

                case InterpolationMode.Hermite:
                    result = HermiteInterpolation(from, to, t);
                    break;

                case InterpolationMode.Adaptive:
                    result = AdaptiveInterpolation(from, to, t);
                    break;

                default:
                    result = to;
                    break;
            }

            result.Timestamp = Mathf.Lerp(from.Timestamp, to.Timestamp, t);
            return result;
        }

        /// <summary>
        /// 线性插值
        /// </summary>
        private NetworkSnapshot LinearInterpolation(NetworkSnapshot from, NetworkSnapshot to, float t)
        {
            return new NetworkSnapshot
            {
                Position = Vector3.Lerp(from.Position, to.Position, t),
                Rotation = Quaternion.Lerp(from.Rotation, to.Rotation, t),
                Scale = Vector3.Lerp(from.Scale, to.Scale, t)
            };
        }

        /// <summary>
        /// 三次插值
        /// </summary>
        private NetworkSnapshot CubicInterpolation(NetworkSnapshot from, NetworkSnapshot to, float t)
        {
            // 使用平滑步函数
            float smoothT = t * t * (3f - 2f * t);

            return new NetworkSnapshot
            {
                Position = Vector3.Lerp(from.Position, to.Position, smoothT),
                Rotation = Quaternion.Lerp(from.Rotation, to.Rotation, smoothT),
                Scale = Vector3.Lerp(from.Scale, to.Scale, smoothT)
            };
        }

        /// <summary>
        /// 埃尔米特插值
        /// </summary>
        private NetworkSnapshot HermiteInterpolation(NetworkSnapshot from, NetworkSnapshot to, float t)
        {
            // 计算速度
            Vector3 velocity = (to.Position - from.Position) / (to.Timestamp - from.Timestamp);

            // 埃尔米特插值
            float t2 = t * t;
            float t3 = t2 * t;

            float h1 = 2 * t3 - 3 * t2 + 1;
            float h2 = -2 * t3 + 3 * t2;
            float h3 = t3 - 2 * t2 + t;
            float h4 = t3 - t2;

            Vector3 position = h1 * from.Position + h2 * to.Position + h3 * velocity * (to.Timestamp - from.Timestamp) + h4 * velocity * (to.Timestamp - from.Timestamp);

            return new NetworkSnapshot
            {
                Position = position,
                Rotation = Quaternion.Lerp(from.Rotation, to.Rotation, t),
                Scale = Vector3.Lerp(from.Scale, to.Scale, t)
            };
        }

        /// <summary>
        /// 自适应插值
        /// </summary>
        private NetworkSnapshot AdaptiveInterpolation(NetworkSnapshot from, NetworkSnapshot to, float t)
        {
            // 根据距离和时间差选择插值方法
            float distance = Vector3.Distance(from.Position, to.Position);
            float timeDelta = to.Timestamp - from.Timestamp;

            if (distance > 1f || timeDelta > 0.2f)
            {
                // 大距离或长时间间隔使用线性插值
                return LinearInterpolation(from, to, t);
            }
            else
            {
                // 小距离使用三次插值获得更平滑的效果
                return CubicInterpolation(from, to, t);
            }
        }

        /// <summary>
        /// 更新插值
        /// </summary>
        private void UpdateInterpolation()
        {
            if (!_isInterpolating) return;

            float deltaTime = Time.deltaTime;

            // 计算插值进度
            if (enableSmoothing)
            {
                // 平滑插值
                _interpolationProgress += deltaTime * smoothingFactor;
                _interpolationProgress = Mathf.Clamp01(_interpolationProgress);
            }
            else
            {
                // 直接设置到目标位置
                _interpolationProgress = 1f;
            }

            // 应用插值
            ApplyInterpolation(_interpolationProgress);

            // 检查是否完成插值
            if (_interpolationProgress >= 1f)
            {
                _currentSnapshot = _targetSnapshot;
                _interpolationProgress = 0f;
                _isInterpolating = false;
            }
        }

        /// <summary>
        /// 应用插值
        /// </summary>
        private void ApplyInterpolation(float t)
        {
            // 位置插值
            if (interpolatePosition)
            {
                // 使用extrapolationLimit限制外推范围
                float clampedT = Mathf.Clamp(t, 0f, 1f + extrapolationLimit);
                Vector3 newPosition = Vector3.Lerp(_currentSnapshot.Position, _targetSnapshot.Position, clampedT);

                // 检查位置误差
                float positionError = Vector3.Distance(newPosition, _targetSnapshot.Position);
                if (positionError > maxPositionError)
                {
                    // 误差过大，直接跳转
                    newPosition = _targetSnapshot.Position;
                }
                else if (positionError < positionThreshold)
                {
                    // 误差很小，直接设置到目标位置
                    newPosition = _targetSnapshot.Position;
                }

                _transform.position = newPosition;
            }

            // 旋转插值
            if (interpolateRotation)
            {
                Quaternion newRotation = Quaternion.Lerp(_currentSnapshot.Rotation, _targetSnapshot.Rotation, t);

                // 检查旋转误差
                float rotationError = Quaternion.Angle(newRotation, _targetSnapshot.Rotation);
                if (rotationError > maxRotationError)
                {
                    // 误差过大，直接跳转
                    newRotation = _targetSnapshot.Rotation;
                }
                else if (rotationError < rotationThreshold)
                {
                    // 误差很小，直接设置到目标旋转
                    newRotation = _targetSnapshot.Rotation;
                }

                _transform.rotation = newRotation;
            }

            // 缩放插值
            if (interpolateScale)
            {
                Vector3 newScale = Vector3.Lerp(_currentSnapshot.Scale, _targetSnapshot.Scale, t);

                // 检查缩放误差
                float scaleError = Vector3.Distance(newScale, _targetSnapshot.Scale);
                if (scaleError < scaleThreshold)
                {
                    newScale = _targetSnapshot.Scale;
                }

                _transform.localScale = newScale;
            }
        }

        /// <summary>
        /// 强制同步到指定状态
        /// </summary>
        public void ForceSync(Vector3 position, Quaternion rotation, Vector3 scale)
        {
            _currentSnapshot = new NetworkSnapshot
            {
                Position = position,
                Rotation = rotation,
                Scale = scale,
                Timestamp = Time.time
            };

            _targetSnapshot = _currentSnapshot;
            _transform.position = position;
            _transform.rotation = rotation;
            _transform.localScale = scale;

            _isInterpolating = false;
            _interpolationProgress = 0f;

            // 清空快照队列
            _snapshots.Clear();
        }

        /// <summary>
        /// 预测未来位置
        /// </summary>
        public Vector3 PredictPosition(float futureTime)
        {
            if (_snapshots.Count < 2) return _transform.position;

            // 计算速度
            var snapshotArray = _snapshots.ToArray();
            var latest = snapshotArray[snapshotArray.Length - 1];
            var previous = snapshotArray[snapshotArray.Length - 2];

            float timeDelta = latest.Timestamp - previous.Timestamp;
            if (timeDelta < MIN_TIME_DELTA) return latest.Position;

            Vector3 velocity = (latest.Position - previous.Position) / timeDelta;

            // 预测位置
            return latest.Position + velocity * futureTime;
        }

        /// <summary>
        /// 获取当前插值状态
        /// </summary>
        public InterpolationState GetInterpolationState()
        {
            return new InterpolationState
            {
                IsInterpolating = _isInterpolating,
                Progress = _interpolationProgress,
                CurrentSnapshot = _currentSnapshot,
                TargetSnapshot = _targetSnapshot,
                SnapshotCount = _snapshots.Count
            };
        }

        /// <summary>
        /// 设置插值模式
        /// </summary>
        public void SetInterpolationMode(InterpolationMode mode)
        {
            interpolationMode = mode;
        }

        /// <summary>
        /// 设置插值时间
        /// </summary>
        public void SetInterpolationTime(float time)
        {
            interpolationTime = Mathf.Max(0f, time);
        }

        /// <summary>
        /// 清空插值数据
        /// </summary>
        public void ClearInterpolationData()
        {
            _snapshots.Clear();
            _isInterpolating = false;
            _interpolationProgress = 0f;
        }
    }

    /// <summary>
    /// 网络快照
    /// </summary>
    [Serializable]
    public struct NetworkSnapshot
    {
        public Vector3 Position;
        public Quaternion Rotation;
        public Vector3 Scale;
        public float Timestamp;

        public override string ToString()
        {
            return $"Snapshot[Pos: {Position}, Rot: {Rotation.eulerAngles}, Time: {Timestamp:F3}]";
        }
    }

    /// <summary>
    /// 插值状态
    /// </summary>
    public struct InterpolationState
    {
        public bool IsInterpolating;
        public float Progress;
        public NetworkSnapshot CurrentSnapshot;
        public NetworkSnapshot TargetSnapshot;
        public int SnapshotCount;
    }
}
