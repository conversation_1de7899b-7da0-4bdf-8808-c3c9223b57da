using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;
using CustomNetworking.Synchronization;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 网络性能分析器 - 分析和优化网络性能
    /// </summary>
    public class NetworkPerformanceAnalyzer : MonoBehaviour
    {
        [Header("分析设置")]
        [SerializeField] private bool enableAnalysis = true;
        [SerializeField] private float analysisInterval = 1f;
        [SerializeField] private int sampleSize = 60; // 60秒的样本
        [SerializeField] private bool enableAutoOptimization = false;

        [Header("性能阈值")]
        [SerializeField] private float targetFPS = 60f;
        [SerializeField] private float maxLatency = 100f;
        [SerializeField] private float maxPacketLoss = 0.05f;
        [SerializeField] private float maxBandwidth = 50f; // KB/s

        [Header("优化设置")]
        [SerializeField] private bool enableLODOptimization = true;
        [SerializeField] private bool enableSyncRateOptimization = true;
        [SerializeField] private bool enableBandwidthOptimization = true;

        // 性能样本
        private Queue<NetworkPerformanceSample> _performanceSamples = new Queue<NetworkPerformanceSample>();
        private Queue<float> _fpsHistory = new Queue<float>();
        private Queue<float> _latencyHistory = new Queue<float>();
        private Queue<float> _bandwidthHistory = new Queue<float>();

        // 分析结果
        private PerformanceAnalysisResult _lastAnalysisResult;
        private List<PerformanceIssue> _detectedIssues = new List<PerformanceIssue>();
        private List<OptimizationSuggestion> _optimizationSuggestions = new List<OptimizationSuggestion>();

        // 组件引用
        private INetworkManager _networkManager;
        private NetworkSynchronizationManager _syncManager;
        private NetworkDebugManager _debugManager;

        // 分析状态
        private float _lastAnalysisTime;
        private bool _isAnalyzing;

        // 事件
        public event Action<PerformanceAnalysisResult> OnAnalysisCompleted;
        public event Action<PerformanceIssue> OnIssueDetected;
        public event Action<OptimizationSuggestion> OnOptimizationSuggested;

        private void Start()
        {
            InitializeAnalyzer();
        }

        private void Update()
        {
            if (enableAnalysis)
            {
                CollectPerformanceData();

                if (Time.time - _lastAnalysisTime >= analysisInterval)
                {
                    PerformAnalysis();
                    _lastAnalysisTime = Time.time;
                }
            }
        }

        /// <summary>
        /// 初始化分析器
        /// </summary>
        private void InitializeAnalyzer()
        {
            _networkManager = GetNetworkManager();
            _syncManager = NetworkSynchronizationManager.Instance;
            _debugManager = NetworkDebugManager.Instance;

            _lastAnalysisTime = Time.time;

            UnityEngine.Debug.Log("[NetworkAnalyzer] 网络性能分析器已初始化");
        }

        /// <summary>
        /// 收集性能数据
        /// </summary>
        private void CollectPerformanceData()
        {
            var sample = NetworkPerformanceSample.Create();

            // 获取网络诊断信息
            if (_networkManager != null)
            {
                var diagnostics = GetNetworkDiagnostics();
                if (diagnostics.HasValue)
                {
                    sample.Latency = diagnostics.Value.Latency;
                    sample.Bandwidth = diagnostics.Value.BandwidthUsage;
                    sample.PacketLoss = diagnostics.Value.PacketLoss;
                }
            }

            // 获取同步对象数量
            if (_syncManager != null)
            {
                sample.ActiveObjects = GetSyncObjectCount();
            }

            // 添加样本
            _performanceSamples.Enqueue(sample);
            _fpsHistory.Enqueue(sample.FPS);
            _latencyHistory.Enqueue(sample.Latency);
            _bandwidthHistory.Enqueue(sample.Bandwidth);

            // 限制样本数量
            while (_performanceSamples.Count > sampleSize)
            {
                _performanceSamples.Dequeue();
                _fpsHistory.Dequeue();
                _latencyHistory.Dequeue();
                _bandwidthHistory.Dequeue();
            }
        }

        /// <summary>
        /// 执行性能分析
        /// </summary>
        private void PerformAnalysis()
        {
            if (_isAnalyzing || _performanceSamples.Count < 10) return;

            _isAnalyzing = true;

            try
            {
                // 计算统计数据
                var analysisResult = CalculatePerformanceStatistics();

                // 检测性能问题
                DetectPerformanceIssues(analysisResult);

                // 生成优化建议
                GenerateOptimizationSuggestions(analysisResult);

                // 自动优化
                if (enableAutoOptimization)
                {
                    ApplyAutoOptimizations();
                }

                _lastAnalysisResult = analysisResult;
                OnAnalysisCompleted?.Invoke(analysisResult);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[NetworkAnalyzer] 性能分析失败: {ex.Message}");
            }
            finally
            {
                _isAnalyzing = false;
            }
        }

        /// <summary>
        /// 计算性能统计
        /// </summary>
        private PerformanceAnalysisResult CalculatePerformanceStatistics()
        {
            var samples = _performanceSamples.ToArray();

            var result = new PerformanceAnalysisResult
            {
                Timestamp = DateTime.Now,
                SampleCount = samples.Length,

                // FPS统计
                AverageFPS = samples.Average(s => s.FPS),
                MinFPS = samples.Min(s => s.FPS),
                MaxFPS = samples.Max(s => s.FPS),
                FPSVariance = CalculateVariance(_fpsHistory.ToArray()),

                // 延迟统计
                AverageLatency = samples.Average(s => s.Latency),
                MinLatency = samples.Min(s => s.Latency),
                MaxLatency = samples.Max(s => s.Latency),
                LatencyVariance = CalculateVariance(_latencyHistory.ToArray()),

                // 带宽统计
                AverageBandwidth = samples.Average(s => s.Bandwidth),
                MinBandwidth = samples.Min(s => s.Bandwidth),
                MaxBandwidth = samples.Max(s => s.Bandwidth),
                BandwidthVariance = CalculateVariance(_bandwidthHistory.ToArray()),

                // 其他统计
                AveragePacketLoss = samples.Average(s => s.PacketLoss),
                AverageActiveObjects = (int)samples.Average(s => s.ActiveObjects)
            };

            // 计算性能评分
            result.PerformanceScore = CalculatePerformanceScore(result);

            return result;
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        private float CalculateVariance(float[] values)
        {
            if (values.Length < 2) return 0f;

            float mean = values.Average();
            float variance = values.Sum(v => (v - mean) * (v - mean)) / values.Length;

            return variance;
        }

        /// <summary>
        /// 计算性能评分
        /// </summary>
        private float CalculatePerformanceScore(PerformanceAnalysisResult result)
        {
            float score = 100f;

            // FPS评分 (40%)
            float fpsScore = Mathf.Clamp01(result.AverageFPS / targetFPS) * 40f;

            // 延迟评分 (30%)
            float latencyScore = Mathf.Clamp01(1f - (result.AverageLatency / maxLatency)) * 30f;

            // 丢包率评分 (20%)
            float packetLossScore = Mathf.Clamp01(1f - (result.AveragePacketLoss / maxPacketLoss)) * 20f;

            // 带宽评分 (10%)
            float bandwidthScore = Mathf.Clamp01(1f - (result.AverageBandwidth / maxBandwidth)) * 10f;

            score = fpsScore + latencyScore + packetLossScore + bandwidthScore;

            return Mathf.Clamp(score, 0f, 100f);
        }

        /// <summary>
        /// 检测性能问题
        /// </summary>
        private void DetectPerformanceIssues(PerformanceAnalysisResult result)
        {
            _detectedIssues.Clear();

            // 检测FPS问题
            if (result.AverageFPS < targetFPS * 0.8f)
            {
                var issue = new PerformanceIssue
                {
                    Type = IssueType.LowFPS,
                    Severity = result.AverageFPS < targetFPS * 0.5f ? IssueSeverity.Critical : IssueSeverity.High,
                    Description = $"平均FPS过低: {result.AverageFPS:F1} (目标: {targetFPS})",
                    DetectedAt = DateTime.Now
                };

                _detectedIssues.Add(issue);
                OnIssueDetected?.Invoke(issue);
            }

            // 检测延迟问题
            if (result.AverageLatency > maxLatency)
            {
                var issue = new PerformanceIssue
                {
                    Type = IssueType.HighLatency,
                    Severity = result.AverageLatency > maxLatency * 2f ? IssueSeverity.Critical : IssueSeverity.High,
                    Description = $"平均延迟过高: {result.AverageLatency:F1}ms (最大: {maxLatency}ms)",
                    DetectedAt = DateTime.Now
                };

                _detectedIssues.Add(issue);
                OnIssueDetected?.Invoke(issue);
            }

            // 检测丢包问题
            if (result.AveragePacketLoss > maxPacketLoss)
            {
                var issue = new PerformanceIssue
                {
                    Type = IssueType.PacketLoss,
                    Severity = result.AveragePacketLoss > maxPacketLoss * 2f ? IssueSeverity.Critical : IssueSeverity.Medium,
                    Description = $"丢包率过高: {result.AveragePacketLoss * 100:F1}% (最大: {maxPacketLoss * 100:F1}%)",
                    DetectedAt = DateTime.Now
                };

                _detectedIssues.Add(issue);
                OnIssueDetected?.Invoke(issue);
            }

            // 检测带宽问题
            if (result.AverageBandwidth > maxBandwidth)
            {
                var issue = new PerformanceIssue
                {
                    Type = IssueType.HighBandwidth,
                    Severity = IssueSeverity.Medium,
                    Description = $"带宽使用过高: {result.AverageBandwidth:F1} KB/s (最大: {maxBandwidth} KB/s)",
                    DetectedAt = DateTime.Now
                };

                _detectedIssues.Add(issue);
                OnIssueDetected?.Invoke(issue);
            }

            // 检测FPS波动问题
            if (result.FPSVariance > 100f)
            {
                var issue = new PerformanceIssue
                {
                    Type = IssueType.FPSInstability,
                    Severity = IssueSeverity.Medium,
                    Description = $"FPS不稳定，方差: {result.FPSVariance:F1}",
                    DetectedAt = DateTime.Now
                };

                _detectedIssues.Add(issue);
                OnIssueDetected?.Invoke(issue);
            }
        }

        /// <summary>
        /// 生成优化建议
        /// </summary>
        private void GenerateOptimizationSuggestions(PerformanceAnalysisResult result)
        {
            _optimizationSuggestions.Clear();

            foreach (var issue in _detectedIssues)
            {
                switch (issue.Type)
                {
                    case IssueType.LowFPS:
                        _optimizationSuggestions.Add(new OptimizationSuggestion
                        {
                            Type = OptimizationType.ReduceSyncRate,
                            Priority = OptimizationPriority.High,
                            Description = "降低同步频率以提高FPS",
                            ExpectedImprovement = "FPS +10-20%"
                        });
                        break;

                    case IssueType.HighLatency:
                        _optimizationSuggestions.Add(new OptimizationSuggestion
                        {
                            Type = OptimizationType.OptimizeInterpolation,
                            Priority = OptimizationPriority.High,
                            Description = "优化插值设置以减少延迟感知",
                            ExpectedImprovement = "延迟感知 -20-30%"
                        });
                        break;

                    case IssueType.HighBandwidth:
                        // 使用enableBandwidthOptimization字段控制带宽优化建议
                        if (enableBandwidthOptimization)
                        {
                            _optimizationSuggestions.Add(new OptimizationSuggestion
                            {
                                Type = OptimizationType.EnableLOD,
                                Priority = OptimizationPriority.Medium,
                                Description = "启用LOD系统以减少带宽使用",
                                ExpectedImprovement = "带宽 -30-50%"
                            });
                        }
                        break;

                    case IssueType.PacketLoss:
                        _optimizationSuggestions.Add(new OptimizationSuggestion
                        {
                            Type = OptimizationType.IncreaseReliability,
                            Priority = OptimizationPriority.High,
                            Description = "增加关键数据的可靠性传输",
                            ExpectedImprovement = "数据完整性 +90%"
                        });
                        break;
                }
            }

            // 通知优化建议
            foreach (var suggestion in _optimizationSuggestions)
            {
                OnOptimizationSuggested?.Invoke(suggestion);
            }
        }

        /// <summary>
        /// 应用自动优化
        /// </summary>
        private void ApplyAutoOptimizations()
        {
            foreach (var suggestion in _optimizationSuggestions)
            {
                if (suggestion.Priority == OptimizationPriority.High)
                {
                    ApplyOptimization(suggestion);
                }
            }
        }

        /// <summary>
        /// 应用优化
        /// </summary>
        private void ApplyOptimization(OptimizationSuggestion suggestion)
        {
            switch (suggestion.Type)
            {
                case OptimizationType.ReduceSyncRate:
                    if (enableSyncRateOptimization && _syncManager != null)
                    {
                        // 降低同步频率
                        UnityEngine.Debug.Log("[NetworkAnalyzer] 应用优化: 降低同步频率");
                    }
                    break;

                case OptimizationType.EnableLOD:
                    if (enableLODOptimization && _syncManager != null)
                    {
                        // 启用LOD系统
                        UnityEngine.Debug.Log("[NetworkAnalyzer] 应用优化: 启用LOD系统");
                    }
                    break;

                case OptimizationType.OptimizeInterpolation:
                    // 优化插值设置
                    UnityEngine.Debug.Log("[NetworkAnalyzer] 应用优化: 优化插值设置");
                    break;

                case OptimizationType.IncreaseReliability:
                    // 增加可靠性
                    UnityEngine.Debug.Log("[NetworkAnalyzer] 应用优化: 增加传输可靠性");
                    break;
            }
        }

        /// <summary>
        /// 获取最新分析结果
        /// </summary>
        public PerformanceAnalysisResult GetLatestAnalysisResult()
        {
            return _lastAnalysisResult;
        }

        /// <summary>
        /// 获取检测到的问题
        /// </summary>
        public List<PerformanceIssue> GetDetectedIssues()
        {
            return new List<PerformanceIssue>(_detectedIssues);
        }

        /// <summary>
        /// 获取优化建议
        /// </summary>
        public List<OptimizationSuggestion> GetOptimizationSuggestions()
        {
            return new List<OptimizationSuggestion>(_optimizationSuggestions);
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        public string GeneratePerformanceReport()
        {
            var report = new System.Text.StringBuilder();

            report.AppendLine("=== 网络性能分析报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"样本数量: {_lastAnalysisResult.SampleCount}");
            report.AppendLine($"性能评分: {_lastAnalysisResult.PerformanceScore:F1}/100");
            report.AppendLine();

            report.AppendLine("性能统计:");
            report.AppendLine($"  平均FPS: {_lastAnalysisResult.AverageFPS:F1} (范围: {_lastAnalysisResult.MinFPS:F1} - {_lastAnalysisResult.MaxFPS:F1})");
            report.AppendLine($"  平均延迟: {_lastAnalysisResult.AverageLatency:F1}ms (范围: {_lastAnalysisResult.MinLatency:F1} - {_lastAnalysisResult.MaxLatency:F1}ms)");
            report.AppendLine($"  平均带宽: {_lastAnalysisResult.AverageBandwidth:F1} KB/s");
            report.AppendLine($"  平均丢包率: {_lastAnalysisResult.AveragePacketLoss * 100:F1}%");
            report.AppendLine();

            if (_detectedIssues.Count > 0)
            {
                report.AppendLine("检测到的问题:");
                foreach (var issue in _detectedIssues)
                {
                    report.AppendLine($"  [{issue.Severity}] {issue.Description}");
                }
                report.AppendLine();
            }

            if (_optimizationSuggestions.Count > 0)
            {
                report.AppendLine("优化建议:");
                foreach (var suggestion in _optimizationSuggestions)
                {
                    report.AppendLine($"  [{suggestion.Priority}] {suggestion.Description} (预期改善: {suggestion.ExpectedImprovement})");
                }
            }

            return report.ToString();
        }

        /// <summary>
        /// 重置分析器
        /// </summary>
        public void ResetAnalyzer()
        {
            _performanceSamples.Clear();
            _fpsHistory.Clear();
            _latencyHistory.Clear();
            _bandwidthHistory.Clear();
            _detectedIssues.Clear();
            _optimizationSuggestions.Clear();

            UnityEngine.Debug.Log("[NetworkAnalyzer] 分析器已重置");
        }

        /// <summary>
        /// 获取网络诊断信息
        /// </summary>
        private CustomNetworking.ErrorHandling.NetworkDiagnostics? GetNetworkDiagnostics()
        {
            if (_networkManager == null)
                return null;

            try
            {
                return _networkManager.GetNetworkDiagnostics();
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogWarning($"Failed to get network diagnostics: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取同步对象数量
        /// </summary>
        private int GetSyncObjectCount()
        {
            if (_syncManager == null)
                return 0;

            try
            {
                return _syncManager.GetSyncObjectCount();
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogWarning($"Failed to get sync object count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取网络管理器实例
        /// </summary>
        private INetworkManager GetNetworkManager()
        {
            // 查找实现了INetworkManager接口的组件
            var allComponents = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None); // 使用FindObjectsByType替代已弃用的FindObjectsOfType
            foreach (var component in allComponents)
            {
                if (component is INetworkManager networkManager)
                {
                    return networkManager;
                }
            }
            return null;
        }
    }

    #region 数据结构定义

    /// <summary>
    /// 性能分析结果
    /// </summary>
    [Serializable]
    public class PerformanceAnalysisResult
    {
        public DateTime Timestamp;
        public int SampleCount;
        public float PerformanceScore;

        // FPS统计
        public float AverageFPS;
        public float MinFPS;
        public float MaxFPS;
        public float FPSVariance;

        // 延迟统计
        public float AverageLatency;
        public float MinLatency;
        public float MaxLatency;
        public float LatencyVariance;

        // 带宽统计
        public float AverageBandwidth;
        public float MinBandwidth;
        public float MaxBandwidth;
        public float BandwidthVariance;

        // 其他统计
        public float AveragePacketLoss;
        public int AverageActiveObjects;
    }

    /// <summary>
    /// 性能问题
    /// </summary>
    [Serializable]
    public class PerformanceIssue
    {
        public IssueType Type;
        public IssueSeverity Severity;
        public string Description;
        public DateTime DetectedAt;
    }

    /// <summary>
    /// 优化建议
    /// </summary>
    [Serializable]
    public class OptimizationSuggestion
    {
        public OptimizationType Type;
        public OptimizationPriority Priority;
        public string Description;
        public string ExpectedImprovement;
    }

    /// <summary>
    /// 问题类型
    /// </summary>
    public enum IssueType
    {
        LowFPS,
        HighLatency,
        PacketLoss,
        HighBandwidth,
        FPSInstability
    }

    /// <summary>
    /// 问题严重程度
    /// </summary>
    public enum IssueSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 优化类型
    /// </summary>
    public enum OptimizationType
    {
        ReduceSyncRate,
        EnableLOD,
        OptimizeInterpolation,
        IncreaseReliability
    }

    /// <summary>
    /// 优化优先级
    /// </summary>
    public enum OptimizationPriority
    {
        Low,
        Medium,
        High
    }



    #endregion
}
