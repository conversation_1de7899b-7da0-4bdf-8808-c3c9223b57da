using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 网络组件验证器 - 确保所有网络对象和组件正确配置
    /// </summary>
    public class NetworkComponentValidator : MonoBehaviour
    {
        [Header("验证设置")]
        [SerializeField] private bool validateOnStart = true;
        [SerializeField] private bool showDetailedLogs = true;
        [SerializeField] private bool autoFixIssues = true;

        private List<ValidationResult> _validationResults = new List<ValidationResult>();

        private void Start()
        {
            if (validateOnStart)
            {
                ValidateAllNetworkComponents();
            }
        }

        /// <summary>
        /// 验证所有网络组件
        /// </summary>
        [ContextMenu("验证网络组件")]
        public void ValidateAllNetworkComponents()
        {
            _validationResults.Clear();

            Debug.Log("[NetworkValidator] 开始验证网络组件...");

            // 查找所有NetworkBehaviour组件 - 使用FindObjectsByType替代已弃用的FindObjectsOfType
            var networkBehaviours = FindObjectsByType<NetworkBehaviour>(FindObjectsSortMode.None);
            Debug.Log($"[NetworkValidator] 找到 {networkBehaviours.Length} 个NetworkBehaviour组件");

            foreach (var behaviour in networkBehaviours)
            {
                ValidateNetworkBehaviour(behaviour);
            }

            // 查找所有NetworkObject组件 - 使用FindObjectsByType替代已弃用的FindObjectsOfType
            var networkObjects = FindObjectsByType<NetworkObject>(FindObjectsSortMode.None);
            Debug.Log($"[NetworkValidator] 找到 {networkObjects.Length} 个NetworkObject组件");

            foreach (var networkObject in networkObjects)
            {
                ValidateNetworkObject(networkObject);
            }

            // 输出验证结果
            OutputValidationResults();
        }

        /// <summary>
        /// 验证NetworkBehaviour组件
        /// </summary>
        private void ValidateNetworkBehaviour(NetworkBehaviour behaviour)
        {
            var result = new ValidationResult
            {
                GameObject = behaviour.gameObject,
                ComponentType = behaviour.GetType().Name,
                Issues = new List<string>()
            };

            // 检查是否有NetworkObject组件
            var networkObject = behaviour.GetComponent<NetworkObject>();
            if (networkObject == null)
            {
                result.Issues.Add("缺少NetworkObject组件");

                if (autoFixIssues)
                {
                    behaviour.gameObject.AddComponent<NetworkObject>();
                    result.Issues.Add("已自动添加NetworkObject组件");
                }
            }

            // 检查是否正确注册到NetworkObject
            if (networkObject != null && !networkObject.HasBehaviour(behaviour))
            {
                result.Issues.Add("未正确注册到NetworkObject");
            }

            // 检查网络属性
            ValidateNetworkedProperties(behaviour, result);

            if (result.Issues.Count > 0)
            {
                _validationResults.Add(result);
            }
        }

        /// <summary>
        /// 验证NetworkObject组件
        /// </summary>
        private void ValidateNetworkObject(NetworkObject networkObject)
        {
            var result = new ValidationResult
            {
                GameObject = networkObject.gameObject,
                ComponentType = "NetworkObject",
                Issues = new List<string>()
            };

            // 检查是否有NetworkBehaviour组件
            var behaviours = networkObject.GetComponents<NetworkBehaviour>();
            if (behaviours.Length == 0)
            {
                result.Issues.Add("没有NetworkBehaviour组件");
            }

            // 检查ID是否有效
            if (!networkObject.Id.IsValid)
            {
                result.Issues.Add("NetworkId无效");
            }

            // 检查Runner引用
            if (networkObject.Runner == null)
            {
                result.Issues.Add("NetworkRunner引用为空");
            }

            if (result.Issues.Count > 0)
            {
                _validationResults.Add(result);
            }
        }

        /// <summary>
        /// 验证网络属性
        /// </summary>
        private void ValidateNetworkedProperties(NetworkBehaviour behaviour, ValidationResult result)
        {
            var type = behaviour.GetType();
            var properties = type.GetProperties();

            int networkedPropertyCount = 0;

            foreach (var property in properties)
            {
                var networkedAttr = property.GetCustomAttributes(typeof(NetworkedAttribute), true);
                if (networkedAttr.Length > 0)
                {
                    networkedPropertyCount++;

                    // 检查属性类型是否支持网络同步
                    if (!IsNetworkSupportedType(property.PropertyType))
                    {
                        result.Issues.Add($"属性 {property.Name} 的类型 {property.PropertyType.Name} 不支持网络同步");
                    }
                }
            }

            if (showDetailedLogs && networkedPropertyCount > 0)
            {
                Debug.Log($"[NetworkValidator] {behaviour.GetType().Name} 有 {networkedPropertyCount} 个网络属性");
            }
        }

        /// <summary>
        /// 检查类型是否支持网络同步
        /// </summary>
        private bool IsNetworkSupportedType(System.Type type)
        {
            // 基本类型
            if (type.IsPrimitive || type == typeof(string))
                return true;

            // Unity类型
            if (type == typeof(Vector3) || type == typeof(Vector2) || type == typeof(Quaternion) ||
                type == typeof(Color) || type == typeof(Color32))
                return true;

            // 自定义网络类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(NetworkString<>))
                return true;

            if (type == typeof(NetworkBool) || type == typeof(PlayerRef) || type == typeof(TickTimer))
                return true;

            return false;
        }

        /// <summary>
        /// 输出验证结果
        /// </summary>
        private void OutputValidationResults()
        {
            if (_validationResults.Count == 0)
            {
                Debug.Log("[NetworkValidator] ✅ 所有网络组件验证通过！");
                return;
            }

            Debug.LogWarning($"[NetworkValidator] ⚠️ 发现 {_validationResults.Count} 个组件存在问题：");

            foreach (var result in _validationResults)
            {
                string issues = string.Join(", ", result.Issues);
                Debug.LogWarning($"[NetworkValidator] {result.GameObject.name} ({result.ComponentType}): {issues}", result.GameObject);
            }
        }

        /// <summary>
        /// 修复所有发现的问题
        /// </summary>
        [ContextMenu("修复网络组件问题")]
        public void FixAllIssues()
        {
            autoFixIssues = true;
            ValidateAllNetworkComponents();
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    [System.Serializable]
    public class ValidationResult
    {
        public GameObject GameObject;
        public string ComponentType;
        public List<string> Issues;
    }
}

// NetworkObject扩展方法
namespace CustomNetworking.Core
{
    public static class NetworkObjectExtensions
    {
        /// <summary>
        /// 检查是否包含指定的NetworkBehaviour
        /// </summary>
        public static bool HasBehaviour(this NetworkObject networkObject, NetworkBehaviour behaviour)
        {
            var behaviours = networkObject.GetComponents<NetworkBehaviour>();
            foreach (var b in behaviours)
            {
                if (b == behaviour)
                    return true;
            }
            return false;
        }
    }
}
