using System;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;
using CustomNetworking.Synchronization;

namespace CustomNetworking.Debug
{
    /// <summary>
    /// 网络调试管理器 - 提供网络状态监控和调试功能
    /// </summary>
    public class NetworkDebugManager : MonoBehaviour
    {
        [Header("调试设置")]
        [SerializeField] private bool enableDebugUI = true;
        [SerializeField] private bool enableConsoleLogging = true;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private bool enableNetworkVisualization = true;

        [Header("UI设置")]
        [SerializeField] private KeyCode toggleKey = KeyCode.F1;
        [SerializeField] private bool showOnStart = false;
        [SerializeField] private float uiScale = 1f;
        [SerializeField] private Color backgroundColor = new Color(0, 0, 0, 0.8f);

        [Header("监控设置")]
        [SerializeField] private float updateInterval = 0.5f;
        [SerializeField] private int maxLogEntries = 100;
        [SerializeField] private int maxPerformanceSamples = 60;

        // 调试状态
        private bool _showDebugUI;
        private Vector2 _scrollPosition;
        private int _selectedTab;
        private string[] _tabNames = { "状态", "性能", "日志", "对象", "网络" };

        // 网络组件引用
        private INetworkManager _networkManager;
        private NetworkErrorHandler _errorHandler;
        private NetworkSynchronizationManager _syncManager;

        // 调试数据
        private List<DebugLogEntry> _logEntries = new List<DebugLogEntry>();
        private Queue<float> _fpsHistory = new Queue<float>();
        private Queue<float> _latencyHistory = new Queue<float>();
        private Queue<float> _bandwidthHistory = new Queue<float>();
        private NetworkDebugStats _debugStats;

        // 性能监控
        private float _lastUpdateTime;
        private float _frameCount;
        private float _currentFPS;

        // 单例实例
        private static NetworkDebugManager _instance;
        public static NetworkDebugManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<NetworkDebugManager>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("NetworkDebugManager");
                        _instance = go.AddComponent<NetworkDebugManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeDebugManager();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            _showDebugUI = showOnStart;
        }

        private void Update()
        {
            HandleInput();
            UpdatePerformanceMonitoring();
            UpdateDebugStats();
        }

        private void OnGUI()
        {
            if (_showDebugUI && enableDebugUI)
            {
                DrawDebugUI();
            }
        }

        /// <summary>
        /// 初始化调试管理器
        /// </summary>
        private void InitializeDebugManager()
        {
            // 获取网络组件引用
            _networkManager = GetNetworkManager();
            _errorHandler = NetworkErrorHandler.Instance;
            _syncManager = NetworkSynchronizationManager.Instance;

            // 订阅事件
            if (_errorHandler != null)
            {
                _errorHandler.OnErrorOccurred += OnNetworkError;
                _errorHandler.OnConnectionLost += OnConnectionLost;
                _errorHandler.OnConnectionRestored += OnConnectionRestored;
            }

            // 初始化调试统计
            _debugStats = new NetworkDebugStats();
            _lastUpdateTime = Time.time;

            // 初始化网络可视化 - 使用enableNetworkVisualization字段
            if (enableNetworkVisualization)
            {
                var visualizer = FindFirstObjectByType<NetworkVisualizer>();
                if (visualizer != null)
                {
                    visualizer.enabled = true;
                }
            }

            LogDebug("网络调试管理器已初始化", DebugLogType.Info);
        }

        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            try
            {
                // 尝试使用旧的Input系统
                if (Input.GetKeyDown(toggleKey))
                {
                    ToggleDebugUI();
                }
            }
            catch (System.InvalidOperationException)
            {
                // 如果项目使用新的Input System，则跳过输入处理
                // 在实际项目中，这里应该使用新的Input System API
                // 为了测试兼容性，我们暂时忽略输入处理
            }
        }

        /// <summary>
        /// 更新性能监控
        /// </summary>
        private void UpdatePerformanceMonitoring()
        {
            if (!enablePerformanceMonitoring) return;

            _frameCount++;
            float currentTime = Time.time;

            if (currentTime - _lastUpdateTime >= updateInterval)
            {
                // 计算FPS
                _currentFPS = _frameCount / (currentTime - _lastUpdateTime);
                _frameCount = 0;
                _lastUpdateTime = currentTime;

                // 更新历史数据
                UpdatePerformanceHistory();
            }
        }

        /// <summary>
        /// 更新性能历史
        /// </summary>
        private void UpdatePerformanceHistory()
        {
            // FPS历史
            _fpsHistory.Enqueue(_currentFPS);
            if (_fpsHistory.Count > maxPerformanceSamples)
            {
                _fpsHistory.Dequeue();
            }

            // 延迟历史
            if (_networkManager != null)
            {
                var diagnostics = GetNetworkDiagnostics();
                if (diagnostics.HasValue)
                {
                    _latencyHistory.Enqueue(diagnostics.Value.Latency);
                    _bandwidthHistory.Enqueue(diagnostics.Value.BandwidthUsage);

                    if (_latencyHistory.Count > maxPerformanceSamples)
                    {
                        _latencyHistory.Dequeue();
                    }

                    if (_bandwidthHistory.Count > maxPerformanceSamples)
                    {
                        _bandwidthHistory.Dequeue();
                    }
                }
            }
        }

        /// <summary>
        /// 更新调试统计
        /// </summary>
        private void UpdateDebugStats()
        {
            _debugStats.CurrentFPS = _currentFPS;
            _debugStats.TotalLogEntries = _logEntries.Count;

            if (_networkManager != null)
            {
                var diagnostics = GetNetworkDiagnostics();
                if (diagnostics.HasValue)
                {
                    _debugStats.CurrentLatency = diagnostics.Value.Latency;
                    _debugStats.CurrentBandwidth = diagnostics.Value.BandwidthUsage;
                    _debugStats.PacketLoss = diagnostics.Value.PacketLoss;
                    _debugStats.ConnectionState = diagnostics.Value.State.ToString();
                    _debugStats.NetworkQuality = diagnostics.Value.Quality.ToString();
                }
            }

            if (_syncManager != null)
            {
                var syncStats = _syncManager.GetPerformanceStats();
                _debugStats.SyncedObjects = syncStats.SyncedObjectsCount;
                _debugStats.SyncRate = syncStats.SyncRate;
            }
        }

        /// <summary>
        /// 绘制调试UI
        /// </summary>
        private void DrawDebugUI()
        {
            // 设置UI缩放
            Matrix4x4 oldMatrix = GUI.matrix;
            GUI.matrix = Matrix4x4.Scale(Vector3.one * uiScale);

            // 主窗口
            Rect windowRect = new Rect(10, 10, 600, 400);
            GUI.Box(windowRect, "", GUI.skin.window);

            GUILayout.BeginArea(windowRect);

            // 标题栏
            GUILayout.BeginHorizontal();
            GUILayout.Label("网络调试工具", GUI.skin.label);
            GUILayout.FlexibleSpace();
            if (GUILayout.Button("X", GUILayout.Width(20)))
            {
                _showDebugUI = false;
            }
            GUILayout.EndHorizontal();

            // 标签页
            _selectedTab = GUILayout.Toolbar(_selectedTab, _tabNames);

            // 内容区域
            _scrollPosition = GUILayout.BeginScrollView(_scrollPosition);

            switch (_selectedTab)
            {
                case 0: DrawStatusTab(); break;
                case 1: DrawPerformanceTab(); break;
                case 2: DrawLogTab(); break;
                case 3: DrawObjectsTab(); break;
                case 4: DrawNetworkTab(); break;
            }

            GUILayout.EndScrollView();
            GUILayout.EndArea();

            // 恢复UI缩放
            GUI.matrix = oldMatrix;
        }

        /// <summary>
        /// 绘制状态标签页
        /// </summary>
        private void DrawStatusTab()
        {
            GUILayout.Label("网络状态", GUI.skin.label);

            GUILayout.BeginVertical("box");
            GUILayout.Label($"连接状态: {_debugStats.ConnectionState}");
            GUILayout.Label($"网络质量: {_debugStats.NetworkQuality}");
            GUILayout.Label($"当前延迟: {_debugStats.CurrentLatency:F1}ms");
            GUILayout.Label($"丢包率: {_debugStats.PacketLoss * 100:F1}%");
            GUILayout.Label($"带宽使用: {_debugStats.CurrentBandwidth:F1} KB/s");
            GUILayout.EndVertical();

            GUILayout.Label("同步状态", GUI.skin.label);

            GUILayout.BeginVertical("box");
            GUILayout.Label($"同步对象: {_debugStats.SyncedObjects}");
            GUILayout.Label($"同步率: {_debugStats.SyncRate:F1}/s");
            GUILayout.Label($"当前FPS: {_debugStats.CurrentFPS:F1}");
            GUILayout.EndVertical();

            // 控制按钮
            GUILayout.Label("调试控制", GUI.skin.label);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("清除日志"))
            {
                ClearLogs();
            }
            if (GUILayout.Button("强制GC"))
            {
                System.GC.Collect();
                LogDebug("手动执行垃圾回收", DebugLogType.Info);
            }
            GUILayout.EndHorizontal();
        }

        /// <summary>
        /// 绘制性能标签页
        /// </summary>
        private void DrawPerformanceTab()
        {
            GUILayout.Label("性能监控", GUI.skin.label);

            // FPS图表
            GUILayout.Label($"FPS: {_debugStats.CurrentFPS:F1}");
            DrawPerformanceGraph(_fpsHistory, "FPS", 0, 120, Color.green);

            // 延迟图表
            GUILayout.Label($"延迟: {_debugStats.CurrentLatency:F1}ms");
            DrawPerformanceGraph(_latencyHistory, "延迟", 0, 500, Color.yellow);

            // 带宽图表
            GUILayout.Label($"带宽: {_debugStats.CurrentBandwidth:F1} KB/s");
            DrawPerformanceGraph(_bandwidthHistory, "带宽", 0, 100, Color.blue);

            // 内存信息
            GUILayout.Label("内存使用", GUI.skin.label);
            GUILayout.BeginVertical("box");
            GUILayout.Label($"总内存: {SystemInfo.systemMemorySize} MB");
            GUILayout.Label($"已用内存: {System.GC.GetTotalMemory(false) / 1024 / 1024} MB");
            GUILayout.Label($"Unity堆内存: {UnityEngine.Profiling.Profiler.GetTotalAllocatedMemoryLong() / 1024 / 1024} MB"); // 使用GetTotalAllocatedMemoryLong替代已弃用的GetTotalAllocatedMemory
            GUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制性能图表
        /// </summary>
        private void DrawPerformanceGraph(Queue<float> data, string label, float minValue, float maxValue, Color color)
        {
            if (data.Count < 2) return;

            Rect graphRect = GUILayoutUtility.GetRect(500, 60);
            GUI.Box(graphRect, "");

            var dataArray = data.ToArray();
            Vector2[] points = new Vector2[dataArray.Length];

            for (int i = 0; i < dataArray.Length; i++)
            {
                float x = graphRect.x + (i / (float)(dataArray.Length - 1)) * graphRect.width;
                float normalizedValue = Mathf.InverseLerp(minValue, maxValue, dataArray[i]);
                float y = graphRect.y + graphRect.height - (normalizedValue * graphRect.height);
                points[i] = new Vector2(x, y);
            }

            // 绘制线条
            for (int i = 0; i < points.Length - 1; i++)
            {
                DrawLine(points[i], points[i + 1], color);
            }
        }

        /// <summary>
        /// 绘制日志标签页
        /// </summary>
        private void DrawLogTab()
        {
            GUILayout.Label($"调试日志 ({_logEntries.Count})", GUI.skin.label);

            // 日志过滤
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("全部"))
            {
                // 显示全部日志
            }
            if (GUILayout.Button("错误"))
            {
                // 只显示错误日志
            }
            if (GUILayout.Button("警告"))
            {
                // 只显示警告日志
            }
            if (GUILayout.Button("信息"))
            {
                // 只显示信息日志
            }
            GUILayout.EndHorizontal();

            // 日志列表
            foreach (var entry in _logEntries)
            {
                Color originalColor = GUI.color;

                switch (entry.Type)
                {
                    case DebugLogType.Error:
                        GUI.color = Color.red;
                        break;
                    case DebugLogType.Warning:
                        GUI.color = Color.yellow;
                        break;
                    case DebugLogType.Info:
                        GUI.color = Color.white;
                        break;
                }

                GUILayout.BeginVertical("box");
                GUILayout.Label($"[{entry.Timestamp:HH:mm:ss}] {entry.Message}");
                GUILayout.EndVertical();

                GUI.color = originalColor;
            }
        }

        /// <summary>
        /// 绘制对象标签页
        /// </summary>
        private void DrawObjectsTab()
        {
            GUILayout.Label("网络对象", GUI.skin.label);

            var networkObjects = FindObjectsByType<NetworkObject>(FindObjectsSortMode.None); // 使用FindObjectsByType替代已弃用的FindObjectsOfType

            GUILayout.Label($"总数: {networkObjects.Length}");

            foreach (var obj in networkObjects)
            {
                GUILayout.BeginVertical("box");
                GUILayout.Label($"名称: {obj.name}");
                GUILayout.Label($"ID: {obj.Id}");
                GUILayout.Label($"权限: {(obj.HasInputAuthority ? "本地" : "远程")}");
                GUILayout.Label($"位置: {obj.transform.position}");
                GUILayout.EndVertical();
            }
        }

        /// <summary>
        /// 绘制网络标签页
        /// </summary>
        private void DrawNetworkTab()
        {
            GUILayout.Label("网络详情", GUI.skin.label);

            if (_networkManager != null)
            {
                var diagnostics = GetNetworkDiagnostics();
                if (diagnostics.HasValue)
                {
                    GUILayout.BeginVertical("box");
                    GUILayout.Label($"连接状态: {diagnostics.Value.State}");
                    GUILayout.Label($"网络质量: {diagnostics.Value.Quality}");
                    GUILayout.Label($"延迟: {diagnostics.Value.Latency:F1}ms");
                    GUILayout.Label($"丢包率: {diagnostics.Value.PacketLoss * 100:F1}%");
                    GUILayout.Label($"带宽: {diagnostics.Value.BandwidthUsage:F1} KB/s");
                    GUILayout.Label($"稳定性: {diagnostics.Value.Stability * 100:F1}%");
                    GUILayout.EndVertical();
                }
            }

            // 错误统计
            if (_errorHandler != null)
            {
                GUILayout.Label("错误统计", GUI.skin.label);

                var errorStats = _errorHandler.GetErrorStatistics();
                foreach (var kvp in errorStats)
                {
                    if (kvp.Value > 0)
                    {
                        GUILayout.Label($"{kvp.Key}: {kvp.Value}");
                    }
                }
            }
        }

        /// <summary>
        /// 绘制线条
        /// </summary>
        private void DrawLine(Vector2 start, Vector2 end, Color color)
        {
            // 简化的线条绘制实现
            // 在实际项目中可以使用更高级的绘制方法
            GUI.color = color;
            GUI.DrawTexture(new Rect(start.x, start.y, end.x - start.x, 1), Texture2D.whiteTexture);
            GUI.color = Color.white;
        }

        /// <summary>
        /// 切换调试UI显示
        /// </summary>
        public void ToggleDebugUI()
        {
            _showDebugUI = !_showDebugUI;
        }

        /// <summary>
        /// 显示调试UI
        /// </summary>
        public void ShowDebugUI()
        {
            _showDebugUI = true;
        }

        /// <summary>
        /// 隐藏调试UI
        /// </summary>
        public void HideDebugUI()
        {
            _showDebugUI = false;
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void LogDebug(string message, DebugLogType type = DebugLogType.Info)
        {
            var entry = new DebugLogEntry
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.Now
            };

            _logEntries.Add(entry);

            // 限制日志数量
            if (_logEntries.Count > maxLogEntries)
            {
                _logEntries.RemoveAt(0);
            }

            // 控制台输出
            if (enableConsoleLogging)
            {
                switch (type)
                {
                    case DebugLogType.Error:
                        UnityEngine.Debug.LogError($"[NetworkDebug] {message}");
                        break;
                    case DebugLogType.Warning:
                        UnityEngine.Debug.LogWarning($"[NetworkDebug] {message}");
                        break;
                    case DebugLogType.Info:
                        UnityEngine.Debug.Log($"[NetworkDebug] {message}");
                        break;
                }
            }
        }

        /// <summary>
        /// 清除日志
        /// </summary>
        public void ClearLogs()
        {
            _logEntries.Clear();
            LogDebug("日志已清除", DebugLogType.Info);
        }

        /// <summary>
        /// 网络错误事件处理
        /// </summary>
        private void OnNetworkError(NetworkError error)
        {
            LogDebug($"网络错误: {error.Type} - {error.Message}", DebugLogType.Error);
        }

        /// <summary>
        /// 连接丢失事件处理
        /// </summary>
        private void OnConnectionLost()
        {
            LogDebug("网络连接丢失", DebugLogType.Warning);
        }

        /// <summary>
        /// 连接恢复事件处理
        /// </summary>
        private void OnConnectionRestored()
        {
            LogDebug("网络连接已恢复", DebugLogType.Info);
        }

        /// <summary>
        /// 获取调试统计
        /// </summary>
        public NetworkDebugStats GetDebugStats()
        {
            return _debugStats;
        }

        /// <summary>
        /// 获取网络诊断信息
        /// </summary>
        private CustomNetworking.ErrorHandling.NetworkDiagnostics? GetNetworkDiagnostics()
        {
            if (_networkManager == null)
                return null;

            try
            {
                return _networkManager.GetNetworkDiagnostics();
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogWarning($"Failed to get network diagnostics: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取网络管理器实例
        /// </summary>
        private INetworkManager GetNetworkManager()
        {
            // 查找实现了INetworkManager接口的组件
            var allComponents = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None); // 使用FindObjectsByType替代已弃用的FindObjectsOfType
            foreach (var component in allComponents)
            {
                if (component is INetworkManager networkManager)
                {
                    return networkManager;
                }
            }
            return null;
        }

        private void OnDestroy()
        {
            // 取消事件订阅
            if (_errorHandler != null)
            {
                _errorHandler.OnErrorOccurred -= OnNetworkError;
                _errorHandler.OnConnectionLost -= OnConnectionLost;
                _errorHandler.OnConnectionRestored -= OnConnectionRestored;
            }
        }
    }
}
