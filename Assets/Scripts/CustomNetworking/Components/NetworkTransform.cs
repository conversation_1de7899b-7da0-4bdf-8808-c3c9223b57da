using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Components
{
    /// <summary>
    /// 自定义网络变换组件 - 替代 Photon Fusion NetworkTransform
    /// </summary>
    public class NetworkTransform : NetworkBehaviour
    {
        [Header("同步设置")]
        [SerializeField] private bool syncPosition = true;
        [SerializeField] private bool syncRotation = true;
        [SerializeField] private bool syncScale = false;

        [Header("插值设置")]
        [SerializeField] private float positionThreshold = 0.01f;
        [SerializeField] private float rotationThreshold = 1f;
        [SerializeField] private float scaleThreshold = 0.01f;

        [Header("平滑设置")]
        [SerializeField] private float interpolationRate = 15f;

        [Header("带宽优化设置")]
        [SerializeField] private float maxSyncRate = 20f; // 最大同步频率 (Hz)
        [SerializeField] private float staticThreshold = 0.001f; // 静止阈值
        [SerializeField] private float staticSyncRate = 2f; // 静止时同步频率 (Hz)
        [SerializeField] private bool enableAdaptiveSync = true; // 自适应同步
        [SerializeField] private bool enableDeltaCompression = true; // 增量压缩

        // 网络同步属性
        [Networked] public Vector3 NetworkPosition { get; set; }
        [Networked] public Quaternion NetworkRotation { get; set; }
        [Networked] public Vector3 NetworkScale { get; set; }

        // 本地缓存
        private Vector3 _lastPosition;
        private Quaternion _lastRotation;
        private Vector3 _lastScale;

        // 插值目标
        private Vector3 _targetPosition;
        private Quaternion _targetRotation;
        private Vector3 _targetScale;

        // 带宽优化相关
        private float _lastSyncTime;
        private float _lastMovementTime;
        private bool _isStatic;
        private Vector3 _lastVelocity;
        private int _staticFrameCount;

        public override void Spawned()
        {
            base.Spawned();

            // 初始化网络属性
            if (HasStateAuthority)
            {
                NetworkPosition = transform.position;
                NetworkRotation = transform.rotation;
                NetworkScale = transform.localScale;
            }

            // 初始化本地缓存
            _lastPosition = transform.position;
            _lastRotation = transform.rotation;
            _lastScale = transform.localScale;

            // 初始化插值目标
            _targetPosition = NetworkPosition;
            _targetRotation = NetworkRotation;
            _targetScale = NetworkScale;
        }

        public override void FixedUpdateNetwork()
        {
            if (!HasStateAuthority) return;

            if (enableAdaptiveSync)
            {
                UpdateAdaptiveSync();
            }
            else
            {
                UpdateStandardSync();
            }
        }

        /// <summary>
        /// 自适应同步更新
        /// </summary>
        private void UpdateAdaptiveSync()
        {
            float currentTime = Time.time;
            Vector3 currentVelocity = (transform.position - _lastPosition) / Time.fixedDeltaTime;

            // 检测是否静止
            bool wasStatic = _isStatic;
            _isStatic = currentVelocity.magnitude < staticThreshold;

            if (_isStatic)
            {
                _staticFrameCount++;
            }
            else
            {
                _staticFrameCount = 0;
                _lastMovementTime = currentTime;
            }

            // 计算同步频率
            float syncRate = CalculateSyncRate(currentVelocity.magnitude, _isStatic);
            float syncInterval = 1f / syncRate;

            // 检查是否需要同步
            bool shouldSync = (currentTime - _lastSyncTime) >= syncInterval;

            if (shouldSync || (!wasStatic && _isStatic) || (wasStatic && !_isStatic))
            {
                PerformSync();
                _lastSyncTime = currentTime;
            }

            _lastVelocity = currentVelocity;
        }

        /// <summary>
        /// 标准同步更新
        /// </summary>
        private void UpdateStandardSync()
        {
            // 检查位置变化 - 使用enableDeltaCompression进行优化
            if (syncPosition && Vector3.Distance(transform.position, _lastPosition) > positionThreshold)
            {
                if (enableDeltaCompression)
                {
                    // 增量压缩：只发送变化量
                    Vector3 deltaPosition = transform.position - _lastPosition;
                    NetworkPosition = _lastPosition + deltaPosition;
                }
                else
                {
                    NetworkPosition = transform.position;
                }
                _lastPosition = transform.position;
            }

            // 检查旋转变化
            if (syncRotation && Quaternion.Angle(transform.rotation, _lastRotation) > rotationThreshold)
            {
                NetworkRotation = transform.rotation;
                _lastRotation = transform.rotation;
            }

            // 检查缩放变化
            if (syncScale && Vector3.Distance(transform.localScale, _lastScale) > scaleThreshold)
            {
                NetworkScale = transform.localScale;
                _lastScale = transform.localScale;
            }
        }

        /// <summary>
        /// 计算同步频率
        /// </summary>
        private float CalculateSyncRate(float velocity, bool isStatic)
        {
            if (isStatic)
            {
                return staticSyncRate;
            }

            // 基于速度的自适应频率
            float normalizedVelocity = Mathf.Clamp01(velocity / 10f); // 假设最大速度为10
            return Mathf.Lerp(staticSyncRate, maxSyncRate, normalizedVelocity);
        }

        /// <summary>
        /// 执行同步
        /// </summary>
        private void PerformSync()
        {
            bool hasChanges = false;

            // 检查位置变化
            if (syncPosition && Vector3.Distance(transform.position, _lastPosition) > positionThreshold)
            {
                NetworkPosition = transform.position;
                _lastPosition = transform.position;
                hasChanges = true;
            }

            // 检查旋转变化
            if (syncRotation && Quaternion.Angle(transform.rotation, _lastRotation) > rotationThreshold)
            {
                NetworkRotation = transform.rotation;
                _lastRotation = transform.rotation;
                hasChanges = true;
            }

            // 检查缩放变化
            if (syncScale && Vector3.Distance(transform.localScale, _lastScale) > scaleThreshold)
            {
                NetworkScale = transform.localScale;
                _lastScale = transform.localScale;
                hasChanges = true;
            }

            // 如果没有变化但是静止状态改变，发送一次确认包
            if (!hasChanges && _staticFrameCount == 1)
            {
                // 发送静止确认
                NetworkPosition = transform.position;
                NetworkRotation = transform.rotation;
                if (syncScale) NetworkScale = transform.localScale;
            }
        }

        public override void Render()
        {
            base.Render();

            if (HasStateAuthority) return; // 权威客户端不需要插值

            // 更新插值目标
            _targetPosition = NetworkPosition;
            _targetRotation = NetworkRotation;
            _targetScale = NetworkScale;

            // 执行插值
            if (syncPosition)
            {
                transform.position = Vector3.Lerp(transform.position, _targetPosition,
                    Time.deltaTime * interpolationRate);
            }

            if (syncRotation)
            {
                transform.rotation = Quaternion.Lerp(transform.rotation, _targetRotation,
                    Time.deltaTime * interpolationRate);
            }

            if (syncScale)
            {
                transform.localScale = Vector3.Lerp(transform.localScale, _targetScale,
                    Time.deltaTime * interpolationRate);
            }
        }

        /// <summary>
        /// 强制同步当前变换
        /// </summary>
        public void ForceSync()
        {
            if (!HasStateAuthority) return;

            NetworkPosition = transform.position;
            NetworkRotation = transform.rotation;
            NetworkScale = transform.localScale;

            _lastPosition = transform.position;
            _lastRotation = transform.rotation;
            _lastScale = transform.localScale;
        }

        /// <summary>
        /// 瞬移到指定位置（不插值）
        /// </summary>
        public void Teleport(Vector3 position, Quaternion rotation)
        {
            if (HasStateAuthority)
            {
                transform.position = position;
                transform.rotation = rotation;

                NetworkPosition = position;
                NetworkRotation = rotation;

                _lastPosition = position;
                _lastRotation = rotation;
            }
            else
            {
                // 非权威客户端直接设置目标
                _targetPosition = position;
                _targetRotation = rotation;

                transform.position = position;
                transform.rotation = rotation;
            }
        }

        /// <summary>
        /// 获取网络位置
        /// </summary>
        public Vector3 GetNetworkPosition()
        {
            return NetworkPosition;
        }

        /// <summary>
        /// 获取网络旋转
        /// </summary>
        public Quaternion GetNetworkRotation()
        {
            return NetworkRotation;
        }

        /// <summary>
        /// 获取网络缩放
        /// </summary>
        public Vector3 GetNetworkScale()
        {
            return NetworkScale;
        }

        /// <summary>
        /// 设置同步选项
        /// </summary>
        public void SetSyncOptions(bool position, bool rotation, bool scale)
        {
            syncPosition = position;
            syncRotation = rotation;
            syncScale = scale;
        }

        /// <summary>
        /// 设置插值速率
        /// </summary>
        public void SetInterpolationRate(float rate)
        {
            interpolationRate = Mathf.Max(0.1f, rate);
        }

        /// <summary>
        /// 设置同步阈值
        /// </summary>
        public void SetThresholds(float posThreshold, float rotThreshold, float scaleThreshold)
        {
            positionThreshold = Mathf.Max(0.001f, posThreshold);
            rotationThreshold = Mathf.Max(0.1f, rotThreshold);
            scaleThreshold = Mathf.Max(0.001f, scaleThreshold);
        }

        /// <summary>
        /// 设置同步频率
        /// </summary>
        public void SetSyncRate(int rateHz)
        {
            maxSyncRate = Mathf.Max(1f, rateHz);
        }

        /// <summary>
        /// 获取当前同步频率
        /// </summary>
        public int GetSyncRate()
        {
            return (int)maxSyncRate;
        }

        #region 调试信息

        private void OnDrawGizmosSelected()
        {
            if (!Application.isPlaying) return;

            // 绘制网络位置
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(NetworkPosition, 0.1f);

            // 绘制当前位置
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, 0.05f);

            // 绘制连线
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(NetworkPosition, transform.position);
        }

        #endregion
    }
}
