using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.Core.RPC;

namespace CustomNetworking.Authority
{
    /// <summary>
    /// 权限管理器 - 实现文档中描述的权限管理功能
    /// </summary>
    public class AuthorityManager : MonoBehaviour
    {
        #region 配置参数

        [Header("Authority Configuration")]
        [SerializeField] private bool enableLocalCache = true;
        [SerializeField] private float cacheRefreshInterval = 30.0f;
        [SerializeField] private bool strictValidation = true;
        [SerializeField] private float authorityTimeout = 60.0f;

        #endregion

        #region 状态管理

        // 权限缓存
        private Dictionary<NetworkId, ObjectAuthority> _authorityCache = new Dictionary<NetworkId, ObjectAuthority>();
        private Dictionary<PlayerRef, List<NetworkId>> _playerAuthorities = new Dictionary<PlayerRef, List<NetworkId>>();

        // 权限请求队列
        private Queue<AuthorityRequest> _pendingRequests = new Queue<AuthorityRequest>();
        private Dictionary<string, AuthorityRequest> _activeRequests = new Dictionary<string, AuthorityRequest>();

        // 网络组件
        private NetworkRunner _runner;
        private string _playerId;

        // 缓存管理
        private float _lastCacheRefresh;

        #endregion

        #region 事件

        public event Action<NetworkId, PlayerRef, PlayerRef> OnAuthorityChanged; // objectId, oldOwner, newOwner
        public event Action<NetworkId, List<AuthorityType>> OnAuthorityGranted; // objectId, authorities
        public event Action<NetworkId, List<AuthorityType>> OnAuthorityRevoked; // objectId, authorities
        public event Action<AuthorityRequest> OnAuthorityRequestCompleted;
        public event Action<string> OnAuthorityError;

        #endregion

        #region Unity生命周期

        private void Start()
        {
            InitializeAuthorityManager();
        }

        private void Update()
        {
            ProcessAuthorityRequests();
            UpdateCache();
            CleanupExpiredRequests();
        }

        private void OnDestroy()
        {
            Cleanup();
        }

        #endregion

        #region 初始化

        private void InitializeAuthorityManager()
        {
            _runner = FindFirstObjectByType<NetworkRunner>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (_runner == null)
            {
                UnityEngine.Debug.LogError("NetworkRunner not found! AuthorityManager requires NetworkRunner.");
                return;
            }

            _playerId = _runner.LocalPlayer.ToString();

            // 初始化缓存刷新
            if (enableLocalCache)
            {
                _lastCacheRefresh = Time.time;
            }

            UnityEngine.Debug.Log("Authority Manager initialized");
        }

        private void Cleanup()
        {
            _authorityCache.Clear();
            _playerAuthorities.Clear();
            _pendingRequests.Clear();
            _activeRequests.Clear();
        }

        #endregion

        #region 权限验证

        /// <summary>
        /// 检查是否有对象权限
        /// </summary>
        public bool HasAuthority(NetworkId objectId, AuthorityType authorityType = AuthorityType.Modify)
        {
            return HasAuthority(objectId, _runner.LocalPlayer, authorityType);
        }

        /// <summary>
        /// 检查指定玩家是否有对象权限
        /// </summary>
        public bool HasAuthority(NetworkId objectId, PlayerRef player, AuthorityType authorityType = AuthorityType.Modify)
        {
            // 首先检查本地缓存
            if (enableLocalCache && _authorityCache.TryGetValue(objectId, out var objAuth))
            {
                return objAuth.HasAuthority(player, authorityType);
            }

            // 如果没有缓存且启用严格验证，请求服务器验证
            if (strictValidation)
            {
                RequestAuthorityCheck(objectId, player, authorityType);
                return false; // 默认拒绝，等待服务器响应
            }

            // 非严格模式下的默认权限检查
            return CheckDefaultAuthority(objectId, player, authorityType);
        }

        /// <summary>
        /// 请求权限检查
        /// </summary>
        public void RequestAuthorityCheck(NetworkId objectId, PlayerRef player, AuthorityType authorityType)
        {
            var request = new AuthorityRequest
            {
                RequestId = Guid.NewGuid().ToString(),
                ObjectId = objectId,
                Player = player,
                AuthorityType = authorityType,
                RequestType = AuthorityRequestType.Check,
                Timestamp = Time.time
            };

            _pendingRequests.Enqueue(request);
            _activeRequests[request.RequestId] = request;

            UnityEngine.Debug.Log($"Queued authority check request for object {objectId}, player {player}, authority {authorityType}");
        }

        /// <summary>
        /// 请求权限转移
        /// </summary>
        public void RequestAuthorityTransfer(NetworkId objectId, PlayerRef fromPlayer, PlayerRef toPlayer, AuthorityType authorityType)
        {
            var request = new AuthorityRequest
            {
                RequestId = Guid.NewGuid().ToString(),
                ObjectId = objectId,
                Player = fromPlayer,
                TargetPlayer = toPlayer,
                AuthorityType = authorityType,
                RequestType = AuthorityRequestType.Transfer,
                Timestamp = Time.time
            };

            _pendingRequests.Enqueue(request);
            _activeRequests[request.RequestId] = request;

            UnityEngine.Debug.Log($"Queued authority transfer request from {fromPlayer} to {toPlayer} for object {objectId}");
        }

        /// <summary>
        /// 请求权限授予
        /// </summary>
        public void RequestAuthorityGrant(NetworkId objectId, PlayerRef player, AuthorityType authorityType)
        {
            var request = new AuthorityRequest
            {
                RequestId = Guid.NewGuid().ToString(),
                ObjectId = objectId,
                Player = player,
                AuthorityType = authorityType,
                RequestType = AuthorityRequestType.Grant,
                Timestamp = Time.time
            };

            _pendingRequests.Enqueue(request);
            _activeRequests[request.RequestId] = request;

            UnityEngine.Debug.Log($"Queued authority grant request for player {player}, object {objectId}, authority {authorityType}");
        }

        /// <summary>
        /// 请求权限撤销
        /// </summary>
        public void RequestAuthorityRevoke(NetworkId objectId, PlayerRef player, AuthorityType authorityType)
        {
            var request = new AuthorityRequest
            {
                RequestId = Guid.NewGuid().ToString(),
                ObjectId = objectId,
                Player = player,
                AuthorityType = authorityType,
                RequestType = AuthorityRequestType.Revoke,
                Timestamp = Time.time
            };

            _pendingRequests.Enqueue(request);
            _activeRequests[request.RequestId] = request;

            UnityEngine.Debug.Log($"Queued authority revoke request for player {player}, object {objectId}, authority {authorityType}");
        }

        #endregion

        #region 权限管理

        /// <summary>
        /// 设置对象权限
        /// </summary>
        public void SetObjectAuthority(NetworkId objectId, PlayerRef owner, List<AuthorityType> authorities)
        {
            if (!_authorityCache.ContainsKey(objectId))
            {
                _authorityCache[objectId] = new ObjectAuthority
                {
                    ObjectId = objectId,
                    Owner = owner,
                    Authorities = new Dictionary<PlayerRef, List<AuthorityType>>(),
                    LastUpdated = Time.time
                };
            }

            var objAuth = _authorityCache[objectId];
            objAuth.Owner = owner;
            objAuth.Authorities[owner] = new List<AuthorityType>(authorities);
            objAuth.LastUpdated = Time.time;

            // 更新玩家权限列表
            UpdatePlayerAuthorityList(owner, objectId);

            UnityEngine.Debug.Log($"Set authority for object {objectId}, owner {owner}, authorities: {string.Join(", ", authorities)}");
        }

        /// <summary>
        /// 添加玩家权限
        /// </summary>
        public void AddPlayerAuthority(NetworkId objectId, PlayerRef player, AuthorityType authorityType)
        {
            if (!_authorityCache.ContainsKey(objectId))
            {
                _authorityCache[objectId] = new ObjectAuthority
                {
                    ObjectId = objectId,
                    Owner = PlayerRef.None,
                    Authorities = new Dictionary<PlayerRef, List<AuthorityType>>(),
                    LastUpdated = Time.time
                };
            }

            var objAuth = _authorityCache[objectId];
            if (!objAuth.Authorities.ContainsKey(player))
            {
                objAuth.Authorities[player] = new List<AuthorityType>();
            }

            if (!objAuth.Authorities[player].Contains(authorityType))
            {
                objAuth.Authorities[player].Add(authorityType);
                objAuth.LastUpdated = Time.time;

                UpdatePlayerAuthorityList(player, objectId);
                OnAuthorityGranted?.Invoke(objectId, new List<AuthorityType> { authorityType });

                UnityEngine.Debug.Log($"Added authority {authorityType} for player {player} on object {objectId}");
            }
        }

        /// <summary>
        /// 移除玩家权限
        /// </summary>
        public void RemovePlayerAuthority(NetworkId objectId, PlayerRef player, AuthorityType authorityType)
        {
            if (_authorityCache.TryGetValue(objectId, out var objAuth))
            {
                if (objAuth.Authorities.ContainsKey(player))
                {
                    if (objAuth.Authorities[player].Remove(authorityType))
                    {
                        objAuth.LastUpdated = Time.time;

                        // 如果玩家没有任何权限了，从列表中移除
                        if (objAuth.Authorities[player].Count == 0)
                        {
                            objAuth.Authorities.Remove(player);
                        }

                        UpdatePlayerAuthorityList(player, objectId);
                        OnAuthorityRevoked?.Invoke(objectId, new List<AuthorityType> { authorityType });

                        UnityEngine.Debug.Log($"Removed authority {authorityType} for player {player} on object {objectId}");
                    }
                }
            }
        }

        /// <summary>
        /// 转移对象所有权
        /// </summary>
        public void TransferOwnership(NetworkId objectId, PlayerRef newOwner)
        {
            if (_authorityCache.TryGetValue(objectId, out var objAuth))
            {
                var oldOwner = objAuth.Owner;
                objAuth.Owner = newOwner;
                objAuth.LastUpdated = Time.time;

                // 将所有权限转移给新所有者
                if (objAuth.Authorities.ContainsKey(oldOwner))
                {
                    var authorities = new List<AuthorityType>(objAuth.Authorities[oldOwner]);
                    objAuth.Authorities.Remove(oldOwner);
                    objAuth.Authorities[newOwner] = authorities;
                }

                UpdatePlayerAuthorityList(oldOwner, objectId);
                UpdatePlayerAuthorityList(newOwner, objectId);

                OnAuthorityChanged?.Invoke(objectId, oldOwner, newOwner);

                UnityEngine.Debug.Log($"Transferred ownership of object {objectId} from {oldOwner} to {newOwner}");
            }
        }

        #endregion

        #region 缓存管理

        private void UpdateCache()
        {
            if (!enableLocalCache)
                return;

            if (Time.time - _lastCacheRefresh >= cacheRefreshInterval)
            {
                _lastCacheRefresh = Time.time;
                RefreshAuthorityCache();
            }
        }

        private void RefreshAuthorityCache()
        {
            // 清理过期的缓存条目
            var expiredObjects = new List<NetworkId>();
            foreach (var kvp in _authorityCache)
            {
                if (Time.time - kvp.Value.LastUpdated > authorityTimeout)
                {
                    expiredObjects.Add(kvp.Key);
                }
            }

            foreach (var objectId in expiredObjects)
            {
                _authorityCache.Remove(objectId);
                UnityEngine.Debug.Log($"Removed expired authority cache for object {objectId}");
            }

            UnityEngine.Debug.Log($"Authority cache refreshed, {_authorityCache.Count} objects cached");
        }

        private void UpdatePlayerAuthorityList(PlayerRef player, NetworkId objectId)
        {
            if (!_playerAuthorities.ContainsKey(player))
            {
                _playerAuthorities[player] = new List<NetworkId>();
            }

            var playerObjects = _playerAuthorities[player];
            if (!playerObjects.Contains(objectId))
            {
                playerObjects.Add(objectId);
            }
        }

        #endregion

        #region 请求处理

        private void ProcessAuthorityRequests()
        {
            while (_pendingRequests.Count > 0)
            {
                var request = _pendingRequests.Dequeue();
                ProcessAuthorityRequest(request);
            }
        }

        private void ProcessAuthorityRequest(AuthorityRequest request)
        {
            // 模拟处理权限请求
            // 在实际实现中，这里会发送网络请求到服务器

            bool success = true;
            string errorMessage = null;

            switch (request.RequestType)
            {
                case AuthorityRequestType.Check:
                    success = CheckDefaultAuthority(request.ObjectId, request.Player, request.AuthorityType);
                    break;

                case AuthorityRequestType.Grant:
                    AddPlayerAuthority(request.ObjectId, request.Player, request.AuthorityType);
                    break;

                case AuthorityRequestType.Revoke:
                    RemovePlayerAuthority(request.ObjectId, request.Player, request.AuthorityType);
                    break;

                case AuthorityRequestType.Transfer:
                    if (request.TargetPlayer.IsValid)
                    {
                        TransferOwnership(request.ObjectId, request.TargetPlayer);
                    }
                    else
                    {
                        success = false;
                        errorMessage = "Invalid target player for transfer";
                    }
                    break;
            }

            // 完成请求
            request.IsCompleted = true;
            request.Success = success;
            request.ErrorMessage = errorMessage;

            OnAuthorityRequestCompleted?.Invoke(request);

            if (!success && !string.IsNullOrEmpty(errorMessage))
            {
                OnAuthorityError?.Invoke(errorMessage);
            }
        }

        private void CleanupExpiredRequests()
        {
            var expiredRequests = new List<string>();
            foreach (var kvp in _activeRequests)
            {
                if (Time.time - kvp.Value.Timestamp > authorityTimeout)
                {
                    expiredRequests.Add(kvp.Key);
                }
            }

            foreach (var requestId in expiredRequests)
            {
                _activeRequests.Remove(requestId);
            }
        }

        #endregion

        #region 辅助方法

        private bool CheckDefaultAuthority(NetworkId objectId, PlayerRef player, AuthorityType authorityType)
        {
            // 默认权限检查逻辑
            // 在实际实现中，这里会根据游戏规则进行权限检查

            // 简化实现：服务器有所有权限，客户端有基本权限
            if (_runner.IsServer)
                return true;

            // 玩家对自己的对象有权限
            if (player.Equals(_runner.LocalPlayer))
                return true;

            // 其他情况根据权限类型判断
            switch (authorityType)
            {
                case AuthorityType.Read:
                    return true; // 所有人都可以读取
                case AuthorityType.Modify:
                case AuthorityType.Delete:
                    return false; // 需要特殊权限
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取对象权限信息
        /// </summary>
        public ObjectAuthority GetObjectAuthority(NetworkId objectId)
        {
            _authorityCache.TryGetValue(objectId, out var authority);
            return authority;
        }

        /// <summary>
        /// 获取玩家拥有权限的对象列表
        /// </summary>
        public List<NetworkId> GetPlayerAuthorizedObjects(PlayerRef player)
        {
            _playerAuthorities.TryGetValue(player, out var objects);
            return objects ?? new List<NetworkId>();
        }

        #endregion
    }
}
