using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.ErrorHandling;

namespace CustomNetworking.Security
{
    /// <summary>
    /// 网络安全管理器 - 防作弊和数据验证
    /// </summary>
    public class NetworkSecurityManager : MonoBehaviour
    {
        [Header("安全设置")]
        [SerializeField] private bool enableAntiCheat = true;
        [SerializeField] private bool enableDataValidation = true;
        [SerializeField] private bool enableEncryption = true;
        [SerializeField] private bool enableRateLimit = true;

        [Header("防作弊配置")]
        [SerializeField] private float maxMovementSpeed = 10f;
        [SerializeField] private float maxPositionDelta = 5f;
        [SerializeField] private float maxRpcFrequency = 20f; // RPC/秒
        [SerializeField] private int maxActionsPerSecond = 10;

        [Header("数据验证")]
        [SerializeField] private bool validatePlayerActions = true;
        [SerializeField] private bool validateGameState = true;
        [SerializeField] private bool validateRpcParameters = true;

        [Header("加密设置")]
        [SerializeField] private bool useAES = true;
        [SerializeField] private bool useHMAC = true;
        [SerializeField] private int keySize = 256;

        // 单例实例
        private static NetworkSecurityManager _instance;
        public static NetworkSecurityManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<NetworkSecurityManager>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("NetworkSecurityManager");
                        _instance = go.AddComponent<NetworkSecurityManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        // 安全状态
        private Dictionary<PlayerRef, PlayerSecurityData> _playerSecurityData = new Dictionary<PlayerRef, PlayerSecurityData>();
        private Dictionary<PlayerRef, List<float>> _rpcTimestamps = new Dictionary<PlayerRef, List<float>>();
        private Dictionary<PlayerRef, List<float>> _actionTimestamps = new Dictionary<PlayerRef, List<float>>();

        // 加密密钥
        private byte[] _encryptionKey;
        private byte[] _hmacKey;
        private AesCryptoServiceProvider _aes;
        private HMACSHA256 _hmac;

        // 事件
        public event Action<PlayerRef, CheatType> OnCheatDetected;
        public event Action<PlayerRef, ValidationFailure> OnValidationFailed;
        public event Action<PlayerRef, SecurityViolation> OnSecurityViolation;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSecurity();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 初始化安全系统
        /// </summary>
        private void InitializeSecurity()
        {
            if (enableEncryption)
            {
                InitializeEncryption();
            }

            UnityEngine.Debug.Log("[NetworkSecurity] 网络安全系统已初始化");
        }

        /// <summary>
        /// 初始化加密系统
        /// </summary>
        private void InitializeEncryption()
        {
            // 生成加密密钥
            _encryptionKey = GenerateRandomKey(keySize / 8);
            _hmacKey = GenerateRandomKey(32); // HMAC-SHA256 密钥

            // 初始化AES
            if (useAES)
            {
                _aes = new AesCryptoServiceProvider();
                _aes.Key = _encryptionKey;
                _aes.Mode = CipherMode.CBC;
                _aes.Padding = PaddingMode.PKCS7;
            }

            // 初始化HMAC
            if (useHMAC)
            {
                _hmac = new HMACSHA256(_hmacKey);
            }
        }

        /// <summary>
        /// 生成随机密钥
        /// </summary>
        private byte[] GenerateRandomKey(int size)
        {
            byte[] key = new byte[size];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(key);
            }
            return key;
        }

        /// <summary>
        /// 注册玩家
        /// </summary>
        public void RegisterPlayer(PlayerRef player)
        {
            if (!_playerSecurityData.ContainsKey(player))
            {
                _playerSecurityData[player] = new PlayerSecurityData
                {
                    PlayerId = player,
                    LastPosition = Vector3.zero,
                    LastUpdateTime = Time.time,
                    ViolationCount = 0,
                    TrustLevel = 1f
                };

                _rpcTimestamps[player] = new List<float>();
                _actionTimestamps[player] = new List<float>();
            }
        }

        /// <summary>
        /// 注销玩家
        /// </summary>
        public void UnregisterPlayer(PlayerRef player)
        {
            _playerSecurityData.Remove(player);
            _rpcTimestamps.Remove(player);
            _actionTimestamps.Remove(player);
        }

        /// <summary>
        /// 验证玩家移动
        /// </summary>
        public bool ValidatePlayerMovement(PlayerRef player, Vector3 newPosition, float deltaTime)
        {
            if (!enableAntiCheat || !_playerSecurityData.ContainsKey(player))
                return true;

            var securityData = _playerSecurityData[player];
            Vector3 lastPosition = securityData.LastPosition;

            // 计算移动距离和速度
            float distance = Vector3.Distance(lastPosition, newPosition);
            float speed = distance / deltaTime;

            // 检查速度是否超过限制
            if (speed > maxMovementSpeed)
            {
                ReportCheat(player, CheatType.SpeedHack, $"移动速度过快: {speed:F2} > {maxMovementSpeed}");
                return false;
            }

            // 检查位置变化是否合理
            if (distance > maxPositionDelta)
            {
                ReportCheat(player, CheatType.Teleport, $"位置变化过大: {distance:F2} > {maxPositionDelta}");
                return false;
            }

            // 更新安全数据
            securityData.LastPosition = newPosition;
            securityData.LastUpdateTime = Time.time;

            return true;
        }

        /// <summary>
        /// 验证RPC调用
        /// </summary>
        public bool ValidateRpcCall(PlayerRef player, string methodName, object[] parameters)
        {
            if (!enableAntiCheat || !_playerSecurityData.ContainsKey(player))
                return true;

            // 使用enableDataValidation字段控制数据验证
            if (!enableDataValidation)
                return true;

            float currentTime = Time.time;

            // 检查RPC频率 - 使用enableRateLimit字段控制频率限制
            if (enableRateLimit && !CheckRpcRate(player, currentTime))
            {
                ReportCheat(player, CheatType.RpcSpam, $"RPC调用频率过高: {methodName}");
                return false;
            }

            // 验证RPC参数
            if (validateRpcParameters && !ValidateRpcParameters(player, methodName, parameters))
            {
                ReportValidationFailure(player, ValidationFailure.InvalidRpcParameters, $"RPC参数无效: {methodName}");
                return false;
            }

            // 验证玩家权限
            if (!ValidatePlayerPermission(player, methodName))
            {
                ReportSecurityViolation(player, SecurityViolation.UnauthorizedRpc, $"无权限调用RPC: {methodName}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证玩家动作
        /// </summary>
        public bool ValidatePlayerAction(PlayerRef player, PlayerAction action)
        {
            if (!enableAntiCheat || !validatePlayerActions)
                return true;

            float currentTime = Time.time;

            // 检查动作频率
            if (!CheckActionRate(player, currentTime))
            {
                ReportCheat(player, CheatType.ActionSpam, $"动作频率过高: {action.Type}");
                return false;
            }

            // 验证动作合法性
            if (!ValidateActionLegality(player, action))
            {
                ReportValidationFailure(player, ValidationFailure.IllegalAction, $"非法动作: {action.Type}");
                return false;
            }

            // 验证动作时机
            if (!ValidateActionTiming(player, action))
            {
                ReportValidationFailure(player, ValidationFailure.InvalidTiming, $"动作时机无效: {action.Type}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查RPC频率
        /// </summary>
        private bool CheckRpcRate(PlayerRef player, float currentTime)
        {
            var timestamps = _rpcTimestamps[player];

            // 移除过期的时间戳
            timestamps.RemoveAll(t => currentTime - t > 1f);

            // 检查频率
            if (timestamps.Count >= maxRpcFrequency)
            {
                return false;
            }

            // 添加当前时间戳
            timestamps.Add(currentTime);
            return true;
        }

        /// <summary>
        /// 检查动作频率
        /// </summary>
        private bool CheckActionRate(PlayerRef player, float currentTime)
        {
            var timestamps = _actionTimestamps[player];

            // 移除过期的时间戳
            timestamps.RemoveAll(t => currentTime - t > 1f);

            // 检查频率
            if (timestamps.Count >= maxActionsPerSecond)
            {
                return false;
            }

            // 添加当前时间戳
            timestamps.Add(currentTime);
            return true;
        }

        /// <summary>
        /// 验证RPC参数
        /// </summary>
        private bool ValidateRpcParameters(PlayerRef player, string methodName, object[] parameters)
        {
            // 根据方法名验证参数
            switch (methodName)
            {
                case "RPC_KillPlayer":
                    return ValidateKillParameters(player, parameters);
                case "RPC_ReportBody":
                    return ValidateReportParameters(player, parameters);
                case "RPC_CompleteTask":
                    return ValidateTaskParameters(player, parameters);
                default:
                    return true; // 未知方法默认通过
            }
        }

        /// <summary>
        /// 验证杀人参数
        /// </summary>
        private bool ValidateKillParameters(PlayerRef killer, object[] parameters)
        {
            if (parameters.Length < 1) return false;

            // 检查杀手是否有杀人权限
            var gameManager = GetGameManager();
            if (gameManager == null) return false;

            // 这里应该检查杀手的角色和状态
            // 简化实现：假设参数有效
            return true;
        }

        /// <summary>
        /// 验证报告参数
        /// </summary>
        private bool ValidateReportParameters(PlayerRef reporter, object[] parameters)
        {
            if (parameters.Length < 1) return false;

            // 检查报告者是否存活
            // 检查尸体位置是否合理
            // 简化实现
            return true;
        }

        /// <summary>
        /// 验证任务参数
        /// </summary>
        private bool ValidateTaskParameters(PlayerRef player, object[] parameters)
        {
            if (parameters.Length < 1) return false;

            // 检查任务是否分配给该玩家
            // 检查任务是否可以完成
            // 简化实现
            return true;
        }

        /// <summary>
        /// 验证玩家权限
        /// </summary>
        private bool ValidatePlayerPermission(PlayerRef player, string methodName)
        {
            // 根据玩家角色和状态验证权限
            var gameManager = GetGameManager();
            if (gameManager == null) return true;

            // 这里应该检查玩家的角色和游戏状态
            // 简化实现：假设有权限
            return true;
        }

        /// <summary>
        /// 验证动作合法性
        /// </summary>
        private bool ValidateActionLegality(PlayerRef player, PlayerAction action)
        {
            // 根据游戏状态和玩家状态验证动作
            switch (action.Type)
            {
                case ActionType.Kill:
                    return ValidateKillAction(player, action);
                case ActionType.Report:
                    return ValidateReportAction(player, action);
                case ActionType.CompleteTask:
                    return ValidateTaskAction(player, action);
                default:
                    return true;
            }
        }

        /// <summary>
        /// 验证动作时机
        /// </summary>
        private bool ValidateActionTiming(PlayerRef player, PlayerAction action)
        {
            // 使用validateGameState字段控制游戏状态验证
            if (!validateGameState)
                return true;

            var gameManager = GetGameManager();
            if (gameManager == null) return true;

            // 检查游戏状态是否允许该动作
            var gameState = GetGameState(gameManager);

            switch (action.Type)
            {
                case ActionType.Kill:
                case ActionType.CompleteTask:
                    return gameState?.ToString() == "Playing";
                case ActionType.Report:
                    return gameState?.ToString() == "Playing" || gameState?.ToString() == "Meeting";
                default:
                    return true;
            }
        }

        /// <summary>
        /// 验证杀人动作
        /// </summary>
        private bool ValidateKillAction(PlayerRef killer, PlayerAction action)
        {
            // 检查杀手是否是鸭子
            // 检查目标是否在杀人范围内
            // 检查杀人冷却时间
            // 简化实现
            return true;
        }

        /// <summary>
        /// 验证报告动作
        /// </summary>
        private bool ValidateReportAction(PlayerRef reporter, PlayerAction action)
        {
            // 检查报告者是否存活
            // 检查是否在尸体附近
            // 简化实现
            return true;
        }

        /// <summary>
        /// 验证任务动作
        /// </summary>
        private bool ValidateTaskAction(PlayerRef player, PlayerAction action)
        {
            // 检查任务是否分配给该玩家
            // 检查是否在任务位置
            // 简化实现
            return true;
        }

        /// <summary>
        /// 加密数据
        /// </summary>
        public byte[] EncryptData(byte[] data)
        {
            if (!enableEncryption || !useAES || _aes == null)
                return data;

            try
            {
                _aes.GenerateIV();
                using (var encryptor = _aes.CreateEncryptor())
                {
                    byte[] encrypted = encryptor.TransformFinalBlock(data, 0, data.Length);

                    // 将IV和加密数据组合
                    byte[] result = new byte[_aes.IV.Length + encrypted.Length];
                    Array.Copy(_aes.IV, 0, result, 0, _aes.IV.Length);
                    Array.Copy(encrypted, 0, result, _aes.IV.Length, encrypted.Length);

                    return result;
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[NetworkSecurity] 数据加密失败: {ex.Message}");
                return data;
            }
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        public byte[] DecryptData(byte[] encryptedData)
        {
            if (!enableEncryption || !useAES || _aes == null)
                return encryptedData;

            try
            {
                // 提取IV
                byte[] iv = new byte[_aes.IV.Length];
                Array.Copy(encryptedData, 0, iv, 0, iv.Length);

                // 提取加密数据
                byte[] encrypted = new byte[encryptedData.Length - iv.Length];
                Array.Copy(encryptedData, iv.Length, encrypted, 0, encrypted.Length);

                _aes.IV = iv;
                using (var decryptor = _aes.CreateDecryptor())
                {
                    return decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[NetworkSecurity] 数据解密失败: {ex.Message}");
                return encryptedData;
            }
        }

        /// <summary>
        /// 计算HMAC
        /// </summary>
        public byte[] ComputeHMAC(byte[] data)
        {
            if (!enableEncryption || !useHMAC || _hmac == null)
                return new byte[0];

            return _hmac.ComputeHash(data);
        }

        /// <summary>
        /// 验证HMAC
        /// </summary>
        public bool VerifyHMAC(byte[] data, byte[] hmac)
        {
            if (!enableEncryption || !useHMAC || _hmac == null)
                return true;

            byte[] computedHmac = ComputeHMAC(data);
            return CompareBytes(computedHmac, hmac);
        }

        /// <summary>
        /// 比较字节数组
        /// </summary>
        private bool CompareBytes(byte[] a, byte[] b)
        {
            if (a.Length != b.Length) return false;

            for (int i = 0; i < a.Length; i++)
            {
                if (a[i] != b[i]) return false;
            }

            return true;
        }

        /// <summary>
        /// 报告作弊行为
        /// </summary>
        private void ReportCheat(PlayerRef player, CheatType cheatType, string details)
        {
            UnityEngine.Debug.LogWarning($"[NetworkSecurity] 检测到作弊行为: {player} - {cheatType} - {details}");

            // 更新玩家安全数据
            if (_playerSecurityData.ContainsKey(player))
            {
                var securityData = _playerSecurityData[player];
                securityData.ViolationCount++;
                securityData.TrustLevel = Mathf.Max(0f, securityData.TrustLevel - 0.1f);

                // 如果违规次数过多，可以考虑踢出玩家
                if (securityData.ViolationCount >= 5)
                {
                    UnityEngine.Debug.LogError($"[NetworkSecurity] 玩家 {player} 违规次数过多，建议踢出");
                }
            }

            OnCheatDetected?.Invoke(player, cheatType);
        }

        /// <summary>
        /// 报告验证失败
        /// </summary>
        private void ReportValidationFailure(PlayerRef player, ValidationFailure failure, string details)
        {
            UnityEngine.Debug.LogWarning($"[NetworkSecurity] 验证失败: {player} - {failure} - {details}");
            OnValidationFailed?.Invoke(player, failure);
        }

        /// <summary>
        /// 报告安全违规
        /// </summary>
        private void ReportSecurityViolation(PlayerRef player, SecurityViolation violation, string details)
        {
            UnityEngine.Debug.LogWarning($"[NetworkSecurity] 安全违规: {player} - {violation} - {details}");
            OnSecurityViolation?.Invoke(player, violation);
        }

        /// <summary>
        /// 获取玩家信任等级
        /// </summary>
        public float GetPlayerTrustLevel(PlayerRef player)
        {
            if (_playerSecurityData.ContainsKey(player))
            {
                return _playerSecurityData[player].TrustLevel;
            }
            return 1f; // 默认信任等级
        }

        /// <summary>
        /// 获取安全统计信息
        /// </summary>
        public SecurityStats GetSecurityStats()
        {
            int totalViolations = 0;
            float averageTrustLevel = 0f;

            foreach (var data in _playerSecurityData.Values)
            {
                totalViolations += data.ViolationCount;
                averageTrustLevel += data.TrustLevel;
            }

            if (_playerSecurityData.Count > 0)
            {
                averageTrustLevel /= _playerSecurityData.Count;
            }

            return new SecurityStats
            {
                TotalPlayers = _playerSecurityData.Count,
                TotalViolations = totalViolations,
                AverageTrustLevel = averageTrustLevel,
                EncryptionEnabled = enableEncryption,
                AntiCheatEnabled = enableAntiCheat
            };
        }

        /// <summary>
        /// 获取游戏管理器实例
        /// </summary>
        private MonoBehaviour GetGameManager()
        {
            // 查找名为 GameManager 的组件
            var allComponents = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None); // 使用FindObjectsByType替代已弃用的FindObjectsOfType
            foreach (var component in allComponents)
            {
                if (component.GetType().Name == "GameManager")
                {
                    return component;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取游戏状态
        /// </summary>
        private object GetGameState(MonoBehaviour gameManager)
        {
            if (gameManager == null) return null;

            // 使用反射获取 CurrentGameState 属性
            var property = gameManager.GetType().GetProperty("CurrentGameState");
            if (property != null)
            {
                return property.GetValue(gameManager);
            }

            return null;
        }

        private void OnDestroy()
        {
            _aes?.Dispose();
            _hmac?.Dispose();
        }
    }
}
