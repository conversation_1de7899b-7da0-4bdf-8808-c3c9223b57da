using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.HostMigration
{
    /// <summary>
    /// 主机迁移管理器 - 实现文档中描述的主机迁移功能
    /// </summary>
    public class HostMigrationManager : MonoBehaviour
    {
        #region 配置参数

        [Header("Host Migration Configuration")]
        [SerializeField] private float heartbeatInterval = 2.0f;
        [SerializeField] private float performanceUpdateInterval = 5.0f;
        [SerializeField] private bool autoRegisterAsCandidate = true;
        [SerializeField] private int candidatePriority = 50;
        [SerializeField] private float hostTimeout = 10.0f;

        #endregion

        #region 状态管理

        public bool IsCurrentHost { get; private set; }
        public bool IsRegisteredCandidate { get; private set; }
        public string CurrentGameId { get; private set; }
        public PlayerRef CurrentHostPlayer { get; private set; }

        // 候选主机列表
        private Dictionary<PlayerRef, HostCandidate> _candidates = new Dictionary<PlayerRef, HostCandidate>();
        private List<PlayerRef> _candidatesByPriority = new List<PlayerRef>();

        // 性能监控数据
        private PerformanceMetrics _currentMetrics = new PerformanceMetrics();
        private float _lastHeartbeatTime;
        private float _lastPerformanceUpdate;

        // 网络组件
        private NetworkRunner _runner;
        private string _playerId;

        #endregion

        #region 事件

        public event Action<PlayerRef, PlayerRef> OnHostChanged; // oldHost, newHost
        public event Action<HostMigrationEvent> OnMigrationEvent;
        public event Action OnMigrationStarted;
        public event Action OnMigrationCompleted;
        public event Action<PlayerRef> OnCandidateRegistered;
        public event Action<PlayerRef> OnCandidateUnregistered;

        #endregion

        #region Unity生命周期

        private void Start()
        {
            InitializeHostMigration();
        }

        private void Update()
        {
            if (_runner == null || !_runner.IsRunning)
                return;

            UpdateHeartbeat();
            UpdatePerformanceMetrics();
            CheckHostHealth();
        }

        private void OnDestroy()
        {
            Cleanup();
        }

        #endregion

        #region 初始化

        private void InitializeHostMigration()
        {
            _runner = FindFirstObjectByType<NetworkRunner>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (_runner == null)
            {
                UnityEngine.Debug.LogError("NetworkRunner not found! HostMigrationManager requires NetworkRunner.");
                return;
            }

            _playerId = _runner.LocalPlayer.ToString();
            CurrentHostPlayer = _runner.IsHost ? _runner.LocalPlayer : PlayerRef.None;
            IsCurrentHost = _runner.IsHost;

            // 如果自动注册为候选主机
            if (autoRegisterAsCandidate && !IsCurrentHost)
            {
                RegisterAsCandidate();
            }

            UnityEngine.Debug.Log("Host Migration Manager initialized");
        }

        private void Cleanup()
        {
            if (IsRegisteredCandidate)
            {
                UnregisterAsCandidate();
            }
        }

        #endregion

        #region 候选主机管理

        /// <summary>
        /// 注册为候选主机
        /// </summary>
        public void RegisterAsCandidate()
        {
            if (IsRegisteredCandidate || IsCurrentHost)
                return;

            var candidate = new HostCandidate
            {
                PlayerId = _runner.LocalPlayer,
                Priority = candidatePriority,
                PerformanceMetrics = _currentMetrics,
                RegistrationTime = Time.time
            };

            _candidates[_runner.LocalPlayer] = candidate;
            IsRegisteredCandidate = true;

            OnCandidateRegistered?.Invoke(_runner.LocalPlayer);
            UnityEngine.Debug.Log($"Registered as host migration candidate with priority {candidatePriority}");
        }

        /// <summary>
        /// 取消候选主机注册
        /// </summary>
        public void UnregisterAsCandidate()
        {
            if (!IsRegisteredCandidate)
                return;

            _candidates.Remove(_runner.LocalPlayer);
            IsRegisteredCandidate = false;

            OnCandidateUnregistered?.Invoke(_runner.LocalPlayer);
            UnityEngine.Debug.Log("Unregistered as host migration candidate");
        }

        /// <summary>
        /// 开始主机迁移
        /// </summary>
        public void StartHostMigration(PlayerRef failedHost)
        {
            if (IsCurrentHost)
                return;

            UnityEngine.Debug.Log($"Starting host migration from failed host: {failedHost}");
            OnMigrationStarted?.Invoke();

            // 选择新主机
            PlayerRef newHost = SelectNewHost();
            if (newHost.IsValid)
            {
                InitiateHostTransfer(failedHost, newHost);
            }
            else
            {
                UnityEngine.Debug.LogError("No suitable host candidate found!");
                OnMigrationEvent?.Invoke(new HostMigrationEvent
                {
                    Type = HostMigrationEventType.Failed,
                    OldHost = failedHost,
                    NewHost = PlayerRef.None,
                    Reason = "No suitable candidate",
                    Timestamp = Time.time
                });
            }
        }

        /// <summary>
        /// 选择新主机
        /// </summary>
        private PlayerRef SelectNewHost()
        {
            UpdateCandidatePriorityList();

            foreach (var candidateId in _candidatesByPriority)
            {
                if (_candidates.TryGetValue(candidateId, out var candidate))
                {
                    if (IsValidCandidate(candidate))
                    {
                        return candidateId;
                    }
                }
            }

            return PlayerRef.None;
        }

        /// <summary>
        /// 更新候选主机优先级列表
        /// </summary>
        private void UpdateCandidatePriorityList()
        {
            _candidatesByPriority.Clear();
            _candidatesByPriority.AddRange(_candidates.Keys);

            _candidatesByPriority.Sort((a, b) =>
            {
                var candidateA = _candidates[a];
                var candidateB = _candidates[b];

                int priorityComparison = candidateB.Priority.CompareTo(candidateA.Priority);
                if (priorityComparison != 0)
                    return priorityComparison;

                float scoreA = CalculatePerformanceScore(candidateA.PerformanceMetrics);
                float scoreB = CalculatePerformanceScore(candidateB.PerformanceMetrics);
                return scoreB.CompareTo(scoreA);
            });
        }

        /// <summary>
        /// 执行主机转移
        /// </summary>
        private void InitiateHostTransfer(PlayerRef oldHost, PlayerRef newHost)
        {
            UnityEngine.Debug.Log($"Initiating host transfer from {oldHost} to {newHost}");

            if (newHost.Equals(_runner.LocalPlayer))
            {
                BecomeNewHost(oldHost);
            }
            else
            {
                WaitForNewHost(newHost);
            }
        }

        /// <summary>
        /// 成为新主机
        /// </summary>
        private void BecomeNewHost(PlayerRef oldHost)
        {
            UnityEngine.Debug.Log("Becoming new host...");

            IsCurrentHost = true;
            CurrentHostPlayer = _runner.LocalPlayer;

            if (IsRegisteredCandidate)
            {
                UnregisterAsCandidate();
            }

            OnHostChanged?.Invoke(oldHost, _runner.LocalPlayer);
            OnMigrationCompleted?.Invoke();

            OnMigrationEvent?.Invoke(new HostMigrationEvent
            {
                Type = HostMigrationEventType.Completed,
                OldHost = oldHost,
                NewHost = _runner.LocalPlayer,
                Reason = "Host migration successful",
                Timestamp = Time.time
            });

            UnityEngine.Debug.Log("Successfully became new host");
        }

        /// <summary>
        /// 等待新主机
        /// </summary>
        private void WaitForNewHost(PlayerRef newHost)
        {
            UnityEngine.Debug.Log($"Waiting for new host: {newHost}");
            CurrentHostPlayer = newHost;
            StartCoroutine(WaitForHostTransferTimeout(newHost));
        }

        /// <summary>
        /// 主机转移超时检查
        /// </summary>
        private IEnumerator WaitForHostTransferTimeout(PlayerRef expectedHost)
        {
            float timeout = 30f;
            float elapsed = 0f;

            while (elapsed < timeout)
            {
                if (CurrentHostPlayer.Equals(expectedHost) && IsCurrentHost)
                {
                    OnMigrationCompleted?.Invoke();
                    yield break;
                }

                elapsed += Time.deltaTime;
                yield return null;
            }

            UnityEngine.Debug.LogWarning("Host transfer timeout, restarting migration");
            StartHostMigration(expectedHost);
        }

        #endregion

        #region 性能监控

        private void UpdateHeartbeat()
        {
            if (Time.time - _lastHeartbeatTime >= heartbeatInterval)
            {
                _lastHeartbeatTime = Time.time;

                if (IsCurrentHost)
                {
                    SendHeartbeat();
                }
            }
        }

        private void UpdatePerformanceMetrics()
        {
            if (Time.time - _lastPerformanceUpdate >= performanceUpdateInterval)
            {
                _lastPerformanceUpdate = Time.time;
                CollectPerformanceMetrics();

                if (IsRegisteredCandidate && _candidates.ContainsKey(_runner.LocalPlayer))
                {
                    _candidates[_runner.LocalPlayer].PerformanceMetrics = _currentMetrics;
                }
            }
        }

        private void CheckHostHealth()
        {
            if (IsCurrentHost)
                return;

            if (CurrentHostPlayer.IsValid && Time.time - _lastHeartbeatTime > hostTimeout)
            {
                UnityEngine.Debug.LogWarning($"Host {CurrentHostPlayer} appears to be unresponsive");
                StartHostMigration(CurrentHostPlayer);
            }
        }

        private void SendHeartbeat()
        {
            UnityEngine.Debug.Log("Sending heartbeat to all clients");
        }

        private void CollectPerformanceMetrics()
        {
            _currentMetrics.CpuUsage = UnityEngine.Random.Range(10f, 80f);
            _currentMetrics.MemoryUsage = (UnityEngine.Profiling.Profiler.GetTotalAllocatedMemoryLong() / 1024f / 1024f) / 1000f * 100f; // 使用GetTotalAllocatedMemoryLong替代已弃用的GetTotalAllocatedMemory
            _currentMetrics.FrameRate = 1.0f / Time.deltaTime;
            _currentMetrics.NetworkLatency = UnityEngine.Random.Range(20f, 200f);
            _currentMetrics.Stability = Mathf.Clamp01(_currentMetrics.FrameRate / 60f);
        }

        private bool IsValidCandidate(HostCandidate candidate)
        {
            float performanceScore = CalculatePerformanceScore(candidate.PerformanceMetrics);
            return performanceScore > 0.5f;
        }

        private float CalculatePerformanceScore(PerformanceMetrics metrics)
        {
            float cpuScore = Mathf.Clamp01(1.0f - metrics.CpuUsage / 100.0f);
            float memoryScore = Mathf.Clamp01(1.0f - metrics.MemoryUsage / 100.0f);
            float fpsScore = Mathf.Clamp01(metrics.FrameRate / 60.0f);
            float latencyScore = Mathf.Clamp01(1.0f - metrics.NetworkLatency / 1000.0f);

            return (cpuScore + memoryScore + fpsScore + latencyScore) / 4.0f;
        }

        #endregion

        #region 数据结构

        [Serializable]
        public class HostCandidate
        {
            public PlayerRef PlayerId;
            public int Priority;
            public PerformanceMetrics PerformanceMetrics;
            public float RegistrationTime;
        }

        [Serializable]
        public class PerformanceMetrics
        {
            public float CpuUsage;
            public float MemoryUsage;
            public float FrameRate;
            public float NetworkLatency;
            public float Stability;
        }

        [Serializable]
        public class HostMigrationEvent
        {
            public HostMigrationEventType Type;
            public PlayerRef OldHost;
            public PlayerRef NewHost;
            public string Reason;
            public float Timestamp;
        }

        public enum HostMigrationEventType
        {
            Started,
            InProgress,
            Completed,
            Failed
        }

        #endregion
    }
}
