using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

namespace CustomNetworking.Core
{


    #region 网络连接基类

    /// <summary>
    /// 网络连接抽象基类
    /// </summary>
    public abstract class NetworkConnection
    {
        protected bool _isConnected;
        protected List<PlayerRef> _connectedPlayers = new List<PlayerRef>();

        public bool IsConnected => _isConnected;
        public List<PlayerRef> ConnectedPlayers => new List<PlayerRef>(_connectedPlayers);

        // 事件
        public event Action<PlayerRef> OnPlayerConnected;
        public event Action<PlayerRef> OnPlayerDisconnected;
        public event Action<NetworkData> OnDataReceived;

        public abstract Task StartServer(NetAddress address, int port);
        public abstract Task ConnectToServer(NetAddress address, int port);
        public abstract Task Disconnect();
        public abstract void SendToAll(NetworkData data);
        public abstract void SendToPlayer(PlayerRef player, NetworkData data);

        protected void NotifyPlayerConnected(PlayerRef player)
        {
            if (!_connectedPlayers.Contains(player))
            {
                _connectedPlayers.Add(player);
                OnPlayerConnected?.Invoke(player);
            }
        }

        protected void NotifyPlayerDisconnected(PlayerRef player)
        {
            if (_connectedPlayers.Remove(player))
            {
                OnPlayerDisconnected?.Invoke(player);
            }
        }

        protected void NotifyDataReceived(NetworkData data)
        {
            OnDataReceived?.Invoke(data);
        }
    }

    #endregion

    #region 服务器连接实现

    /// <summary>
    /// 网络服务器连接
    /// </summary>
    public class NetworkServerConnection : NetworkConnection
    {
        private Dictionary<PlayerRef, ClientInfo> _clients = new Dictionary<PlayerRef, ClientInfo>();
        private int _nextPlayerId = 1;

        public override async Task StartServer(NetAddress address, int port)
        {
            try
            {
                // 模拟服务器启动
                await Task.Delay(100); // 模拟启动延迟

                _isConnected = true;
                UnityEngine.Debug.Log($"Server started on {address}:{port}");

                // 模拟一些客户端连接
                SimulateClientConnections();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Failed to start server: {ex.Message}");
                throw;
            }
        }

        public override Task ConnectToServer(NetAddress address, int port) // 移除async关键字，修复CS1998警告
        {
            throw new NotSupportedException("Server connection cannot connect to another server");
        }

        public override Task Disconnect() // 移除async关键字，修复CS1998警告
        {
            if (!_isConnected)
                return Task.CompletedTask;

            // 断开所有客户端
            var playersToDisconnect = new List<PlayerRef>(_connectedPlayers);
            foreach (var player in playersToDisconnect)
            {
                DisconnectClient(player);
            }

            _clients.Clear();
            _isConnected = false;

            UnityEngine.Debug.Log("Server stopped");
            return Task.CompletedTask;
        }

        public override void SendToAll(NetworkData data)
        {
            if (!_isConnected)
                return;

            foreach (var player in _connectedPlayers)
            {
                SendToPlayer(player, data);
            }
        }

        public override void SendToPlayer(PlayerRef player, NetworkData data)
        {
            if (!_isConnected || !_clients.ContainsKey(player))
                return;

            // 模拟网络发送
            UnityEngine.Debug.Log($"Sending data to player {player}: {data.GetType().Name}");
        }

        private void SimulateClientConnections()
        {
            // 模拟客户端连接（用于测试）
            // 在实际实现中，这里会处理真实的网络连接
        }

        public void AcceptClient(string clientId)
        {
            var player = new PlayerRef(_nextPlayerId++);
            var clientInfo = new ClientInfo
            {
                PlayerId = player,
                ClientId = clientId,
                ConnectedTime = DateTime.Now
            };

            _clients[player] = clientInfo;
            NotifyPlayerConnected(player);

            UnityEngine.Debug.Log($"Client {clientId} connected as player {player}");
        }

        public void DisconnectClient(PlayerRef player)
        {
            if (_clients.Remove(player))
            {
                NotifyPlayerDisconnected(player);
                UnityEngine.Debug.Log($"Player {player} disconnected");
            }
        }

        private class ClientInfo
        {
            public PlayerRef PlayerId;
            public string ClientId;
            public DateTime ConnectedTime;
        }
    }

    #endregion

    #region 客户端连接实现

    /// <summary>
    /// 网络客户端连接
    /// </summary>
    public class NetworkClientConnection : NetworkConnection
    {
        private PlayerRef _localPlayer;
        private bool _isConnecting;

        public PlayerRef LocalPlayer => _localPlayer;

        public override Task StartServer(NetAddress address, int port) // 移除async关键字，修复CS1998警告
        {
            throw new NotSupportedException("Client connection cannot start a server");
        }

        public override async Task ConnectToServer(NetAddress address, int port)
        {
            if (_isConnected || _isConnecting)
                return;

            _isConnecting = true;

            try
            {
                // 模拟连接过程
                await Task.Delay(500); // 模拟连接延迟

                // 模拟连接成功
                _isConnected = true;
                _isConnecting = false;
                _localPlayer = new PlayerRef(1); // 模拟分配的玩家ID

                NotifyPlayerConnected(_localPlayer);

                UnityEngine.Debug.Log($"Connected to server at {address}:{port} as player {_localPlayer}");
            }
            catch (Exception ex)
            {
                _isConnecting = false;
                UnityEngine.Debug.LogError($"Failed to connect to server: {ex.Message}");
                throw;
            }
        }

        public override Task Disconnect() // 移除async关键字，修复CS1998警告
        {
            if (!_isConnected)
                return Task.CompletedTask;

            _isConnected = false;

            if (_localPlayer.IsValid)
            {
                NotifyPlayerDisconnected(_localPlayer);
                _localPlayer = PlayerRef.None;
            }

            UnityEngine.Debug.Log("Disconnected from server");
            return Task.CompletedTask;
        }

        public override void SendToAll(NetworkData data)
        {
            // 客户端发送给服务器
            SendToServer(data);
        }

        public override void SendToPlayer(PlayerRef player, NetworkData data)
        {
            // 客户端只能发送给服务器
            SendToServer(data);
        }

        private void SendToServer(NetworkData data)
        {
            if (!_isConnected)
                return;

            // 模拟发送到服务器
            UnityEngine.Debug.Log($"Sending data to server: {data.GetType().Name}");
        }
    }

    #endregion

    #region 网络数据类型

    /// <summary>
    /// 网络数据基类
    /// </summary>
    [Serializable]
    public abstract class NetworkData
    {
        public uint Timestamp { get; set; }
        public PlayerRef Sender { get; set; }

        protected NetworkData()
        {
            Timestamp = (uint)(DateTime.Now.Ticks / TimeSpan.TicksPerMillisecond);
        }
    }

    /// <summary>
    /// 游戏状态数据 - 简化实现
    /// TODO: 实现完整的网络对象同步
    /// </summary>
    [Serializable]
    public class GameStateData : NetworkData
    {
        public Dictionary<NetworkId, byte[]> NetworkObjectStates { get; set; } = new Dictionary<NetworkId, byte[]>();
        public uint CurrentTick { get; set; }
        public float SimulationTime { get; set; }
    }

    /// <summary>
    /// 输入数据
    /// </summary>
    [Serializable]
    public class InputData : NetworkData
    {
        public INetworkInput Input { get; set; }
        public uint TargetTick { get; set; }
    }

    /// <summary>
    /// RPC数据包 - 暂时简化实现
    /// TODO: 实现完整的RPC系统
    /// </summary>
    [Serializable]
    public class RPCPacket : NetworkData
    {
        public string MethodName { get; set; }
        public byte[] Parameters { get; set; }
        // Sender 属性已在基类 NetworkData 中定义，无需重复
    }



    #endregion
}
