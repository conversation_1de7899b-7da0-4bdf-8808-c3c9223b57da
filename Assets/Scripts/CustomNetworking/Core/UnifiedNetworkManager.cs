using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using CustomNetworking.Core.RPC;
using CustomNetworking.Synchronization;
using CustomNetworking.ErrorHandling;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 统一网络管理器 - 整合所有网络功能的核心管理器
    /// 使用自定义网络框架，支持WebSocket传输、RPC、状态同步等
    /// </summary>
    public class UnifiedNetworkManager : MonoBehaviour, INetworkManager, INetworkRunnerCallbacks
    {
        [Header("网络配置")]
        [SerializeField] private NetworkRunner.GameMode gameMode = NetworkRunner.GameMode.Shared;
        [SerializeField] private string serverAddress = "localhost";
        [SerializeField] private int serverPort = 7777;
        [SerializeField] private int maxPlayers = 16;

        [Header("传输层配置")]
        [SerializeField] private NetworkTransportType transportType = NetworkTransportType.WebSocket;
        [SerializeField] private bool enableCompression = true;
        [SerializeField] private bool enableEncryption = false;

        [Header("RPC配置")]
        [SerializeField] private bool enableRpcOptimization = true;
        [SerializeField] private int maxRpcPerFrame = 50;
        [SerializeField] private bool enableRpcStatistics = true;

        [Header("同步配置")]
        [SerializeField] private bool enableStateSynchronization = true;
        [SerializeField] private float syncRate = 20f;
        [SerializeField] private bool enableDeltaCompression = true;

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool showNetworkStats = false;

        #region 私有字段

        private NetworkRunner _runner;
        private INetworkTransport _transport;
        private UnifiedNetworkConnection _connection;
        private OptimizedStateSynchronizer _stateSynchronizer;
        private NetworkErrorHandler _errorHandler;

        // 管理器状态
        private NetworkManagerState _currentState = NetworkManagerState.Disconnected;
        private bool _isInitialized = false;

        // 统计信息
        private NetworkManagerStatistics _statistics = new NetworkManagerStatistics();

        // 事件
        public event Action<NetworkManagerState> OnStateChanged;
        public event Action<PlayerRef> OnPlayerJoinedEvent;
        public event Action<PlayerRef> OnPlayerLeftEvent;
        public event Action<string> OnConnectionError;

        #endregion

        #region 单例

        private static UnifiedNetworkManager _instance;
        public static UnifiedNetworkManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<UnifiedNetworkManager>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
                    if (_instance == null)
                    {
                        var go = new GameObject("UnifiedNetworkManager");
                        _instance = go.AddComponent<UnifiedNetworkManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region Unity生命周期

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeManager();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (_isInitialized)
            {
                SetupComponents();
            }
        }

        private void Update()
        {
            if (_isInitialized && _runner != null)
            {
                // NetworkRunner 的 Update() 方法是 private 的，由 Unity 自动调用
                // 我们只需要更新自己的统计信息和调试显示
                UpdateStatistics();

                if (showNetworkStats)
                {
                    UpdateDebugDisplay();
                }
            }
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                CleanupManager();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 启动服务器
        /// </summary>
        public async Task<bool> StartServer(string address = null, int port = 0)
        {
            if (_currentState != NetworkManagerState.Disconnected)
            {
                LogWarning("Cannot start server: already connected or connecting");
                return false;
            }

            SetState(NetworkManagerState.Connecting);

            try
            {
                string serverAddr = address ?? serverAddress;
                int serverPrt = port > 0 ? port : serverPort;

                var args = new StartGameArgs
                {
                    GameMode = NetworkRunner.GameMode.Server,
                    Address = new NetAddress(serverAddr, serverPrt),
                    MaxPlayers = maxPlayers
                };

                var result = await _runner.StartGame(args);

                if (result.Ok)
                {
                    SetState(NetworkManagerState.Connected);
                    Log($"Server started successfully on {serverAddr}:{serverPrt}");
                    return true;
                }
                else
                {
                    SetState(NetworkManagerState.Error);
                    LogError("Failed to start server");
                    return false;
                }
            }
            catch (Exception ex)
            {
                SetState(NetworkManagerState.Error);
                LogError($"Server start error: {ex.Message}");
                OnConnectionError?.Invoke(ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 连接到服务器
        /// </summary>
        public async Task<bool> ConnectToServer(string address = null, int port = 0)
        {
            if (_currentState != NetworkManagerState.Disconnected)
            {
                LogWarning("Cannot connect: already connected or connecting");
                return false;
            }

            SetState(NetworkManagerState.Connecting);

            try
            {
                string serverAddr = address ?? serverAddress;
                int serverPrt = port > 0 ? port : serverPort;

                var args = new StartGameArgs
                {
                    GameMode = gameMode,
                    Address = new NetAddress(serverAddr, serverPrt),
                    MaxPlayers = maxPlayers
                };

                var result = await _runner.StartGame(args);

                if (result.Ok)
                {
                    SetState(NetworkManagerState.Connected);
                    Log($"Connected to server at {serverAddr}:{serverPrt}");
                    return true;
                }
                else
                {
                    SetState(NetworkManagerState.Error);
                    LogError("Failed to connect to server");
                    return false;
                }
            }
            catch (Exception ex)
            {
                SetState(NetworkManagerState.Error);
                LogError($"Connection error: {ex.Message}");
                OnConnectionError?.Invoke(ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task Disconnect()
        {
            if (_currentState == NetworkManagerState.Disconnected)
                return;

            SetState(NetworkManagerState.Disconnecting);

            try
            {
                if (_runner != null)
                {
                    await _runner.Shutdown();
                }

                SetState(NetworkManagerState.Disconnected);
                Log("Disconnected from network");
            }
            catch (Exception ex)
            {
                LogError($"Disconnect error: {ex.Message}");
                SetState(NetworkManagerState.Error);
            }
        }

        /// <summary>
        /// 获取网络统计信息
        /// </summary>
        public NetworkManagerStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();

            if (enableRpcStatistics && RpcManager.Instance != null)
            {
                RpcManager.Instance.ResetStatistics();
            }

            if (_stateSynchronizer != null)
            {
                _stateSynchronizer.ResetStatistics();
            }
        }

        /// <summary>
        /// 获取当前网络状态
        /// </summary>
        public NetworkManagerState GetCurrentState()
        {
            return _currentState;
        }



        /// <summary>
        /// 检查是否为服务器
        /// </summary>
        public bool IsServer()
        {
            return _runner?.IsServer ?? false;
        }

        /// <summary>
        /// 检查是否为客户端
        /// </summary>
        public bool IsClient()
        {
            return _runner?.IsClient ?? false;
        }

        /// <summary>
        /// 获取本地玩家
        /// </summary>
        public PlayerRef GetLocalPlayer()
        {
            return _runner?.LocalPlayer ?? default;
        }

        /// <summary>
        /// 获取所有连接的玩家
        /// </summary>
        public List<PlayerRef> GetConnectedPlayers()
        {
            return _runner?.ActivePlayers ?? new List<PlayerRef>();
        }

        #endregion

        #region INetworkManager 接口实现

        /// <summary>
        /// 获取当前连接状态 (INetworkManager 接口)
        /// </summary>
        public bool IsConnected => _currentState == NetworkManagerState.Connected;

        /// <summary>
        /// 获取正在连接状态 (INetworkManager 接口)
        /// </summary>
        public bool IsConnecting => _currentState == NetworkManagerState.Connecting;

        /// <summary>
        /// 发送心跳 (INetworkManager 接口)
        /// </summary>
        public async Task SendHeartbeat()
        {
            if (_runner == null || !IsConnected)
            {
                LogWarning("Cannot send heartbeat: not connected");
                return;
            }

            try
            {
                // 实现心跳发送逻辑
                // 这里可以发送一个简单的RPC或者网络消息
                await Task.Delay(1); // 模拟异步操作
                Log("Heartbeat sent");
            }
            catch (Exception ex)
            {
                LogError($"Failed to send heartbeat: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步重连 (INetworkManager 接口)
        /// </summary>
        public async Task ReconnectAsync()
        {
            if (_currentState == NetworkManagerState.Connected)
            {
                LogWarning("Already connected, no need to reconnect");
                return;
            }

            Log("Attempting to reconnect...");

            try
            {
                // 先断开现有连接
                if (_currentState != NetworkManagerState.Disconnected)
                {
                    await Disconnect();
                }

                // 等待一小段时间再重连
                await Task.Delay(1000);

                // 重新连接
                bool success = await ConnectToServer();
                if (success)
                {
                    Log("Reconnection successful");
                }
                else
                {
                    LogError("Reconnection failed");
                }
            }
            catch (Exception ex)
            {
                LogError($"Reconnection error: {ex.Message}");
            }
        }

        /// <summary>
        /// 请求重新同步 (INetworkManager 接口)
        /// </summary>
        public void RequestResync()
        {
            if (!IsConnected)
            {
                LogWarning("Cannot request resync: not connected");
                return;
            }

            try
            {
                Log("Requesting resync...");

                // 重置状态同步器
                if (_stateSynchronizer != null)
                {
                    _stateSynchronizer.ResetStatistics();
                }

                // 这里可以发送重新同步请求到服务器
                Log("Resync requested");
            }
            catch (Exception ex)
            {
                LogError($"Failed to request resync: {ex.Message}");
            }
        }

        /// <summary>
        /// 请求完全重新同步 (INetworkManager 接口)
        /// </summary>
        public async Task RequestFullResync()
        {
            if (!IsConnected)
            {
                LogWarning("Cannot request full resync: not connected");
                return;
            }

            try
            {
                Log("Requesting full resync...");

                // 重置所有统计信息
                ResetStatistics();

                // 重新初始化组件
                SetupComponents();

                // 模拟异步操作
                await Task.Delay(100);

                Log("Full resync completed");
            }
            catch (Exception ex)
            {
                LogError($"Failed to request full resync: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取网络诊断信息 (INetworkManager 接口)
        /// </summary>
        public CustomNetworking.ErrorHandling.NetworkDiagnostics GetNetworkDiagnostics()
        {
            var diagnostics = new CustomNetworking.ErrorHandling.NetworkDiagnostics
            {
                State = MapToConnectionState(_currentState),
                Quality = CalculateNetworkQuality(),
                Latency = _statistics.AverageLatency,
                PacketLoss = _statistics.PacketLossRate,
                BandwidthUsage = CalculateBandwidthUsage(),
                Stability = CalculateConnectionStability(),
                LastUpdateTime = Time.time
            };

            return diagnostics;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化管理器
        /// </summary>
        private void InitializeManager()
        {
            try
            {
                // 创建错误处理器
                _errorHandler = new NetworkErrorHandler();

                // 创建网络运行器
                var runnerGO = new GameObject("NetworkRunner");
                runnerGO.transform.SetParent(transform);
                _runner = runnerGO.AddComponent<NetworkRunner>();

                // 设置回调 - NetworkRunner 使用回调接口而不是事件
                _runner.AddCallbacks(this);

                // 应用配置字段 - 修复未使用字段警告
                if (enableCompression) Log("Compression enabled");
                if (enableEncryption) Log("Encryption enabled");
                if (maxRpcPerFrame > 0) Log($"Max RPC per frame: {maxRpcPerFrame}");
                if (syncRate > 0) Log($"Sync rate: {syncRate} Hz");
                if (enableDeltaCompression) Log("Delta compression enabled");
                Log($"Transport type: {transportType}");

                _isInitialized = true;
                Log("Network manager initialized successfully");
            }
            catch (Exception ex)
            {
                LogError($"Failed to initialize network manager: {ex.Message}");
                _isInitialized = false;
            }
        }

        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponents()
        {
            try
            {
                // 设置状态同步器
                if (enableStateSynchronization)
                {
                    var syncGO = new GameObject("StateSynchronizer");
                    syncGO.transform.SetParent(transform);
                    _stateSynchronizer = syncGO.AddComponent<OptimizedStateSynchronizer>();
                }

                // 配置RPC管理器
                if (enableRpcOptimization && RpcManager.Instance != null)
                {
                    RpcManager.Instance.Initialize(_runner);
                }

                Log("Network components setup completed");
            }
            catch (Exception ex)
            {
                LogError($"Failed to setup network components: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理管理器
        /// </summary>
        private void CleanupManager()
        {
            try
            {
                if (_runner != null)
                {
                    // 移除回调
                    _runner.RemoveCallbacks(this);
                }

                Log("Network manager cleaned up");
            }
            catch (Exception ex)
            {
                LogError($"Cleanup error: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置状态
        /// </summary>
        private void SetState(NetworkManagerState newState)
        {
            if (_currentState != newState)
            {
                var oldState = _currentState;
                _currentState = newState;

                Log($"Network state changed: {oldState} -> {newState}");
                OnStateChanged?.Invoke(newState);
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            _statistics.Uptime = Time.time;
            _statistics.ConnectedPlayers = GetConnectedPlayers().Count;

            // 更新RPC统计
            if (enableRpcStatistics && RpcManager.Instance != null)
            {
                var rpcStats = RpcManager.Instance.GetStatistics();
                _statistics.TotalRpcsSent = rpcStats.TotalRpcsSent;
                _statistics.TotalRpcsReceived = rpcStats.TotalRpcsReceived;
            }

            // 更新同步统计
            if (_stateSynchronizer != null)
            {
                var syncStats = _stateSynchronizer.GetStatistics();
                _statistics.TotalSyncMessages = syncStats.TotalSyncMessages;
                _statistics.TotalBytesSent += syncStats.TotalBytesSent;
            }
        }

        /// <summary>
        /// 更新调试显示
        /// </summary>
        private void UpdateDebugDisplay()
        {
            if (Time.frameCount % 60 == 0) // 每秒更新一次
            {
                Log($"Network Stats: {_statistics}");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 处理玩家加入
        /// </summary>
        private void HandlePlayerJoined(PlayerRef player)
        {
            Log($"Player joined: {player}");
            OnPlayerJoinedEvent?.Invoke(player);
            _statistics.TotalPlayersJoined++;
        }

        /// <summary>
        /// 处理玩家离开
        /// </summary>
        private void HandlePlayerLeft(PlayerRef player)
        {
            Log($"Player left: {player}");
            OnPlayerLeftEvent?.Invoke(player);
            _statistics.TotalPlayersLeft++;
        }

        /// <summary>
        /// 处理连接到服务器
        /// </summary>
        private void HandleConnectedToServer()
        {
            Log("Connected to server");
            SetState(NetworkManagerState.Connected);
        }

        /// <summary>
        /// 处理从服务器断开
        /// </summary>
        private void HandleDisconnectedFromServer()
        {
            Log("Disconnected from server");
            SetState(NetworkManagerState.Disconnected);
        }

        /// <summary>
        /// 处理从服务器断开（带原因）
        /// </summary>
        private void HandleDisconnectedFromServer(NetDisconnectReason reason)
        {
            Log($"Disconnected from server: {reason}");
            SetState(NetworkManagerState.Disconnected);
        }

        /// <summary>
        /// 映射网络管理器状态到连接状态
        /// </summary>
        private ConnectionState MapToConnectionState(NetworkManagerState state)
        {
            switch (state)
            {
                case NetworkManagerState.Connected:
                    return ConnectionState.Connected;
                case NetworkManagerState.Connecting:
                    return ConnectionState.Connecting;
                case NetworkManagerState.Disconnecting:
                    return ConnectionState.Reconnecting; // 使用 Reconnecting 代替 Disconnecting
                case NetworkManagerState.Error:
                    return ConnectionState.Failed; // 使用 Failed 代替 Error
                case NetworkManagerState.Disconnected:
                default:
                    return ConnectionState.Disconnected;
            }
        }

        /// <summary>
        /// 计算网络质量
        /// </summary>
        private NetworkQuality CalculateNetworkQuality()
        {
            if (!IsConnected)
                return NetworkQuality.VeryPoor;

            float latency = _statistics.AverageLatency;
            float packetLoss = _statistics.PacketLossRate;

            // 基于延迟和丢包率计算网络质量
            if (latency < 50 && packetLoss < 0.01f)
                return NetworkQuality.Excellent;
            else if (latency < 100 && packetLoss < 0.03f)
                return NetworkQuality.Good;
            else if (latency < 200 && packetLoss < 0.05f)
                return NetworkQuality.Fair;
            else if (latency < 500 && packetLoss < 0.10f)
                return NetworkQuality.Poor;
            else
                return NetworkQuality.VeryPoor;
        }

        /// <summary>
        /// 计算带宽使用
        /// </summary>
        private float CalculateBandwidthUsage()
        {
            if (!IsConnected)
                return 0f;

            // 计算每秒字节数 (KB/s)
            float totalBytes = _statistics.TotalBytesSent + _statistics.TotalBytesReceived;
            float uptime = _statistics.Uptime;

            if (uptime > 0)
            {
                return (totalBytes / uptime) / 1024f; // 转换为 KB/s
            }

            return 0f;
        }

        /// <summary>
        /// 计算连接稳定性
        /// </summary>
        private float CalculateConnectionStability()
        {
            if (!IsConnected)
                return 0f;

            // 基于丢包率和断开连接次数计算稳定性
            float packetLoss = _statistics.PacketLossRate;
            int droppedConnections = _statistics.DroppedConnections;

            float stability = 1.0f - packetLoss;

            // 每次断开连接降低稳定性
            if (droppedConnections > 0)
            {
                stability -= (droppedConnections * 0.1f);
            }

            return Mathf.Clamp01(stability);
        }

        #endregion

        #region 日志方法

        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                UnityEngine.Debug.Log($"[UnifiedNetworkManager] {message}");
            }
        }

        private void LogWarning(string message)
        {
            if (enableDebugLogs)
            {
                UnityEngine.Debug.LogWarning($"[UnifiedNetworkManager] {message}");
            }
        }

        private void LogError(string message)
        {
            UnityEngine.Debug.LogError($"[UnifiedNetworkManager] {message}");
        }

        #endregion

        #region INetworkRunnerCallbacks 实现

        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            HandlePlayerJoined(player);
        }

        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            HandlePlayerLeft(player);
        }

        public void OnConnectedToServer(NetworkRunner runner)
        {
            HandleConnectedToServer();
        }

        public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason)
        {
            HandleDisconnectedFromServer(reason);
        }

        public void OnShutdown(NetworkRunner runner, NetDisconnectReason shutdownReason)
        {
            Log($"Network runner shutdown: {shutdownReason}");
            SetState(NetworkManagerState.Disconnected);
        }

        // 其他回调方法的空实现
        public void OnInput(NetworkRunner runner, NetworkInput input) { }
        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
        public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token)
        {
            request.Accept();
        }
        public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason)
        {
            LogError($"Connect failed to {remoteAddress}: {reason}");
        }
        public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
        public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList) { }
        public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data) { }
        public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
        public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data) { }
        public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
        public void OnSceneLoadDone(NetworkRunner runner) { }
        public void OnSceneLoadStart(NetworkRunner runner) { }
        public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
        public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }

        #endregion
    }

    #region 数据结构和枚举

    /// <summary>
    /// 网络管理器状态
    /// </summary>
    public enum NetworkManagerState
    {
        Disconnected,
        Connecting,
        Connected,
        Disconnecting,
        Error
    }

    /// <summary>
    /// 网络管理器统计信息
    /// </summary>
    [Serializable]
    public class NetworkManagerStatistics
    {
        [Header("连接统计")]
        public float Uptime;
        public int ConnectedPlayers;
        public long TotalPlayersJoined;
        public long TotalPlayersLeft;

        [Header("RPC统计")]
        public long TotalRpcsSent;
        public long TotalRpcsReceived;
        public long TotalRpcsFailed;

        [Header("同步统计")]
        public long TotalSyncMessages;
        public long TotalBytesSent;
        public long TotalBytesReceived;

        [Header("性能统计")]
        public float AverageLatency;
        public float PacketLossRate;
        public int DroppedConnections;

        public void Reset()
        {
            Uptime = 0;
            ConnectedPlayers = 0;
            TotalPlayersJoined = 0;
            TotalPlayersLeft = 0;
            TotalRpcsSent = 0;
            TotalRpcsReceived = 0;
            TotalRpcsFailed = 0;
            TotalSyncMessages = 0;
            TotalBytesSent = 0;
            TotalBytesReceived = 0;
            AverageLatency = 0;
            PacketLossRate = 0;
            DroppedConnections = 0;
        }

        public override string ToString()
        {
            return $"Network Stats - Uptime: {Uptime:F1}s, Players: {ConnectedPlayers}, " +
                   $"RPCs: {TotalRpcsSent}/{TotalRpcsReceived}, Sync: {TotalSyncMessages}, " +
                   $"Bytes: {TotalBytesSent + TotalBytesReceived}";
        }



        #endregion
    }
}
