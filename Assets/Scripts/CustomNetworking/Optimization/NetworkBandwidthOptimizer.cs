using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Optimization
{
    /// <summary>
    /// 网络带宽优化器 - 管理网络同步的带宽使用
    /// </summary>
    public class NetworkBandwidthOptimizer : MonoBehaviour
    {
        [Header("优化设置")]
        [SerializeField] private bool enableOptimization = true;
        [SerializeField] private float maxBandwidthKbps = 100f; // 最大带宽 (KB/s)
        [SerializeField] private int maxRpcPerSecond = 50; // 最大RPC频率
        [SerializeField] private float priorityUpdateInterval = 0.1f; // 优先级更新间隔

        [Header("调试信息")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private float debugUpdateInterval = 1f;

        // 单例实例
        private static NetworkBandwidthOptimizer _instance;
        public static NetworkBandwidthOptimizer Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<NetworkBandwidthOptimizer>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("NetworkBandwidthOptimizer");
                        _instance = go.AddComponent<NetworkBandwidthOptimizer>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        // 带宽监控
        private float _currentBandwidthUsage;
        private int _currentRpcCount;
        private float _lastResetTime;
        private Queue<float> _bandwidthHistory = new Queue<float>();

        // 优先级管理
        private Dictionary<NetworkBehaviour, float> _objectPriorities = new Dictionary<NetworkBehaviour, float>();
        private List<NetworkBehaviour> _registeredObjects = new List<NetworkBehaviour>();

        // 调试信息
        private float _lastDebugUpdate;
        private int _totalRpcsSent;
        private float _totalBandwidthUsed;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }

            _lastResetTime = Time.time;
            _lastDebugUpdate = Time.time;
        }

        private void Update()
        {
            if (!enableOptimization) return;

            UpdateBandwidthMonitoring();
            UpdatePriorities();

            if (showDebugInfo)
            {
                UpdateDebugInfo();
            }
        }

        /// <summary>
        /// 注册网络对象
        /// </summary>
        public void RegisterNetworkObject(NetworkBehaviour networkObject)
        {
            if (!_registeredObjects.Contains(networkObject))
            {
                _registeredObjects.Add(networkObject);
                _objectPriorities[networkObject] = 1f; // 默认优先级
            }
        }

        /// <summary>
        /// 取消注册网络对象
        /// </summary>
        public void UnregisterNetworkObject(NetworkBehaviour networkObject)
        {
            _registeredObjects.Remove(networkObject);
            _objectPriorities.Remove(networkObject);
        }

        /// <summary>
        /// 检查是否可以发送RPC
        /// </summary>
        public bool CanSendRpc(NetworkBehaviour sender, int dataSize)
        {
            if (!enableOptimization) return true;

            // 检查RPC频率限制
            if (_currentRpcCount >= maxRpcPerSecond)
            {
                return false;
            }

            // 检查带宽限制
            float estimatedBandwidth = _currentBandwidthUsage + (dataSize / 1024f);
            if (estimatedBandwidth > maxBandwidthKbps)
            {
                return false;
            }

            // 检查对象优先级
            float priority = GetObjectPriority(sender);
            if (priority < 0.1f) // 低优先级对象可能被限制
            {
                return UnityEngine.Random.value < priority;
            }

            return true;
        }

        /// <summary>
        /// 记录RPC发送
        /// </summary>
        public void RecordRpcSent(NetworkBehaviour sender, int dataSize)
        {
            if (!enableOptimization) return;

            _currentRpcCount++;
            _currentBandwidthUsage += dataSize / 1024f; // 转换为KB
            _totalRpcsSent++;
            _totalBandwidthUsed += dataSize / 1024f;
        }

        /// <summary>
        /// 获取对象优先级
        /// </summary>
        public float GetObjectPriority(NetworkBehaviour networkObject)
        {
            if (_objectPriorities.TryGetValue(networkObject, out float priority))
            {
                return priority;
            }
            return 1f; // 默认优先级
        }

        /// <summary>
        /// 设置对象优先级
        /// </summary>
        public void SetObjectPriority(NetworkBehaviour networkObject, float priority)
        {
            _objectPriorities[networkObject] = Mathf.Clamp01(priority);
        }

        /// <summary>
        /// 更新带宽监控
        /// </summary>
        private void UpdateBandwidthMonitoring()
        {
            float currentTime = Time.time;

            // 每秒重置计数器
            if (currentTime - _lastResetTime >= 1f)
            {
                _bandwidthHistory.Enqueue(_currentBandwidthUsage);

                // 保持历史记录在合理范围内
                if (_bandwidthHistory.Count > 10)
                {
                    _bandwidthHistory.Dequeue();
                }

                _currentBandwidthUsage = 0f;
                _currentRpcCount = 0;
                _lastResetTime = currentTime;
            }
        }

        /// <summary>
        /// 更新对象优先级
        /// </summary>
        private void UpdatePriorities()
        {
            float currentTime = Time.time;

            if (currentTime - _lastResetTime < priorityUpdateInterval)
                return;

            // 基于距离和重要性计算优先级
            Camera mainCamera = Camera.main;
            if (mainCamera == null) return;

            Vector3 cameraPosition = mainCamera.transform.position;

            foreach (var networkObject in _registeredObjects)
            {
                if (networkObject == null) continue;

                float priority = CalculateObjectPriority(networkObject, cameraPosition);
                _objectPriorities[networkObject] = priority;
            }
        }

        /// <summary>
        /// 计算对象优先级
        /// </summary>
        private float CalculateObjectPriority(NetworkBehaviour networkObject, Vector3 cameraPosition)
        {
            float basePriority = 1f;

            // 基于距离的优先级
            float distance = Vector3.Distance(networkObject.transform.position, cameraPosition);
            float distancePriority = Mathf.Clamp01(1f - (distance / 50f)); // 50米外优先级降低

            // 基于对象类型的优先级
            float typePriority = GetTypePriority(networkObject);

            // 基于可见性的优先级
            float visibilityPriority = IsObjectVisible(networkObject, cameraPosition) ? 1f : 0.3f;

            return basePriority * distancePriority * typePriority * visibilityPriority;
        }

        /// <summary>
        /// 获取类型优先级
        /// </summary>
        private float GetTypePriority(NetworkBehaviour networkObject)
        {
            // 玩家对象优先级最高 - 使用字符串比较避免直接类型引用
            if (networkObject.GetComponent("PlayerController") != null)
                return 1f;

            // 游戏管理器优先级高 - 使用类型名称比较
            if (networkObject.GetType().Name == "GameManager")
                return 0.9f;

            // 其他对象默认优先级
            return 0.5f;
        }

        /// <summary>
        /// 检查对象是否可见
        /// </summary>
        private bool IsObjectVisible(NetworkBehaviour networkObject, Vector3 cameraPosition)
        {
            // 简单的可见性检查
            Renderer renderer = networkObject.GetComponent<Renderer>();
            if (renderer == null) return true; // 没有渲染器的对象默认可见

            return renderer.isVisible;
        }

        /// <summary>
        /// 更新调试信息
        /// </summary>
        private void UpdateDebugInfo()
        {
            float currentTime = Time.time;

            if (currentTime - _lastDebugUpdate >= debugUpdateInterval)
            {
                float avgBandwidth = GetAverageBandwidth();

                UnityEngine.Debug.Log($"[NetworkOptimizer] " +
                         $"Current: {_currentBandwidthUsage:F2} KB/s, " +
                         $"Average: {avgBandwidth:F2} KB/s, " +
                         $"RPC/s: {_currentRpcCount}, " +
                         $"Objects: {_registeredObjects.Count}");

                _lastDebugUpdate = currentTime;
            }
        }

        /// <summary>
        /// 获取平均带宽使用
        /// </summary>
        private float GetAverageBandwidth()
        {
            if (_bandwidthHistory.Count == 0) return 0f;

            float total = 0f;
            foreach (float bandwidth in _bandwidthHistory)
            {
                total += bandwidth;
            }

            return total / _bandwidthHistory.Count;
        }

        /// <summary>
        /// 设置最大带宽限制
        /// </summary>
        public void SetMaxBandwidth(float bandwidthKbps)
        {
            maxBandwidthKbps = Mathf.Max(1f, bandwidthKbps);
        }

        /// <summary>
        /// 获取当前最大带宽设置
        /// </summary>
        public float GetMaxBandwidth()
        {
            return maxBandwidthKbps;
        }

        /// <summary>
        /// 获取优化统计信息
        /// </summary>
        public OptimizationStats GetStats()
        {
            return new OptimizationStats
            {
                CurrentBandwidthKbps = _currentBandwidthUsage,
                AverageBandwidthKbps = GetAverageBandwidth(),
                CurrentRpcPerSecond = _currentRpcCount,
                TotalRpcsSent = _totalRpcsSent,
                TotalBandwidthUsedKb = _totalBandwidthUsed,
                RegisteredObjectCount = _registeredObjects.Count
            };
        }
    }

    /// <summary>
    /// 优化统计信息
    /// </summary>
    [Serializable]
    public struct OptimizationStats
    {
        public float CurrentBandwidthKbps;
        public float AverageBandwidthKbps;
        public int CurrentRpcPerSecond;
        public int TotalRpcsSent;
        public float TotalBandwidthUsedKb;
        public int RegisteredObjectCount;
    }
}
