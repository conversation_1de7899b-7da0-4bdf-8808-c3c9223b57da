using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Rendering;

namespace GooseDuckKill.Lighting
{
    /// <summary>
    /// 光照管理器
    /// 统一管理不同场景的光照设置和环境光
    /// </summary>
    public class LightingManager : MonoBehaviour
    {
        [Header("场景光照配置")]
        [SerializeField] private LightingProfile mainMenuProfile;
        [SerializeField] private LightingProfile lobbyProfile;
        [SerializeField] private LightingProfile gameProfile;
        
        [Header("环境光设置")]
        [SerializeField] private bool manageAmbientLighting = true;
        [SerializeField] private AmbientMode ambientMode = AmbientMode.Trilight;
        
        [Header("过渡设置")]
        [SerializeField] private float transitionDuration = 2f;
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        private LightingProfile currentProfile;
        private bool isTransitioning = false;
        
        [System.Serializable]
        public class LightingProfile
        {
            [Header("主光源设置")]
            public float intensity = 1.2f;
            public Color lightColor = Color.white;
            public Vector3 rotation = new Vector3(50f, -30f, 0f);
            
            [Header("阴影设置")]
            public LightShadows shadowType = LightShadows.Soft;
            public float shadowStrength = 0.8f;
            
            [Header("环境光设置")]
            public Color ambientSkyColor = new Color(0.54f, 0.58f, 0.66f);
            public Color ambientEquatorColor = new Color(0.38f, 0.38f, 0.35f);
            public Color ambientGroundColor = new Color(0.11f, 0.11f, 0.11f);
            public float ambientIntensity = 1f;
            
            [Header("雾效设置")]
            public bool enableFog = false;
            public Color fogColor = Color.gray;
            public float fogDensity = 0.01f;
            public float fogStartDistance = 0f;
            public float fogEndDistance = 300f;
            
            [Header("天空盒设置")]
            public Material skyboxMaterial;
            public float skyboxExposure = 1f;
        }
        
        private void Awake()
        {
            // 确保只有一个光照管理器
            if (FindObjectsOfType<LightingManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            
            DontDestroyOnLoad(gameObject);
        }
        
        private void Start()
        {
            // 初始化默认配置
            InitializeDefaultProfiles();
            
            // 应用当前场景的光照设置
            ApplyLightingForCurrentScene();
            
            // 监听场景切换
            SceneManager.sceneLoaded += OnSceneLoaded;
        }
        
        private void OnDestroy()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }
        
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            ApplyLightingForCurrentScene();
        }
        
        /// <summary>
        /// 初始化默认配置
        /// </summary>
        private void InitializeDefaultProfiles()
        {
            if (mainMenuProfile == null)
            {
                mainMenuProfile = CreateDefaultMainMenuProfile();
            }
            
            if (lobbyProfile == null)
            {
                lobbyProfile = CreateDefaultLobbyProfile();
            }
            
            if (gameProfile == null)
            {
                gameProfile = CreateDefaultGameProfile();
            }
        }
        
        /// <summary>
        /// 创建默认主菜单配置
        /// </summary>
        private LightingProfile CreateDefaultMainMenuProfile()
        {
            return new LightingProfile
            {
                intensity = 1.2f,
                lightColor = Color.white,
                rotation = new Vector3(50f, -30f, 0f),
                shadowType = Application.isMobilePlatform ? LightShadows.None : LightShadows.Soft,
                shadowStrength = 0.8f,
                ambientSkyColor = new Color(0.54f, 0.58f, 0.66f),
                ambientEquatorColor = new Color(0.38f, 0.38f, 0.35f),
                ambientGroundColor = new Color(0.11f, 0.11f, 0.11f),
                ambientIntensity = 1f,
                enableFog = false
            };
        }
        
        /// <summary>
        /// 创建默认大厅配置
        /// </summary>
        private LightingProfile CreateDefaultLobbyProfile()
        {
            return new LightingProfile
            {
                intensity = 1.1f,
                lightColor = new Color(1f, 0.95f, 0.8f), // 稍微暖一点
                rotation = new Vector3(45f, -20f, 0f),
                shadowType = Application.isMobilePlatform ? LightShadows.None : LightShadows.Hard,
                shadowStrength = 0.7f,
                ambientSkyColor = new Color(0.5f, 0.55f, 0.6f),
                ambientEquatorColor = new Color(0.35f, 0.35f, 0.32f),
                ambientGroundColor = new Color(0.1f, 0.1f, 0.1f),
                ambientIntensity = 0.9f,
                enableFog = false
            };
        }
        
        /// <summary>
        /// 创建默认游戏配置
        /// </summary>
        private LightingProfile CreateDefaultGameProfile()
        {
            return new LightingProfile
            {
                intensity = 1f,
                lightColor = new Color(0.9f, 0.9f, 1f), // 稍微冷一点
                rotation = new Vector3(60f, -45f, 0f),
                shadowType = Application.isMobilePlatform ? LightShadows.None : LightShadows.Soft,
                shadowStrength = 0.9f,
                ambientSkyColor = new Color(0.4f, 0.45f, 0.55f),
                ambientEquatorColor = new Color(0.3f, 0.3f, 0.28f),
                ambientGroundColor = new Color(0.08f, 0.08f, 0.08f),
                ambientIntensity = 0.8f,
                enableFog = true,
                fogColor = new Color(0.5f, 0.5f, 0.6f),
                fogDensity = 0.005f
            };
        }
        
        /// <summary>
        /// 为当前场景应用光照设置
        /// </summary>
        private void ApplyLightingForCurrentScene()
        {
            string sceneName = SceneManager.GetActiveScene().name;
            LightingProfile profile = GetProfileForScene(sceneName);
            
            if (profile != null)
            {
                ApplyLightingProfile(profile);
            }
        }
        
        /// <summary>
        /// 获取指定场景的光照配置
        /// </summary>
        private LightingProfile GetProfileForScene(string sceneName)
        {
            switch (sceneName.ToLower())
            {
                case "mainmenu":
                case "main menu":
                    return mainMenuProfile;
                    
                case "lobby":
                    return lobbyProfile;
                    
                case "skeld":
                case "mira":
                case "polus":
                    return gameProfile;
                    
                default:
                    return mainMenuProfile; // 默认使用主菜单配置
            }
        }
        
        /// <summary>
        /// 应用光照配置
        /// </summary>
        public void ApplyLightingProfile(LightingProfile profile)
        {
            if (profile == null) return;
            
            currentProfile = profile;
            
            // 配置主光源
            ConfigureDirectionalLight(profile);
            
            // 配置环境光
            if (manageAmbientLighting)
            {
                ConfigureAmbientLighting(profile);
            }
            
            // 配置雾效
            ConfigureFog(profile);
            
            // 配置天空盒
            ConfigureSkybox(profile);
            
            Debug.Log($"应用光照配置: {SceneManager.GetActiveScene().name}");
        }
        
        /// <summary>
        /// 配置主光源
        /// </summary>
        private void ConfigureDirectionalLight(LightingProfile profile)
        {
            var directionalLight = FindObjectOfType<Light>();
            if (directionalLight == null || directionalLight.type != LightType.Directional)
            {
                // 创建主光源
                var lightGO = new GameObject("Directional Light");
                directionalLight = lightGO.AddComponent<Light>();
                directionalLight.type = LightType.Directional;
            }
            
            directionalLight.intensity = profile.intensity;
            directionalLight.color = profile.lightColor;
            directionalLight.transform.rotation = Quaternion.Euler(profile.rotation);
            directionalLight.shadows = profile.shadowType;
            directionalLight.shadowStrength = profile.shadowStrength;
        }
        
        /// <summary>
        /// 配置环境光
        /// </summary>
        private void ConfigureAmbientLighting(LightingProfile profile)
        {
            RenderSettings.ambientMode = ambientMode;
            RenderSettings.ambientSkyColor = profile.ambientSkyColor;
            RenderSettings.ambientEquatorColor = profile.ambientEquatorColor;
            RenderSettings.ambientGroundColor = profile.ambientGroundColor;
            RenderSettings.ambientIntensity = profile.ambientIntensity;
        }
        
        /// <summary>
        /// 配置雾效
        /// </summary>
        private void ConfigureFog(LightingProfile profile)
        {
            RenderSettings.fog = profile.enableFog;
            if (profile.enableFog)
            {
                RenderSettings.fogColor = profile.fogColor;
                RenderSettings.fogMode = FogMode.ExponentialSquared;
                RenderSettings.fogDensity = profile.fogDensity;
                RenderSettings.fogStartDistance = profile.fogStartDistance;
                RenderSettings.fogEndDistance = profile.fogEndDistance;
            }
        }
        
        /// <summary>
        /// 配置天空盒
        /// </summary>
        private void ConfigureSkybox(LightingProfile profile)
        {
            if (profile.skyboxMaterial != null)
            {
                RenderSettings.skybox = profile.skyboxMaterial;
                RenderSettings.skybox.SetFloat("_Exposure", profile.skyboxExposure);
            }
        }
        
        /// <summary>
        /// 平滑过渡到新的光照配置
        /// </summary>
        public void TransitionToProfile(LightingProfile newProfile)
        {
            if (isTransitioning || newProfile == null) return;
            
            StartCoroutine(SmoothTransition(currentProfile, newProfile));
        }
        
        private System.Collections.IEnumerator SmoothTransition(LightingProfile from, LightingProfile to)
        {
            isTransitioning = true;
            float elapsed = 0f;
            
            var directionalLight = FindObjectOfType<Light>();
            if (directionalLight == null) yield break;
            
            while (elapsed < transitionDuration)
            {
                elapsed += Time.deltaTime;
                float progress = transitionCurve.Evaluate(elapsed / transitionDuration);
                
                // 插值光照参数
                directionalLight.intensity = Mathf.Lerp(from.intensity, to.intensity, progress);
                directionalLight.color = Color.Lerp(from.lightColor, to.lightColor, progress);
                
                // 插值环境光
                if (manageAmbientLighting)
                {
                    RenderSettings.ambientSkyColor = Color.Lerp(from.ambientSkyColor, to.ambientSkyColor, progress);
                    RenderSettings.ambientEquatorColor = Color.Lerp(from.ambientEquatorColor, to.ambientEquatorColor, progress);
                    RenderSettings.ambientGroundColor = Color.Lerp(from.ambientGroundColor, to.ambientGroundColor, progress);
                    RenderSettings.ambientIntensity = Mathf.Lerp(from.ambientIntensity, to.ambientIntensity, progress);
                }
                
                yield return null;
            }
            
            // 应用最终配置
            ApplyLightingProfile(to);
            isTransitioning = false;
        }
        
        /// <summary>
        /// 获取移动端优化配置
        /// </summary>
        public static LightingProfile GetMobileOptimizedProfile()
        {
            return new LightingProfile
            {
                intensity = 1f,
                lightColor = Color.white,
                rotation = new Vector3(50f, -30f, 0f),
                shadowType = LightShadows.None,
                shadowStrength = 0f,
                ambientIntensity = 0.8f,
                enableFog = false
            };
        }
        
        /// <summary>
        /// 获取桌面端高质量配置
        /// </summary>
        public static LightingProfile GetDesktopOptimizedProfile()
        {
            return new LightingProfile
            {
                intensity = 1.2f,
                lightColor = Color.white,
                rotation = new Vector3(50f, -30f, 0f),
                shadowType = LightShadows.Soft,
                shadowStrength = 0.8f,
                ambientIntensity = 1f,
                enableFog = true,
                fogDensity = 0.01f
            };
        }
    }
}
