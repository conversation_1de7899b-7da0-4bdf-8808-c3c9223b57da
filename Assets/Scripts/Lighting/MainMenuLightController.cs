using UnityEngine;
using UnityEngine.Rendering;

namespace GooseDuckKill.Lighting
{
    /// <summary>
    /// MainMenu 场景光照控制器
    /// 自动配置和动态控制 Directional Light 参数
    /// </summary>
    public class MainMenuLightController : MonoBehaviour
    {
        [Header("光照基础设置")]
        [SerializeField] private Light directionalLight;
        [SerializeField] private float intensity = 1.2f;
        [SerializeField] private Color lightColor = Color.white;
        [SerializeField] private Vector3 lightRotation = new Vector3(50f, -30f, 0f);
        
        [Header("阴影设置")]
        [SerializeField] private LightShadows shadowType = LightShadows.Soft;
        [SerializeField] private float shadowStrength = 0.8f;
        [SerializeField] private float shadowBias = 0.05f;
        [SerializeField] private float shadowNormalBias = 0.4f;
        [SerializeField] private float shadowNearPlane = 0.1f;
        
        [Header("动态光照效果")]
        [SerializeField] private bool enableDynamicLighting = true;
        [SerializeField] private bool enableIntensityPulse = false;
        [SerializeField] private float pulseAmplitude = 0.2f;
        [SerializeField] private float pulseSpeed = 1f;
        [SerializeField] private bool enableColorShift = false;
        [SerializeField] private Color[] colorPalette = { Color.white, new Color(1f, 0.95f, 0.8f), new Color(0.9f, 0.9f, 1f) };
        [SerializeField] private float colorShiftSpeed = 0.5f;
        
        [Header("时间模拟")]
        [SerializeField] private bool enableTimeSimulation = false;
        [SerializeField] private float dayDuration = 120f; // 2分钟一个完整周期
        [SerializeField] private AnimationCurve intensityCurve = AnimationCurve.EaseInOut(0, 0.3f, 1, 1.2f);
        [SerializeField] private Gradient timeColorGradient;
        
        [Header("平台优化")]
        [SerializeField] private bool adaptToMobile = true;
        [SerializeField] private LightingQuality mobileQuality = LightingQuality.Low;
        [SerializeField] private LightingQuality desktopQuality = LightingQuality.High;
        
        // 私有变量
        private float originalIntensity;
        private Color originalColor;
        private Vector3 originalRotation;
        private float timeOffset;
        private int currentColorIndex = 0;
        private float colorTransitionTime = 0f;
        
        // 光照质量枚举
        public enum LightingQuality
        {
            Low,    // 移动端低质量
            Medium, // 中等质量
            High    // 桌面端高质量
        }
        
        private void Awake()
        {
            // 自动查找 Directional Light
            if (directionalLight == null)
            {
                directionalLight = FindObjectOfType<Light>();
                if (directionalLight != null && directionalLight.type != LightType.Directional)
                {
                    directionalLight = null;
                }
            }
            
            // 如果没找到，创建一个
            if (directionalLight == null)
            {
                CreateDirectionalLight();
            }
        }
        
        private void Start()
        {
            InitializeLighting();
            ApplyPlatformOptimizations();
            
            // 初始化时间模拟的颜色渐变
            if (timeColorGradient == null)
            {
                InitializeTimeColorGradient();
            }
        }
        
        private void Update()
        {
            if (!enableDynamicLighting || directionalLight == null) return;
            
            UpdateDynamicEffects();
            
            if (enableTimeSimulation)
            {
                UpdateTimeSimulation();
            }
        }
        
        /// <summary>
        /// 创建 Directional Light
        /// </summary>
        private void CreateDirectionalLight()
        {
            var lightGO = new GameObject("Directional Light");
            lightGO.transform.SetParent(transform);
            
            directionalLight = lightGO.AddComponent<Light>();
            directionalLight.type = LightType.Directional;
            
            Debug.Log("自动创建了 Directional Light");
        }
        
        /// <summary>
        /// 初始化光照设置
        /// </summary>
        private void InitializeLighting()
        {
            if (directionalLight == null) return;
            
            // 保存原始值
            originalIntensity = intensity;
            originalColor = lightColor;
            originalRotation = lightRotation;
            
            // 应用基础设置
            directionalLight.intensity = intensity;
            directionalLight.color = lightColor;
            directionalLight.transform.rotation = Quaternion.Euler(lightRotation);
            
            // 配置阴影
            directionalLight.shadows = shadowType;
            directionalLight.shadowStrength = shadowStrength;
            directionalLight.shadowBias = shadowBias;
            directionalLight.shadowNormalBias = shadowNormalBias;
            directionalLight.shadowNearPlane = shadowNearPlane;
            
            // 设置渲染模式
            directionalLight.renderMode = LightRenderMode.Auto;
            
            Debug.Log("MainMenu 光照初始化完成");
        }
        
        /// <summary>
        /// 应用平台优化
        /// </summary>
        private void ApplyPlatformOptimizations()
        {
            if (!adaptToMobile) return;
            
            LightingQuality targetQuality = Application.isMobilePlatform ? mobileQuality : desktopQuality;
            ApplyQualitySettings(targetQuality);
        }
        
        /// <summary>
        /// 应用质量设置
        /// </summary>
        private void ApplyQualitySettings(LightingQuality quality)
        {
            if (directionalLight == null) return;
            
            switch (quality)
            {
                case LightingQuality.Low:
                    directionalLight.shadows = LightShadows.None;
                    directionalLight.renderMode = LightRenderMode.ForceVertex;
                    enableDynamicLighting = false;
                    break;
                    
                case LightingQuality.Medium:
                    directionalLight.shadows = LightShadows.Hard;
                    directionalLight.renderMode = LightRenderMode.Auto;
                    enableIntensityPulse = false;
                    enableColorShift = false;
                    break;
                    
                case LightingQuality.High:
                    directionalLight.shadows = LightShadows.Soft;
                    directionalLight.renderMode = LightRenderMode.ForcePixel;
                    // 保持所有动态效果
                    break;
            }
            
            Debug.Log($"应用光照质量设置: {quality}");
        }
        
        /// <summary>
        /// 更新动态效果
        /// </summary>
        private void UpdateDynamicEffects()
        {
            float time = Time.time + timeOffset;
            
            // 强度脉冲效果
            if (enableIntensityPulse)
            {
                float pulseValue = Mathf.Sin(time * pulseSpeed) * pulseAmplitude;
                directionalLight.intensity = originalIntensity + pulseValue;
            }
            
            // 颜色变换效果
            if (enableColorShift && colorPalette.Length > 1)
            {
                UpdateColorShift(time);
            }
        }
        
        /// <summary>
        /// 更新颜色变换
        /// </summary>
        private void UpdateColorShift(float time)
        {
            colorTransitionTime += Time.deltaTime * colorShiftSpeed;
            
            if (colorTransitionTime >= 1f)
            {
                colorTransitionTime = 0f;
                currentColorIndex = (currentColorIndex + 1) % colorPalette.Length;
            }
            
            int nextColorIndex = (currentColorIndex + 1) % colorPalette.Length;
            Color currentColor = colorPalette[currentColorIndex];
            Color nextColor = colorPalette[nextColorIndex];
            
            directionalLight.color = Color.Lerp(currentColor, nextColor, colorTransitionTime);
        }
        
        /// <summary>
        /// 更新时间模拟
        /// </summary>
        private void UpdateTimeSimulation()
        {
            float timeProgress = (Time.time % dayDuration) / dayDuration;
            
            // 更新强度
            directionalLight.intensity = intensityCurve.Evaluate(timeProgress);
            
            // 更新颜色
            directionalLight.color = timeColorGradient.Evaluate(timeProgress);
            
            // 更新角度 (模拟太阳轨迹)
            float sunAngle = timeProgress * 180f - 90f; // -90° 到 90°
            Vector3 rotation = originalRotation;
            rotation.x = sunAngle;
            directionalLight.transform.rotation = Quaternion.Euler(rotation);
        }
        
        /// <summary>
        /// 初始化时间颜色渐变
        /// </summary>
        private void InitializeTimeColorGradient()
        {
            timeColorGradient = new Gradient();
            
            var colorKeys = new GradientColorKey[]
            {
                new GradientColorKey(new Color(0.2f, 0.3f, 0.6f), 0f),    // 夜晚 - 深蓝
                new GradientColorKey(new Color(1f, 0.6f, 0.3f), 0.25f),   // 日出 - 橙色
                new GradientColorKey(Color.white, 0.5f),                   // 正午 - 白色
                new GradientColorKey(new Color(1f, 0.8f, 0.5f), 0.75f),   // 日落 - 暖黄
                new GradientColorKey(new Color(0.2f, 0.3f, 0.6f), 1f)     // 夜晚 - 深蓝
            };
            
            var alphaKeys = new GradientAlphaKey[]
            {
                new GradientAlphaKey(1f, 0f),
                new GradientAlphaKey(1f, 1f)
            };
            
            timeColorGradient.SetKeys(colorKeys, alphaKeys);
        }
        
        /// <summary>
        /// 设置光照强度
        /// </summary>
        public void SetIntensity(float newIntensity)
        {
            intensity = newIntensity;
            originalIntensity = newIntensity;
            if (directionalLight != null)
            {
                directionalLight.intensity = newIntensity;
            }
        }
        
        /// <summary>
        /// 设置光照颜色
        /// </summary>
        public void SetColor(Color newColor)
        {
            lightColor = newColor;
            originalColor = newColor;
            if (directionalLight != null)
            {
                directionalLight.color = newColor;
            }
        }
        
        /// <summary>
        /// 设置光照旋转
        /// </summary>
        public void SetRotation(Vector3 newRotation)
        {
            lightRotation = newRotation;
            originalRotation = newRotation;
            if (directionalLight != null)
            {
                directionalLight.transform.rotation = Quaternion.Euler(newRotation);
            }
        }
        
        /// <summary>
        /// 启用/禁用动态效果
        /// </summary>
        public void SetDynamicLighting(bool enabled)
        {
            enableDynamicLighting = enabled;
            
            if (!enabled && directionalLight != null)
            {
                // 重置到原始值
                directionalLight.intensity = originalIntensity;
                directionalLight.color = originalColor;
                directionalLight.transform.rotation = Quaternion.Euler(originalRotation);
            }
        }
        
        /// <summary>
        /// 设置质量等级
        /// </summary>
        public void SetQuality(LightingQuality quality)
        {
            ApplyQualitySettings(quality);
        }
        
        /// <summary>
        /// 重置光照设置
        /// </summary>
        public void ResetLighting()
        {
            if (directionalLight == null) return;
            
            directionalLight.intensity = originalIntensity;
            directionalLight.color = originalColor;
            directionalLight.transform.rotation = Quaternion.Euler(originalRotation);
            
            timeOffset = 0f;
            currentColorIndex = 0;
            colorTransitionTime = 0f;
        }
        
        /// <summary>
        /// 获取当前光照组件
        /// </summary>
        public Light GetDirectionalLight()
        {
            return directionalLight;
        }
        
        private void OnValidate()
        {
            // 在编辑器中实时预览效果
            if (Application.isPlaying && directionalLight != null)
            {
                directionalLight.intensity = intensity;
                directionalLight.color = lightColor;
                directionalLight.transform.rotation = Quaternion.Euler(lightRotation);
                directionalLight.shadowStrength = shadowStrength;
            }
        }
    }
}
