using UnityEngine;

namespace GooseDuckKill.Lighting
{
    /// <summary>
    /// 简化版光照控制器
    /// 自动配置基础光照参数，适合快速设置
    /// </summary>
    public class SimpleLightController : MonoBehaviour
    {
        [Header("基础光照设置")]
        [SerializeField] private float intensity = 1.2f;
        [SerializeField] private Color lightColor = Color.white;
        [SerializeField] private Vector3 lightRotation = new Vector3(50f, -30f, 0f);
        
        [Header("阴影设置")]
        [SerializeField] private bool enableShadows = true;
        [SerializeField] private float shadowStrength = 0.8f;
        
        [Header("平台自适应")]
        [SerializeField] private bool autoOptimizeForMobile = true;
        
        private Light directionalLight;
        
        private void Awake()
        {
            SetupDirectionalLight();
        }
        
        private void Start()
        {
            ConfigureLighting();
            ApplyPlatformOptimizations();
        }
        
        /// <summary>
        /// 设置 Directional Light
        /// </summary>
        private void SetupDirectionalLight()
        {
            // 查找现有的 Directional Light
            directionalLight = FindObjectOfType<Light>();
            if (directionalLight != null && directionalLight.type != LightType.Directional)
            {
                directionalLight = null;
            }
            
            // 如果没找到，创建一个
            if (directionalLight == null)
            {
                var lightGO = new GameObject("Directional Light");
                lightGO.transform.SetParent(transform);
                directionalLight = lightGO.AddComponent<Light>();
                directionalLight.type = LightType.Directional;
                
                Debug.Log("自动创建了 Directional Light");
            }
        }
        
        /// <summary>
        /// 配置光照参数
        /// </summary>
        private void ConfigureLighting()
        {
            if (directionalLight == null) return;
            
            // 基础设置
            directionalLight.intensity = intensity;
            directionalLight.color = lightColor;
            directionalLight.transform.rotation = Quaternion.Euler(lightRotation);
            
            // 阴影设置
            if (enableShadows)
            {
                directionalLight.shadows = LightShadows.Soft;
                directionalLight.shadowStrength = shadowStrength;
                directionalLight.shadowBias = 0.05f;
                directionalLight.shadowNormalBias = 0.4f;
            }
            else
            {
                directionalLight.shadows = LightShadows.None;
            }
            
            // 渲染设置
            directionalLight.renderMode = LightRenderMode.Auto;
            
            Debug.Log("简单光照配置完成");
        }
        
        /// <summary>
        /// 应用平台优化
        /// </summary>
        private void ApplyPlatformOptimizations()
        {
            if (!autoOptimizeForMobile || directionalLight == null) return;
            
            #if UNITY_ANDROID || UNITY_IOS
            // 移动端优化
            directionalLight.shadows = LightShadows.None;
            directionalLight.renderMode = LightRenderMode.ForceVertex;
            directionalLight.intensity *= 0.8f; // 稍微降低强度以节省性能
            
            Debug.Log("应用移动端光照优化");
            #else
            // 桌面端保持高质量
            directionalLight.shadows = enableShadows ? LightShadows.Soft : LightShadows.None;
            directionalLight.renderMode = LightRenderMode.Auto;
            
            Debug.Log("应用桌面端光照设置");
            #endif
        }
        
        /// <summary>
        /// 设置光照强度
        /// </summary>
        public void SetIntensity(float newIntensity)
        {
            intensity = newIntensity;
            if (directionalLight != null)
            {
                directionalLight.intensity = newIntensity;
            }
        }
        
        /// <summary>
        /// 设置光照颜色
        /// </summary>
        public void SetColor(Color newColor)
        {
            lightColor = newColor;
            if (directionalLight != null)
            {
                directionalLight.color = newColor;
            }
        }
        
        /// <summary>
        /// 启用/禁用阴影
        /// </summary>
        public void SetShadowsEnabled(bool enabled)
        {
            enableShadows = enabled;
            if (directionalLight != null)
            {
                directionalLight.shadows = enabled ? LightShadows.Soft : LightShadows.None;
            }
        }
        
        /// <summary>
        /// 获取光照组件
        /// </summary>
        public Light GetLight()
        {
            return directionalLight;
        }
    }
}
