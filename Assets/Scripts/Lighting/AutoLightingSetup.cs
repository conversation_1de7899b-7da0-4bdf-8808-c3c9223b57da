using UnityEngine;
using UnityEngine.SceneManagement;

namespace GooseDuckKill.Lighting
{
    /// <summary>
    /// 自动光照设置脚本
    /// 在场景加载时自动配置合适的光照控制器
    /// </summary>
    public class AutoLightingSetup : MonoBehaviour
    {
        [Header("自动设置选项")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool createLightingManager = true;
        [SerializeField] private bool optimizeForPlatform = true;
        
        [Header("控制器选择")]
        [SerializeField] private LightControllerType controllerType = LightControllerType.Auto;
        
        public enum LightControllerType
        {
            Auto,           // 自动选择
            Simple,         // 简单控制器
            Advanced,       // 高级控制器
            None            // 不添加控制器
        }
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupLighting();
            }
        }
        
        /// <summary>
        /// 设置光照系统
        /// </summary>
        public void SetupLighting()
        {
            // 创建全局光照管理器
            if (createLightingManager)
            {
                CreateLightingManager();
            }
            
            // 为当前场景添加合适的光照控制器
            AddLightController();
            
            Debug.Log("自动光照设置完成");
        }
        
        /// <summary>
        /// 创建光照管理器
        /// </summary>
        private void CreateLightingManager()
        {
            // 检查是否已存在
            if (FindObjectOfType<LightingManager>() != null)
            {
                return;
            }
            
            var managerGO = new GameObject("LightingManager");
            var manager = managerGO.AddComponent<LightingManager>();
            DontDestroyOnLoad(managerGO);
            
            Debug.Log("创建了全局光照管理器");
        }
        
        /// <summary>
        /// 添加光照控制器
        /// </summary>
        private void AddLightController()
        {
            var directionalLight = FindDirectionalLight();
            if (directionalLight == null)
            {
                directionalLight = CreateDirectionalLight();
            }
            
            // 根据设置添加控制器
            LightControllerType targetType = controllerType;
            if (targetType == LightControllerType.Auto)
            {
                targetType = GetRecommendedControllerType();
            }
            
            AddControllerToLight(directionalLight, targetType);
        }
        
        /// <summary>
        /// 查找 Directional Light
        /// </summary>
        private Light FindDirectionalLight()
        {
            var lights = FindObjectsOfType<Light>();
            foreach (var light in lights)
            {
                if (light.type == LightType.Directional)
                {
                    return light;
                }
            }
            return null;
        }
        
        /// <summary>
        /// 创建 Directional Light
        /// </summary>
        private Light CreateDirectionalLight()
        {
            var lightGO = new GameObject("Directional Light");
            var light = lightGO.AddComponent<Light>();
            light.type = LightType.Directional;
            
            // 基础配置
            light.intensity = 1.2f;
            light.color = Color.white;
            light.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
            
            Debug.Log("创建了 Directional Light");
            return light;
        }
        
        /// <summary>
        /// 获取推荐的控制器类型
        /// </summary>
        private LightControllerType GetRecommendedControllerType()
        {
            string sceneName = SceneManager.GetActiveScene().name.ToLower();
            
            switch (sceneName)
            {
                case "mainmenu":
                case "main menu":
                    // 主菜单：移动端用简单，桌面端可用高级
                    return Application.isMobilePlatform ? 
                        LightControllerType.Simple : 
                        LightControllerType.Advanced;
                        
                case "lobby":
                    // 大厅：通常用简单控制器
                    return LightControllerType.Simple;
                    
                case "skeld":
                case "mira":
                case "polus":
                    // 游戏场景：通常不需要动态光照控制器
                    return LightControllerType.None;
                    
                default:
                    // 默认使用简单控制器
                    return LightControllerType.Simple;
            }
        }
        
        /// <summary>
        /// 为光照添加控制器
        /// </summary>
        private void AddControllerToLight(Light light, LightControllerType type)
        {
            if (light == null || type == LightControllerType.None)
            {
                return;
            }
            
            var lightGO = light.gameObject;
            
            // 移除现有的光照控制器
            RemoveExistingControllers(lightGO);
            
            // 添加新的控制器
            switch (type)
            {
                case LightControllerType.Simple:
                    var simpleController = lightGO.AddComponent<SimpleLightController>();
                    ConfigureSimpleController(simpleController);
                    Debug.Log("添加了 SimpleLightController");
                    break;
                    
                case LightControllerType.Advanced:
                    var advancedController = lightGO.AddComponent<MainMenuLightController>();
                    ConfigureAdvancedController(advancedController);
                    Debug.Log("添加了 MainMenuLightController");
                    break;
            }
        }
        
        /// <summary>
        /// 移除现有的控制器
        /// </summary>
        private void RemoveExistingControllers(GameObject lightGO)
        {
            var existingControllers = new System.Type[]
            {
                typeof(SimpleLightController),
                typeof(MainMenuLightController)
            };
            
            foreach (var controllerType in existingControllers)
            {
                var component = lightGO.GetComponent(controllerType);
                if (component != null)
                {
                    DestroyImmediate(component);
                }
            }
        }
        
        /// <summary>
        /// 配置简单控制器
        /// </summary>
        private void ConfigureSimpleController(SimpleLightController controller)
        {
            if (optimizeForPlatform)
            {
                // 根据平台优化设置
                #if UNITY_ANDROID || UNITY_IOS
                controller.SetShadowsEnabled(false);
                controller.SetIntensity(1f);
                #else
                controller.SetShadowsEnabled(true);
                controller.SetIntensity(1.2f);
                #endif
            }
        }
        
        /// <summary>
        /// 配置高级控制器
        /// </summary>
        private void ConfigureAdvancedController(MainMenuLightController controller)
        {
            if (optimizeForPlatform)
            {
                // 根据平台优化设置
                var quality = Application.isMobilePlatform ? 
                    MainMenuLightController.LightingQuality.Low : 
                    MainMenuLightController.LightingQuality.High;
                    
                controller.SetQuality(quality);
            }
        }
        
        /// <summary>
        /// 手动设置控制器类型
        /// </summary>
        public void SetControllerType(LightControllerType type)
        {
            controllerType = type;
            AddLightController();
        }
        
        /// <summary>
        /// 重新设置光照
        /// </summary>
        public void ResetLighting()
        {
            SetupLighting();
        }
        
        /// <summary>
        /// 获取当前光照控制器类型
        /// </summary>
        public LightControllerType GetCurrentControllerType()
        {
            var light = FindDirectionalLight();
            if (light == null) return LightControllerType.None;
            
            if (light.GetComponent<SimpleLightController>() != null)
                return LightControllerType.Simple;
            if (light.GetComponent<MainMenuLightController>() != null)
                return LightControllerType.Advanced;
                
            return LightControllerType.None;
        }
        
        /// <summary>
        /// 编辑器中的快速设置按钮
        /// </summary>
        [ContextMenu("Setup Lighting Now")]
        private void SetupLightingContextMenu()
        {
            SetupLighting();
        }
        
        [ContextMenu("Reset to Auto")]
        private void ResetToAutoContextMenu()
        {
            controllerType = LightControllerType.Auto;
            SetupLighting();
        }
        
        [ContextMenu("Force Simple Controller")]
        private void ForceSimpleControllerContextMenu()
        {
            controllerType = LightControllerType.Simple;
            SetupLighting();
        }
        
        [ContextMenu("Force Advanced Controller")]
        private void ForceAdvancedControllerContextMenu()
        {
            controllerType = LightControllerType.Advanced;
            SetupLighting();
        }
    }
}
