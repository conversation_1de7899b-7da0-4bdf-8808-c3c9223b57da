#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering.Universal;

namespace GooseDuckKill.Editor
{
    /// <summary>
    /// URP配置验证工具 - 验证URP配置是否正确设置
    /// </summary>
    public static class URPConfigurationValidator
    {
        /// <summary>
        /// 验证URP配置
        /// </summary>
        [MenuItem("Tools/Validate URP Configuration")]
        public static void ValidateURPConfiguration()
        {
            Debug.Log("[URPValidator] 开始验证URP配置...");

            bool isValid = true;

            // 1. 检查图形设置
            var graphicsSettings = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline;
            if (graphicsSettings == null)
            {
                Debug.LogError("[URPValidator] 图形设置中未设置渲染管线！");
                isValid = false;
            }
            else if (!(graphicsSettings is UniversalRenderPipelineAsset))
            {
                Debug.LogError("[URPValidator] 当前渲染管线不是URP！");
                isValid = false;
            }
            else
            {
                Debug.Log("[URPValidator] ✓ 图形设置中正确设置了URP");
            }

            // 2. 检查质量设置
            var qualitySettings = QualitySettings.renderPipeline;
            if (qualitySettings != null && qualitySettings is UniversalRenderPipelineAsset)
            {
                Debug.Log("[URPValidator] ✓ 质量设置中正确设置了URP");
            }
            else
            {
                Debug.LogWarning("[URPValidator] ⚠ 质量设置中的渲染管线可能未正确设置");
            }

            // 3. 检查URP资源
            var urpAsset = graphicsSettings as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                Debug.Log($"[URPValidator] ✓ URP资源名称: {urpAsset.name}");

                // 检查渲染器数据
                var rendererData = urpAsset.GetRenderer(0);
                if (rendererData != null)
                {
                    Debug.Log($"[URPValidator] ✓ 渲染器数据: {rendererData.GetType().Name}");
                }
                else
                {
                    Debug.LogError("[URPValidator] 渲染器数据为空！");
                    isValid = false;
                }
            }

            // 4. 检查场景中的摄像机
            var cameras = Object.FindObjectsOfType<Camera>();
            foreach (var camera in cameras)
            {
                var cameraData = camera.GetComponent<UniversalAdditionalCameraData>();
                if (cameraData == null)
                {
                    Debug.LogWarning($"[URPValidator] ⚠ 摄像机 '{camera.name}' 缺少UniversalAdditionalCameraData组件");
                }
                else
                {
                    Debug.Log($"[URPValidator] ✓ 摄像机 '{camera.name}' 配置正确");
                }
            }

            if (isValid)
            {
                Debug.Log("[URPValidator] ✅ URP配置验证通过！");
            }
            else
            {
                Debug.LogError("[URPValidator] ❌ URP配置验证失败，请检查上述错误！");
            }
        }

        /// <summary>
        /// 修复URP配置问题
        /// </summary>
        [MenuItem("Tools/Fix URP Configuration Issues")]
        public static void FixURPConfigurationIssues()
        {
            Debug.Log("[URPValidator] 开始修复URP配置问题...");

            // 1. 为场景中的摄像机添加UniversalAdditionalCameraData组件
            var cameras = Object.FindObjectsOfType<Camera>();
            int fixedCameras = 0;

            foreach (var camera in cameras)
            {
                var cameraData = camera.GetComponent<UniversalAdditionalCameraData>();
                if (cameraData == null)
                {
                    camera.gameObject.AddComponent<UniversalAdditionalCameraData>();
                    fixedCameras++;
                    Debug.Log($"[URPValidator] 为摄像机 '{camera.name}' 添加了UniversalAdditionalCameraData组件");
                }
            }

            if (fixedCameras > 0)
            {
                Debug.Log($"[URPValidator] ✅ 修复了 {fixedCameras} 个摄像机的配置");
                EditorUtility.SetDirty(UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects()[0]);
            }
            else
            {
                Debug.Log("[URPValidator] 没有发现需要修复的摄像机配置");
            }
        }
    }
}
#endif
