using System;
using UnityEngine;
using CustomNetworking.Core;

// 使用别名避免命名空间冲突
using UDebug = UnityEngine.Debug;

namespace GooseDuckKill.Core
{
    /// <summary>
    /// 分数管理器 - 管理游戏分数和胜利条件
    /// </summary>
    public class ScoreManager : NetworkBehaviour
    {
        /// <summary>
        /// 团队枚举
        /// </summary>
        public enum Team
        {
            Goose,      // 鹅队伍
            Duck,       // 鸭子队伍
            Neutral     // 中立
        }

        /// <summary>
        /// 胜利条件枚举
        /// </summary>
        public enum VictoryCondition
        {
            GooseTaskComplete,    // 鹅完成所有任务
            DuckKillMajority,     // 鸭子杀死大多数玩家
            DuckEliminated,       // 所有鸭子被淘汰
            TimerExpired,         // 时间耗尽
            SpecialRoleWin        // 特殊角色胜利条件
        }

        // 网络同步属性
        [Networked] private int GooseScore { get; set; }
        [Networked] private int DuckScore { get; set; }
        [Networked] private int NeutralScore { get; set; }
        [Networked] private NetworkBool GameEnded { get; set; }
        [Networked] private Team WinningTeam { get; set; }
        [Networked] private VictoryCondition WinCondition { get; set; }

        // 胜利事件
        public event Action<Team, VictoryCondition> OnTeamVictory;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Spawned()
        {
            base.Spawned();

            if (Runner.IsServer)
            {
                ResetScores();
            }

            UDebug.Log("ScoreManager 初始化完成");
        }

        /// <summary>
        /// 重置所有分数
        /// </summary>
        public void ResetScores()
        {
            if (!Runner.IsServer) return;

            GooseScore = 0;
            DuckScore = 0;
            NeutralScore = 0;
            GameEnded = false;

            UDebug.Log("分数已重置");
        }

        #region 分数管理

        /// <summary>
        /// 添加分数
        /// </summary>
        public void AddScore(Team team, int points)
        {
            if (!Runner.IsServer) return;

            switch (team)
            {
                case Team.Goose:
                    GooseScore += points;
                    break;
                case Team.Duck:
                    DuckScore += points;
                    break;
                case Team.Neutral:
                    NeutralScore += points;
                    break;
            }

            UDebug.Log($"团队 {team} 获得 {points} 分，当前分数：{GetScore(team)}");
        }

        /// <summary>
        /// 获取团队分数
        /// </summary>
        public int GetScore(Team team)
        {
            switch (team)
            {
                case Team.Goose:
                    return GooseScore;
                case Team.Duck:
                    return DuckScore;
                case Team.Neutral:
                    return NeutralScore;
                default:
                    return 0;
            }
        }

        #endregion

        #region 胜利条件

        /// <summary>
        /// 检查胜利条件
        /// </summary>
        public void CheckVictoryConditions()
        {
            if (!Runner.IsServer || GameEnded) return;

            // 获取游戏管理器和角色管理器
            var roleManager = GameManager.Instance?.Roles;
            if (roleManager == null) return;

            // 检查鸭子胜利 - 杀死足够多的鹅
            int aliveGooseCount = roleManager.GetAlivePlayerCountByRole(RoleManager.RoleType.Goose);
            int aliveDuckCount = roleManager.GetAlivePlayerCountByRole(RoleManager.RoleType.Duck);

            if (aliveDuckCount >= aliveGooseCount && aliveDuckCount > 0)
            {
                DeclareVictory(Team.Duck, VictoryCondition.DuckKillMajority);
                return;
            }

            // 检查鹅胜利 - 任务完成
            if (AreAllTasksCompleted())
            {
                DeclareVictory(Team.Goose, VictoryCondition.GooseTaskComplete);
                return;
            }

            // 检查鹅胜利 - 所有鸭子被淘汰
            if (aliveDuckCount == 0 && roleManager.GetTotalPlayerCountByRole(RoleManager.RoleType.Duck) > 0)
            {
                DeclareVictory(Team.Goose, VictoryCondition.DuckEliminated);
                return;
            }

            // 检查中立角色胜利
            CheckNeutralVictoryConditions();
        }

        /// <summary>
        /// 检查所有任务是否完成
        /// </summary>
        public bool AreAllTasksCompleted()
        {
            // 任务系统实现后更新此方法
            // 临时返回false
            return false;
        }

        /// <summary>
        /// 检查中立角色胜利条件
        /// </summary>
        private void CheckNeutralVictoryConditions()
        {
            // 中立角色胜利条件实现后更新此方法
        }

        /// <summary>
        /// 宣布胜利
        /// </summary>
        public void DeclareVictory(Team team, VictoryCondition condition)
        {
            if (!Runner.IsServer || GameEnded) return;

            // 设置胜利状态
            GameEnded = true;
            WinningTeam = team;
            WinCondition = condition;

            UDebug.Log($"游戏结束！胜利团队: {team}，胜利条件: {condition}");

            // 触发事件
            OnTeamVictory?.Invoke(team, condition);

            // 通知游戏管理器结束游戏
            GameManager.Instance?.EndGame();
        }

        /// <summary>
        /// 检查游戏是否已结束
        /// </summary>
        public bool IsGameOver()
        {
            return GameEnded;
        }

        /// <summary>
        /// 获取胜利团队
        /// </summary>
        public Team GetWinningTeam()
        {
            return WinningTeam;
        }

        /// <summary>
        /// 获取胜利条件
        /// </summary>
        public VictoryCondition GetVictoryCondition()
        {
            return WinCondition;
        }

        #endregion

        #region 网络通信

        /// <summary>
        /// 通知客户端游戏结束 - 暂时通过事件系统实现
        /// TODO: 实现自定义RPC系统后替换
        /// </summary>
        private void NotifyGameOver(Team winningTeam, VictoryCondition condition)
        {
            UDebug.Log($"[网络] 游戏结束！胜利团队: {winningTeam}，胜利条件: {condition}");

            // 触发事件供UI等系统使用
            OnTeamVictory?.Invoke(winningTeam, condition);
        }

        #endregion
    }
}
