using UnityEngine;

namespace GooseDuckKill.Core.Debug
{
    /// <summary>
    /// DebugConsole 简单测试
    /// 验证DebugConsole功能是否正常工作
    /// </summary>
    public class DebugConsoleTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runTestOnStart = false;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                TestDebugConsole();
            }
        }
        
        /// <summary>
        /// 测试DebugConsole基本功能
        /// </summary>
        [ContextMenu("Test DebugConsole")]
        public void TestDebugConsole()
        {
            UnityEngine.Debug.Log("=== DebugConsole Test Started ===");
            
            try
            {
                // 测试实例化
                var debugConsole = DebugConsole.Instance;
                UnityEngine.Debug.Log("✓ DebugConsole.Instance - OK");
                
                // 测试日志功能
                debugConsole.LogDebug("Test Info Message", DebugLogType.Info);
                debugConsole.LogDebug("Test Warning Message", DebugLogType.Warning);
                debugConsole.LogDebug("Test Error Message", DebugLogType.Error);
                UnityEngine.Debug.Log("✓ LogDebug methods - OK");
                
                // 测试命令注册
                debugConsole.RegisterCommand("test", "Test command", TestCommand);
                UnityEngine.Debug.Log("✓ RegisterCommand - OK");
                
                // 测试命令执行
                debugConsole.ExecuteCommand("help");
                debugConsole.ExecuteCommand("test");
                UnityEngine.Debug.Log("✓ ExecuteCommand - OK");
                
                // 测试UI切换
                debugConsole.ToggleMobileUI();
                debugConsole.ToggleDesktopUI();
                UnityEngine.Debug.Log("✓ UI Toggle methods - OK");
                
                UnityEngine.Debug.Log("=== DebugConsole Test Completed Successfully ===");
                
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"❌ DebugConsole Test Failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试事件系统
        /// </summary>
        [ContextMenu("Test Event System")]
        public void TestEventSystem()
        {
            UnityEngine.Debug.Log("=== Event System Test Started ===");
            
            try
            {
                bool logEventReceived = false;
                bool uiEventReceived = false;
                bool commandEventReceived = false;
                
                // 订阅事件
                DebugConsole.OnLogEntryAdded += (entry) => {
                    logEventReceived = true;
                    UnityEngine.Debug.Log($"✓ LogEntryAdded event: {entry.Message}");
                };
                
                DebugConsole.OnMobileUIToggled += (visible) => {
                    uiEventReceived = true;
                    UnityEngine.Debug.Log($"✓ MobileUIToggled event: {visible}");
                };
                
                DebugConsole.OnCommandExecuted += (command) => {
                    commandEventReceived = true;
                    UnityEngine.Debug.Log($"✓ CommandExecuted event: {command}");
                };
                
                // 触发事件
                var debugConsole = DebugConsole.Instance;
                debugConsole.LogDebug("Event test message", DebugLogType.Info);
                debugConsole.ToggleMobileUI();
                debugConsole.ExecuteCommand("help");
                
                // 验证事件是否触发
                if (logEventReceived && uiEventReceived && commandEventReceived)
                {
                    UnityEngine.Debug.Log("=== Event System Test Completed Successfully ===");
                }
                else
                {
                    UnityEngine.Debug.LogWarning("⚠️ Some events may not have been triggered properly");
                }
                
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"❌ Event System Test Failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试命令处理器
        /// </summary>
        private void TestCommand(string[] args)
        {
            UnityEngine.Debug.Log("✓ Test command executed successfully!");
            if (args.Length > 0)
            {
                UnityEngine.Debug.Log($"  Arguments: {string.Join(", ", args)}");
            }
        }
        
        /// <summary>
        /// 演示如何注册自定义命令
        /// </summary>
        [ContextMenu("Register Custom Commands")]
        public void RegisterCustomCommands()
        {
            var debugConsole = DebugConsole.Instance;
            
            // 注册一些示例命令
            debugConsole.RegisterCommand("hello", "Say hello", (args) => {
                string name = args.Length > 0 ? args[0] : "World";
                UnityEngine.Debug.Log($"Hello, {name}!");
            });
            
            debugConsole.RegisterCommand("time", "Show current time", (args) => {
                UnityEngine.Debug.Log($"Current time: {System.DateTime.Now:HH:mm:ss}");
            });
            
            debugConsole.RegisterCommand("scene", "Show current scene", (args) => {
                UnityEngine.Debug.Log($"Current scene: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}");
            });
            
            UnityEngine.Debug.Log("✓ Custom commands registered. Try: hello, time, scene");
        }
        
        /// <summary>
        /// 演示如何使用DebugConsole记录不同类型的日志
        /// </summary>
        [ContextMenu("Demo Logging")]
        public void DemoLogging()
        {
            var debugConsole = DebugConsole.Instance;
            
            debugConsole.LogDebug("这是一条信息日志", DebugLogType.Info);
            debugConsole.LogDebug("这是一条警告日志", DebugLogType.Warning);
            debugConsole.LogDebug("这是一条错误日志", DebugLogType.Error);
            
            // 也可以记录格式化的日志
            debugConsole.LogDebug($"当前时间: {System.DateTime.Now}", DebugLogType.Info);
            debugConsole.LogDebug($"当前场景: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}", DebugLogType.Info);
            debugConsole.LogDebug($"FPS: {1f / Time.deltaTime:F1}", DebugLogType.Info);
            
            UnityEngine.Debug.Log("✓ Logging demo completed - check DebugConsole for messages");
        }
        
        /// <summary>
        /// 快速测试所有功能
        /// </summary>
        [ContextMenu("Quick Test All")]
        public void QuickTestAll()
        {
            UnityEngine.Debug.Log("=== Quick Test All Features ===");
            
            TestDebugConsole();
            RegisterCustomCommands();
            DemoLogging();
            TestEventSystem();
            
            UnityEngine.Debug.Log("=== All Tests Completed ===");
        }
        
        #if UNITY_EDITOR
        /// <summary>
        /// 编辑器中的验证
        /// </summary>
        private void OnValidate()
        {
            // 确保DebugConsole在编辑器中可用
            if (Application.isPlaying && DebugConsole.Instance != null)
            {
                // 可以在这里添加编辑器特定的验证逻辑
            }
        }
        #endif
    }
}
