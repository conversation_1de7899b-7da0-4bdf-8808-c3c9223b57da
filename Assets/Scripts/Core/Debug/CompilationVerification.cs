using UnityEngine;

namespace GooseDuckKill.Core.Debug
{
    /// <summary>
    /// 编译验证脚本
    /// 验证所有Debug相关的类都能正确编译
    /// </summary>
    public class CompilationVerification : MonoBehaviour
    {
        [Header("编译验证")]
        [SerializeField] private bool runVerificationOnStart = false;
        
        private void Start()
        {
            if (runVerificationOnStart)
            {
                VerifyCompilation();
            }
        }
        
        /// <summary>
        /// 验证编译
        /// </summary>
        [ContextMenu("Verify Compilation")]
        public void VerifyCompilation()
        {
            UnityEngine.Debug.Log("=== Compilation Verification Started ===");
            
            try
            {
                // 验证DebugConsole类型
                var debugConsoleType = typeof(DebugConsole);
                UnityEngine.Debug.Log($"✓ DebugConsole type: {debugConsoleType.FullName}");
                
                // 验证DebugLogEntry类型
                var logEntryType = typeof(DebugLogEntry);
                UnityEngine.Debug.Log($"✓ DebugLogEntry type: {logEntryType.FullName}");
                
                // 验证DebugCommand类型
                var commandType = typeof(DebugCommand);
                UnityEngine.Debug.Log($"✓ DebugCommand type: {commandType.FullName}");
                
                // 验证DebugLogType枚举
                var logTypeEnum = typeof(DebugLogType);
                UnityEngine.Debug.Log($"✓ DebugLogType enum: {logTypeEnum.FullName}");
                
                // 验证DebugConsole实例化
                var debugConsole = DebugConsole.Instance;
                UnityEngine.Debug.Log($"✓ DebugConsole instance created: {debugConsole != null}");
                
                // 验证事件系统
                bool hasEvents = true;
                try
                {
                    // 测试事件订阅（不实际订阅，只是验证事件存在）
                    var logEventType = typeof(DebugConsole).GetEvent("OnLogEntryAdded");
                    var uiEventType = typeof(DebugConsole).GetEvent("OnMobileUIToggled");
                    var commandEventType = typeof(DebugConsole).GetEvent("OnCommandExecuted");
                    
                    hasEvents = logEventType != null && uiEventType != null && commandEventType != null;
                }
                catch
                {
                    hasEvents = false;
                }
                
                UnityEngine.Debug.Log($"✓ Event system available: {hasEvents}");
                
                // 验证程序集信息
                var assembly = debugConsoleType.Assembly;
                UnityEngine.Debug.Log($"✓ Assembly: {assembly.GetName().Name}");
                
                UnityEngine.Debug.Log("=== Compilation Verification Completed Successfully ===");
                UnityEngine.Debug.Log("🎉 All Debug classes compiled without errors!");
                
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"❌ Compilation Verification Failed: {ex.Message}");
                UnityEngine.Debug.LogError($"Stack Trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 验证命名空间解析
        /// </summary>
        [ContextMenu("Verify Namespace Resolution")]
        public void VerifyNamespaceResolution()
        {
            UnityEngine.Debug.Log("=== Namespace Resolution Verification ===");
            
            // 验证Unity Debug类
            UnityEngine.Debug.Log("✓ UnityEngine.Debug.Log working");
            UnityEngine.Debug.LogWarning("✓ UnityEngine.Debug.LogWarning working");
            UnityEngine.Debug.LogError("✓ UnityEngine.Debug.LogError working");
            
            // 验证DebugConsole类
            var debugConsole = DebugConsole.Instance;
            debugConsole.LogDebug("✓ DebugConsole.LogDebug working", DebugLogType.Info);
            
            UnityEngine.Debug.Log("=== Namespace Resolution Verification Completed ===");
        }
        
        /// <summary>
        /// 验证循环依赖是否已解决
        /// </summary>
        [ContextMenu("Verify No Circular Dependencies")]
        public void VerifyNoCircularDependencies()
        {
            UnityEngine.Debug.Log("=== Circular Dependencies Verification ===");
            
            try
            {
                // 获取当前程序集
                var currentAssembly = typeof(DebugConsole).Assembly;
                var referencedAssemblies = currentAssembly.GetReferencedAssemblies();
                
                UnityEngine.Debug.Log($"Current Assembly: {currentAssembly.GetName().Name}");
                UnityEngine.Debug.Log($"Referenced Assemblies: {referencedAssemblies.Length}");
                
                foreach (var refAssembly in referencedAssemblies)
                {
                    if (refAssembly.Name.StartsWith("GooseDuckKill") || refAssembly.Name == "CustomNetworking")
                    {
                        UnityEngine.Debug.Log($"  → {refAssembly.Name}");
                    }
                }
                
                // 验证UI程序集不在引用列表中
                bool hasUIReference = false;
                foreach (var refAssembly in referencedAssemblies)
                {
                    if (refAssembly.Name == "GooseDuckKill.UI")
                    {
                        hasUIReference = true;
                        break;
                    }
                }
                
                if (hasUIReference)
                {
                    UnityEngine.Debug.LogError("❌ Core assembly still references UI assembly - circular dependency not resolved!");
                }
                else
                {
                    UnityEngine.Debug.Log("✓ Core assembly does not reference UI assembly - circular dependency resolved!");
                }
                
                UnityEngine.Debug.Log("=== Circular Dependencies Verification Completed ===");
                
            }
            catch (System.Exception ex)
            {
                UnityEngine.Debug.LogError($"❌ Circular Dependencies Verification Failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行所有验证
        /// </summary>
        [ContextMenu("Run All Verifications")]
        public void RunAllVerifications()
        {
            UnityEngine.Debug.Log("=== Running All Verifications ===");
            
            VerifyCompilation();
            VerifyNamespaceResolution();
            VerifyNoCircularDependencies();
            
            UnityEngine.Debug.Log("=== All Verifications Completed ===");
        }
    }
}
