using System;
using UnityEngine;
using CustomNetworking.Core;

// 使用别名避免命名空间冲突
using UDebug = UnityEngine.Debug;

namespace GooseDuckKill.Core
{
    /// <summary>
    /// 游戏状态管理器 - 负责管理游戏状态机和状态转换
    /// </summary>
    public class GameStateManager : NetworkBehaviour
    {
        // 游戏状态枚举
        public enum GameState
        {
            Lobby,      // 大厅状态
            Starting,   // 游戏开始中
            Playing,    // 正常游戏中
            Meeting,    // 会议状态
            Ending      // 游戏结束
        }

        // 网络同步状态 - 移除OnChanged特性
        [Networked]
        private GameState NetworkedState { get; set; }

        // 状态转换事件
        public event Action<GameState> OnStateChanged;

        // 当前状态访问器
        public GameState CurrentState => NetworkedState;

        // 上一个状态（用于状态恢复）
        private GameState _previousState;

        // 变更检测器
        private ChangeDetector _stateChangeDetector;

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Spawned()
        {
            base.Spawned();

            // 初始化变更检测器
            _stateChangeDetector = new ChangeDetector();

            // 如果是服务器，设置初始状态
            if (Runner.IsServer)
            {
                NetworkedState = GameState.Lobby;
            }

            UDebug.Log($"GameStateManager 初始化完成，当前状态: {NetworkedState}");
        }

        /// <summary>
        /// 渲染更新 - 检测状态变化
        /// </summary>
        public override void Render()
        {
            base.Render();

            // 简化的变化检测 - 直接检测NetworkedState属性变化
            if (_stateChangeDetector.DetectChange(nameof(NetworkedState), NetworkedState))
            {
                // 状态发生变化，触发事件
                OnStateChanged?.Invoke(NetworkedState);
            }
        }


        /// <summary>
        /// 切换游戏状态
        /// </summary>
        /// <param name="newState">新状态</param>
        public void ChangeState(GameState newState)
        {
            if (!Runner.IsServer)
            {
                UDebug.LogWarning("只有服务器可以改变游戏状态");
                return;
            }

            if (NetworkedState == newState)
            {
                UDebug.LogWarning($"已经处于 {newState} 状态，无需切换");
                return;
            }

            UDebug.Log($"游戏状态从 {NetworkedState} 切换到 {newState}");

            // 记录上一个状态
            _previousState = NetworkedState;

            // 设置新状态
            NetworkedState = newState;

            // 处理状态特定逻辑
            switch (newState)
            {
                case GameState.Lobby:
                    HandleLobbyState();
                    break;
                case GameState.Starting:
                    HandleStartingState();
                    break;
                case GameState.Playing:
                    HandlePlayingState();
                    break;
                case GameState.Meeting:
                    HandleMeetingState();
                    break;
                case GameState.Ending:
                    HandleEndingState();
                    break;
            }
        }

        /// <summary>
        /// 恢复到上一个状态
        /// </summary>
        public void RestorePreviousState()
        {
            if (!Runner.IsServer) return;

            ChangeState(_previousState);
        }

        #region 状态处理方法

        /// <summary>
        /// 处理大厅状态
        /// </summary>
        private void HandleLobbyState()
        {
            // 重置游戏数据
            // 允许玩家加入/离开

            UDebug.Log("进入大厅状态：等待玩家加入...");
        }

        /// <summary>
        /// 处理游戏开始状态
        /// </summary>
        private void HandleStartingState()
        {
            // 锁定房间
            // 分配角色
            // 显示角色揭示动画
            // 启动倒计时

            UDebug.Log("进入开始状态：游戏即将开始，正在分配角色...");
        }

        /// <summary>
        /// 处理游戏进行状态
        /// </summary>
        private void HandlePlayingState()
        {
            // 启用玩家控制
            // 开始任务和杀人计时器
            // 监控胜利条件

            UDebug.Log("进入游戏状态：游戏正在进行中...");
        }

        /// <summary>
        /// 处理会议状态
        /// </summary>
        private void HandleMeetingState()
        {
            // 禁用玩家控制
            // 显示会议UI
            // 启动讨论和投票计时器

            UDebug.Log("进入会议状态：会议已召开，开始讨论...");
        }

        /// <summary>
        /// 处理游戏结束状态
        /// </summary>
        private void HandleEndingState()
        {
            // 显示胜利团队和原因
            // 显示所有玩家角色
            // 显示数据统计
            // 准备重新开始选项

            UDebug.Log("进入结束状态：游戏已结束，显示结果...");
        }

        #endregion
    }
}
