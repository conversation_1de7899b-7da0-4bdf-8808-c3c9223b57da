using UnityEngine;
using UnityEngine.Audio;
using System.Collections.Generic;

namespace GooseDuckKill.Audio
{
    /// <summary>
    /// 音频管理器
    /// 管理游戏中的所有音频播放，包括背景音乐、音效和语音
    /// </summary>
    public class AudioManager : MonoBehaviour
    {
        [Header("音频混合器")]
        [SerializeField] private AudioMixer audioMixer;

        [Header("音频源")]
        [SerializeField] private AudioSource backgroundMusicSource;
        [SerializeField] private AudioSource uiSoundEffectsSource;
        [SerializeField] private AudioSource gameSoundEffectsSource;
        [SerializeField] private AudioSource voiceSource;

        [Header("背景音乐")]
        [SerializeField] private AudioClip mainMenuMusic;
        [SerializeField] private AudioClip lobbyMusic;
        [SerializeField] private AudioClip gameplayMusic;
        [SerializeField] private AudioClip victoryMusic;
        [SerializeField] private AudioClip defeatMusic;

        [Header("UI音效")]
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip buttonHoverSound;
        [SerializeField] private AudioClip notificationSound;
        [SerializeField] private AudioClip errorSound;
        [SerializeField] private AudioClip successSound;

        [Header("游戏音效")]
        [SerializeField] private AudioClip footstepSound;
        [SerializeField] private AudioClip doorOpenSound;
        [SerializeField] private AudioClip doorCloseSound;
        [SerializeField] private AudioClip taskCompleteSound;
        [SerializeField] private AudioClip emergencyMeetingSound;
        [SerializeField] private AudioClip killSound;
        [SerializeField] private AudioClip reportBodySound;
        [SerializeField] private AudioClip ventSound;

        [Header("音量设置")]
        [SerializeField] private float masterVolume = 1f;
        [SerializeField] private float musicVolume = 0.7f;
        [SerializeField] private float sfxVolume = 0.8f;
        [SerializeField] private float voiceVolume = 1f;

        [Header("淡入淡出设置")]
        [SerializeField] private float musicFadeTime = 1f;
        [SerializeField] private bool enableCrossfade = true;

        // 单例实例
        public static AudioManager Instance { get; private set; }

        // 当前播放状态
        private AudioClip currentBackgroundMusic;
        private bool isMusicPlaying = false;
        private bool isMuted = false;

        // 音效池
        private Queue<AudioSource> sfxPool = new Queue<AudioSource>();
        private List<AudioSource> activeSfxSources = new List<AudioSource>();

        // 协程引用
        private Coroutine musicFadeCoroutine;

        // 音频混合器参数名
        private const string MASTER_VOLUME_PARAM = "MasterVolume";
        private const string MUSIC_VOLUME_PARAM = "MusicVolume";
        private const string SFX_VOLUME_PARAM = "SFXVolume";
        private const string VOICE_VOLUME_PARAM = "VoiceVolume";

        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudioManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            // 应用初始音量设置
            ApplyVolumeSettings();

            // 播放主菜单音乐
            PlayBackgroundMusic(mainMenuMusic);
        }

        /// <summary>
        /// 初始化音频管理器
        /// </summary>
        private void InitializeAudioManager()
        {
            // 自动查找音频源组件
            FindAudioSources();

            // 初始化音效池
            InitializeSFXPool();

            // 设置音频源属性
            SetupAudioSources();
        }

        /// <summary>
        /// 查找音频源组件
        /// </summary>
        private void FindAudioSources()
        {
            if (backgroundMusicSource == null)
            {
                var musicGO = transform.Find("Background Music");
                if (musicGO != null)
                {
                    backgroundMusicSource = musicGO.GetComponent<AudioSource>();
                }
            }

            if (uiSoundEffectsSource == null)
            {
                var uiSfxGO = transform.Find("UI Sound Effects");
                if (uiSfxGO != null)
                {
                    uiSoundEffectsSource = uiSfxGO.GetComponent<AudioSource>();
                }
            }

            // 如果没有找到，创建默认的音频源
            if (backgroundMusicSource == null)
            {
                backgroundMusicSource = gameObject.AddComponent<AudioSource>();
                backgroundMusicSource.loop = true;
                backgroundMusicSource.playOnAwake = false;
            }

            if (uiSoundEffectsSource == null)
            {
                uiSoundEffectsSource = gameObject.AddComponent<AudioSource>();
                uiSoundEffectsSource.loop = false;
                uiSoundEffectsSource.playOnAwake = false;
            }
        }

        /// <summary>
        /// 初始化音效池
        /// </summary>
        private void InitializeSFXPool()
        {
            // 创建音效池，避免频繁创建销毁AudioSource
            for (int i = 0; i < 10; i++)
            {
                var sfxGO = new GameObject($"SFX Pool {i}");
                sfxGO.transform.SetParent(transform);
                var audioSource = sfxGO.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.loop = false;
                sfxPool.Enqueue(audioSource);
            }
        }

        /// <summary>
        /// 设置音频源属性
        /// </summary>
        private void SetupAudioSources()
        {
            // 设置音频混合器组
            if (audioMixer != null)
            {
                var musicGroup = audioMixer.FindMatchingGroups("Music")[0];
                var sfxGroup = audioMixer.FindMatchingGroups("SFX")[0];

                if (backgroundMusicSource != null)
                    backgroundMusicSource.outputAudioMixerGroup = musicGroup;

                if (uiSoundEffectsSource != null)
                    uiSoundEffectsSource.outputAudioMixerGroup = sfxGroup;

                if (gameSoundEffectsSource != null)
                    gameSoundEffectsSource.outputAudioMixerGroup = sfxGroup;
            }
        }

        /// <summary>
        /// 播放背景音乐
        /// </summary>
        public void PlayBackgroundMusic(AudioClip musicClip, bool fadeIn = true)
        {
            if (musicClip == null || backgroundMusicSource == null) return;

            // 如果正在播放相同的音乐，不做任何操作
            if (currentBackgroundMusic == musicClip && isMusicPlaying) return;

            if (enableCrossfade && isMusicPlaying)
            {
                // 交叉淡入淡出
                StartCoroutine(CrossfadeMusic(musicClip));
            }
            else
            {
                // 直接播放或淡入
                if (fadeIn && isMusicPlaying)
                {
                    StartCoroutine(FadeOutAndPlayMusic(musicClip));
                }
                else
                {
                    PlayMusicDirectly(musicClip);
                }
            }
        }

        /// <summary>
        /// 直接播放音乐
        /// </summary>
        private void PlayMusicDirectly(AudioClip musicClip)
        {
            backgroundMusicSource.clip = musicClip;
            backgroundMusicSource.volume = musicVolume;
            backgroundMusicSource.Play();

            currentBackgroundMusic = musicClip;
            isMusicPlaying = true;
        }

        /// <summary>
        /// 停止背景音乐
        /// </summary>
        public void StopBackgroundMusic(bool fadeOut = true)
        {
            if (!isMusicPlaying || backgroundMusicSource == null) return;

            if (fadeOut)
            {
                StartCoroutine(FadeOutMusic());
            }
            else
            {
                backgroundMusicSource.Stop();
                isMusicPlaying = false;
                currentBackgroundMusic = null;
            }
        }

        /// <summary>
        /// 播放UI音效
        /// </summary>
        public void PlayUISound(AudioClip soundClip, float volume = 1f)
        {
            if (soundClip == null || uiSoundEffectsSource == null) return;

            uiSoundEffectsSource.PlayOneShot(soundClip, volume * sfxVolume);
        }

        /// <summary>
        /// 播放游戏音效
        /// </summary>
        public void PlayGameSound(AudioClip soundClip, float volume = 1f, Vector3 position = default)
        {
            if (soundClip == null) return;

            // 从音效池获取AudioSource
            AudioSource sfxSource = GetPooledAudioSource();
            if (sfxSource != null)
            {
                sfxSource.clip = soundClip;
                sfxSource.volume = volume * sfxVolume;
                sfxSource.transform.position = position;
                sfxSource.Play();

                // 播放完成后回收到池中
                StartCoroutine(ReturnToPoolAfterPlay(sfxSource, soundClip.length));
            }
        }

        /// <summary>
        /// 从池中获取AudioSource
        /// </summary>
        private AudioSource GetPooledAudioSource()
        {
            if (sfxPool.Count > 0)
            {
                var source = sfxPool.Dequeue();
                activeSfxSources.Add(source);
                return source;
            }

            // 如果池为空，创建新的AudioSource
            var sfxGO = new GameObject("Temp SFX");
            sfxGO.transform.SetParent(transform);
            var audioSource = sfxGO.AddComponent<AudioSource>();
            audioSource.playOnAwake = false;
            audioSource.loop = false;

            if (audioMixer != null)
            {
                var sfxGroup = audioMixer.FindMatchingGroups("SFX")[0];
                audioSource.outputAudioMixerGroup = sfxGroup;
            }

            activeSfxSources.Add(audioSource);
            return audioSource;
        }

        /// <summary>
        /// 播放完成后回收AudioSource
        /// </summary>
        private System.Collections.IEnumerator ReturnToPoolAfterPlay(AudioSource source, float delay)
        {
            yield return new WaitForSeconds(delay);

            if (source != null)
            {
                activeSfxSources.Remove(source);
                sfxPool.Enqueue(source);
            }
        }

        /// <summary>
        /// 音乐淡出协程
        /// </summary>
        private System.Collections.IEnumerator FadeOutMusic()
        {
            float startVolume = backgroundMusicSource.volume;
            float elapsed = 0f;

            while (elapsed < musicFadeTime)
            {
                elapsed += Time.deltaTime;
                backgroundMusicSource.volume = Mathf.Lerp(startVolume, 0f, elapsed / musicFadeTime);
                yield return null;
            }

            backgroundMusicSource.Stop();
            backgroundMusicSource.volume = musicVolume;
            isMusicPlaying = false;
            currentBackgroundMusic = null;
        }

        /// <summary>
        /// 淡出并播放新音乐
        /// </summary>
        private System.Collections.IEnumerator FadeOutAndPlayMusic(AudioClip newMusic)
        {
            yield return StartCoroutine(FadeOutMusic());
            PlayMusicDirectly(newMusic);
            yield return StartCoroutine(FadeInMusic());
        }

        /// <summary>
        /// 音乐淡入协程
        /// </summary>
        private System.Collections.IEnumerator FadeInMusic()
        {
            backgroundMusicSource.volume = 0f;
            float elapsed = 0f;

            while (elapsed < musicFadeTime)
            {
                elapsed += Time.deltaTime;
                backgroundMusicSource.volume = Mathf.Lerp(0f, musicVolume, elapsed / musicFadeTime);
                yield return null;
            }
        }

        /// <summary>
        /// 交叉淡入淡出
        /// </summary>
        private System.Collections.IEnumerator CrossfadeMusic(AudioClip newMusic)
        {
            // 创建临时AudioSource播放新音乐
            var tempSource = gameObject.AddComponent<AudioSource>();
            tempSource.clip = newMusic;
            tempSource.loop = true;
            tempSource.volume = 0f;
            tempSource.outputAudioMixerGroup = backgroundMusicSource.outputAudioMixerGroup;
            tempSource.Play();

            float elapsed = 0f;
            float oldVolume = backgroundMusicSource.volume;

            // 同时淡出旧音乐和淡入新音乐
            while (elapsed < musicFadeTime)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / musicFadeTime;

                backgroundMusicSource.volume = Mathf.Lerp(oldVolume, 0f, progress);
                tempSource.volume = Mathf.Lerp(0f, musicVolume, progress);

                yield return null;
            }

            // 切换AudioSource
            backgroundMusicSource.Stop();
            Destroy(backgroundMusicSource);
            backgroundMusicSource = tempSource;

            currentBackgroundMusic = newMusic;
            isMusicPlaying = true;
        }

        /// <summary>
        /// 设置主音量
        /// </summary>
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat(MASTER_VOLUME_PARAM, Mathf.Log10(masterVolume) * 20);
            }
        }

        /// <summary>
        /// 设置音乐音量
        /// </summary>
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat(MUSIC_VOLUME_PARAM, Mathf.Log10(musicVolume) * 20);
            }

            if (backgroundMusicSource != null && !isMuted)
            {
                backgroundMusicSource.volume = musicVolume;
            }
        }

        /// <summary>
        /// 设置音效音量
        /// </summary>
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat(SFX_VOLUME_PARAM, Mathf.Log10(sfxVolume) * 20);
            }
        }

        /// <summary>
        /// 设置语音音量
        /// </summary>
        public void SetVoiceVolume(float volume)
        {
            voiceVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat(VOICE_VOLUME_PARAM, Mathf.Log10(voiceVolume) * 20);
            }
        }

        /// <summary>
        /// 应用音量设置
        /// </summary>
        public void ApplyVolumeSettings()
        {
            SetMasterVolume(masterVolume);
            SetMusicVolume(musicVolume);
            SetSFXVolume(sfxVolume);
            SetVoiceVolume(voiceVolume);
        }

        /// <summary>
        /// 静音/取消静音
        /// </summary>
        public void SetMuted(bool muted)
        {
            isMuted = muted;

            if (audioMixer != null)
            {
                audioMixer.SetFloat(MASTER_VOLUME_PARAM, muted ? -80f : Mathf.Log10(masterVolume) * 20);
            }
        }

        // 便捷方法 - 播放预定义音效
        public void PlayButtonClick() => PlayUISound(buttonClickSound);
        public void PlayButtonHover() => PlayUISound(buttonHoverSound);
        public void PlayNotification() => PlayUISound(notificationSound);
        public void PlayError() => PlayUISound(errorSound);
        public void PlaySuccess() => PlayUISound(successSound);

        public void PlayFootstep(Vector3 position) => PlayGameSound(footstepSound, 0.5f, position);
        public void PlayDoorOpen(Vector3 position) => PlayGameSound(doorOpenSound, 0.8f, position);
        public void PlayDoorClose(Vector3 position) => PlayGameSound(doorCloseSound, 0.8f, position);
        public void PlayTaskComplete() => PlayGameSound(taskCompleteSound);
        public void PlayEmergencyMeeting() => PlayGameSound(emergencyMeetingSound, 1f);
        public void PlayKill(Vector3 position) => PlayGameSound(killSound, 0.9f, position);
        public void PlayReportBody() => PlayGameSound(reportBodySound);
        public void PlayVent(Vector3 position) => PlayGameSound(ventSound, 0.7f, position);

        // 场景音乐切换
        public void PlayMainMenuMusic() => PlayBackgroundMusic(mainMenuMusic);
        public void PlayLobbyMusic() => PlayBackgroundMusic(lobbyMusic);
        public void PlayGameplayMusic() => PlayBackgroundMusic(gameplayMusic);
        public void PlayVictoryMusic() => PlayBackgroundMusic(victoryMusic);
        public void PlayDefeatMusic() => PlayBackgroundMusic(defeatMusic);

        /// <summary>
        /// 获取当前音量设置
        /// </summary>
        public (float master, float music, float sfx, float voice) GetVolumeSettings()
        {
            return (masterVolume, musicVolume, sfxVolume, voiceVolume);
        }

        /// <summary>
        /// 获取当前播放状态
        /// </summary>
        public bool IsPlayingMusic()
        {
            return isMusicPlaying;
        }

        /// <summary>
        /// 获取当前背景音乐
        /// </summary>
        public AudioClip GetCurrentMusic()
        {
            return currentBackgroundMusic;
        }

        /// <summary>
        /// 停止所有音效
        /// </summary>
        public void StopAllSFX()
        {
            foreach (var source in activeSfxSources)
            {
                if (source != null && source.isPlaying)
                {
                    source.Stop();
                }
            }
        }

        /// <summary>
        /// 暂停所有音频
        /// </summary>
        public void PauseAll()
        {
            if (backgroundMusicSource != null)
                backgroundMusicSource.Pause();

            foreach (var source in activeSfxSources)
            {
                if (source != null && source.isPlaying)
                {
                    source.Pause();
                }
            }
        }

        /// <summary>
        /// 恢复所有音频
        /// </summary>
        public void ResumeAll()
        {
            if (backgroundMusicSource != null)
                backgroundMusicSource.UnPause();

            foreach (var source in activeSfxSources)
            {
                if (source != null)
                {
                    source.UnPause();
                }
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                PauseAll();
            }
            else
            {
                ResumeAll();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                PauseAll();
            }
            else
            {
                ResumeAll();
            }
        }
    }

    /// <summary>
    /// 音频设置数据
    /// </summary>
    [System.Serializable]
    public class AudioSettings
    {
        public float masterVolume = 1f;
        public float musicVolume = 0.7f;
        public float sfxVolume = 0.8f;
        public float voiceVolume = 1f;
        public bool isMuted = false;

        public AudioSettings()
        {
            // 默认设置
        }

        public AudioSettings(float master, float music, float sfx, float voice, bool muted = false)
        {
            masterVolume = master;
            musicVolume = music;
            sfxVolume = sfx;
            voiceVolume = voice;
            isMuted = muted;
        }
    }
}
