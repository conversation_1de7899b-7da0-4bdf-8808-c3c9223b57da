using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// Mobile UI Root 工厂类
    /// 用于创建完整的移动端UI结构
    /// </summary>
    public static class MobileUIRootFactory
    {
        /// <summary>
        /// 创建完整的Mobile UI Root结构
        /// </summary>
        public static GameObject CreateMobileUIRoot(Canvas parentCanvas, MobileUIConfig config = null)
        {
            if (parentCanvas == null)
            {
                Debug.LogError("父级Canvas不能为空");
                return null;
            }

            if (config == null)
            {
                config = MobileUIConfig.Default;
            }

            // 创建根节点
            var rootGO = CreateRootGameObject(parentCanvas, config);

            // 创建子结构
            CreateSafeAreaStructure(rootGO, config);
            CreateControlsStructure(rootGO, config);
            CreateHUDStructure(rootGO, config);

            // 配置组件
            ConfigureMobileUIRoot(rootGO, config);

            Debug.Log("Mobile UI Root 完整结构创建完成");
            return rootGO;
        }

        /// <summary>
        /// 创建根GameObject
        /// </summary>
        private static GameObject CreateRootGameObject(Canvas parentCanvas, MobileUIConfig config)
        {
            var rootGO = new GameObject("Mobile UI Root");
            rootGO.transform.SetParent(parentCanvas.transform, false);

            // 设置RectTransform为全屏
            var rectTransform = rootGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // 添加核心控制器
            var mobileUIRoot = rootGO.AddComponent<MobileUIRoot>();

            // 只在移动平台上激活
            rootGO.SetActive(Application.isMobilePlatform || config.forceEnable);

            return rootGO;
        }

        /// <summary>
        /// 创建Safe Area结构
        /// </summary>
        private static void CreateSafeAreaStructure(GameObject parent, MobileUIConfig config)
        {
            var safeAreaGO = new GameObject("Safe Area Root");
            safeAreaGO.transform.SetParent(parent.transform, false);

            var rectTransform = safeAreaGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // 添加SafeAreaHandler
            var safeAreaHandler = safeAreaGO.AddComponent<SafeAreaHandler>();
            safeAreaHandler.enableSafeArea = config.enableSafeArea;
            safeAreaHandler.adaptToNotch = config.adaptToNotch;
            safeAreaHandler.adaptToHomeIndicator = config.adaptToHomeIndicator;
            safeAreaHandler.adaptToRoundedCorners = config.adaptToRoundedCorners;
            safeAreaHandler.enableDebugVisualization = config.enableDebugVisualization;

            // 设置额外边距
            safeAreaHandler.SetAdditionalMargins(
                config.additionalMargins.top,
                config.additionalMargins.bottom,
                config.additionalMargins.left,
                config.additionalMargins.right
            );
        }

        /// <summary>
        /// 创建Controls结构
        /// </summary>
        private static void CreateControlsStructure(GameObject parent, MobileUIConfig config)
        {
            var controlsGO = new GameObject("Mobile Controls Root");
            controlsGO.transform.SetParent(parent.transform, false);

            var rectTransform = controlsGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 0);
            rectTransform.anchorMax = new Vector2(1, config.controlsHeightRatio);
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // 添加MobileControlsManager
            var controlsManager = controlsGO.AddComponent<MobileControlsManager>();

            // 配置虚拟摇杆
            controlsManager.enableVirtualJoystick = config.enableVirtualJoystick;
            controlsManager.joystickPosition = config.joystickPosition;
            controlsManager.joystickSize = config.joystickSize;

            // 配置动作按钮
            controlsManager.enableActionButtons = config.enableActionButtons;
            controlsManager.actionButtons = config.actionButtons;

            // 配置手势和触觉反馈
            controlsManager.enableGestureRecognition = config.enableGestureRecognition;
            controlsManager.enableHapticFeedback = config.enableHapticFeedback;
            controlsManager.swipeThreshold = config.swipeThreshold;
            controlsManager.tapTimeThreshold = config.tapTimeThreshold;
            controlsManager.defaultHapticType = config.defaultHapticType;

            // 性能设置
            controlsManager.updateFrequency = config.updateFrequency;
            controlsManager.enableVisualEffects = config.enableVisualEffects;

            // 创建虚拟摇杆
            if (config.enableVirtualJoystick)
            {
                CreateVirtualJoystick(controlsGO, config);
            }

            // 创建动作按钮
            if (config.enableActionButtons && config.actionButtons != null)
            {
                CreateActionButtons(controlsGO, config);
            }
        }

        /// <summary>
        /// 创建HUD结构
        /// </summary>
        private static void CreateHUDStructure(GameObject parent, MobileUIConfig config)
        {
            var hudGO = new GameObject("Mobile HUD Root");
            hudGO.transform.SetParent(parent.transform, false);

            var rectTransform = hudGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // 添加MobileHUDManager
            var hudManager = hudGO.AddComponent<MobileHUDManager>();
            hudManager.enableStatusIndicators = config.enableStatusIndicators;
            hudManager.enableNotificationSystem = config.enableNotificationSystem;
            hudManager.enableDebugPanel = config.enableDebugPanel;

            // 创建状态指示器
            if (config.enableStatusIndicators)
            {
                CreateStatusIndicators(hudGO, config);
            }

            // 创建通知系统
            if (config.enableNotificationSystem)
            {
                CreateNotificationSystem(hudGO, config);
            }

            // 创建调试面板
            if (config.enableDebugPanel)
            {
                CreateDebugPanel(hudGO, config);
            }
        }

        /// <summary>
        /// 创建虚拟摇杆
        /// </summary>
        private static void CreateVirtualJoystick(GameObject parent, MobileUIConfig config)
        {
            var joystickGO = new GameObject("Virtual Joystick");
            joystickGO.transform.SetParent(parent.transform, false);

            var rectTransform = joystickGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.zero;
            rectTransform.anchoredPosition = config.joystickPosition;
            rectTransform.sizeDelta = Vector2.one * config.joystickSize;

            // 创建背景
            var backgroundGO = new GameObject("Background");
            backgroundGO.transform.SetParent(joystickGO.transform, false);
            var bgRect = backgroundGO.AddComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;

            var bgImage = backgroundGO.AddComponent<Image>();
            bgImage.color = new Color(1, 1, 1, 0.3f);
            bgImage.sprite = CreateCircleSprite(64);

            // 创建手柄
            var handleGO = new GameObject("Handle");
            handleGO.transform.SetParent(joystickGO.transform, false);
            var handleRect = handleGO.AddComponent<RectTransform>();
            handleRect.anchorMin = new Vector2(0.5f, 0.5f);
            handleRect.anchorMax = new Vector2(0.5f, 0.5f);
            handleRect.sizeDelta = Vector2.one * (config.joystickSize * 0.4f);

            var handleImage = handleGO.AddComponent<Image>();
            handleImage.color = new Color(1, 1, 1, 0.8f);
            handleImage.sprite = CreateCircleSprite(32);

            // 添加SimpleVirtualJoystick组件
            var virtualJoystick = joystickGO.AddComponent<SimpleVirtualJoystick>();
            virtualJoystick.background = bgRect;
            virtualJoystick.handle = handleRect;
        }

        /// <summary>
        /// 创建动作按钮
        /// </summary>
        private static void CreateActionButtons(GameObject parent, MobileUIConfig config)
        {
            for (int i = 0; i < config.actionButtons.Length; i++)
            {
                var buttonConfig = config.actionButtons[i];
                if (!buttonConfig.isVisible) continue;

                var buttonGO = new GameObject(buttonConfig.buttonName);
                buttonGO.transform.SetParent(parent.transform, false);

                var rectTransform = buttonGO.AddComponent<RectTransform>();
                rectTransform.anchorMin = Vector2.one;
                rectTransform.anchorMax = Vector2.one;
                rectTransform.anchoredPosition = -buttonConfig.position;
                rectTransform.sizeDelta = Vector2.one * buttonConfig.size;

                var image = buttonGO.AddComponent<Image>();
                image.color = buttonConfig.color;
                image.sprite = buttonConfig.icon ?? CreateCircleSprite(32);

                var button = buttonGO.AddComponent<Button>();
                button.targetGraphic = image;

                // 添加MobileActionButton组件
                var actionButton = buttonGO.AddComponent<MobileActionButton>();
                actionButton.SetKeyCode(buttonConfig.keyCode);
                actionButton.SetHapticFeedback(config.enableHapticFeedback);
            }
        }

        /// <summary>
        /// 创建状态指示器
        /// </summary>
        private static void CreateStatusIndicators(GameObject parent, MobileUIConfig config)
        {
            var indicatorsGO = new GameObject("Status Indicators");
            indicatorsGO.transform.SetParent(parent.transform, false);

            var rectTransform = indicatorsGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 1);
            rectTransform.anchorMax = new Vector2(1, 1);
            rectTransform.anchoredPosition = Vector2.zero;
            rectTransform.sizeDelta = new Vector2(0, 50);

            // 创建网络状态指示器
            CreateNetworkIndicator(indicatorsGO);

            // 创建FPS计数器
            CreateFPSCounter(indicatorsGO);

            // 创建电池指示器
            CreateBatteryIndicator(indicatorsGO);
        }

        /// <summary>
        /// 创建通知系统
        /// </summary>
        private static void CreateNotificationSystem(GameObject parent, MobileUIConfig config)
        {
            var notificationGO = new GameObject("Notification System");
            notificationGO.transform.SetParent(parent.transform, false);

            var rectTransform = notificationGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 0.8f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.8f);
            rectTransform.sizeDelta = new Vector2(300, 100);

            // 添加通知管理器组件
            var notificationManager = notificationGO.AddComponent<NotificationManager>();
        }

        /// <summary>
        /// 创建调试面板
        /// </summary>
        private static void CreateDebugPanel(GameObject parent, MobileUIConfig config)
        {
            var debugGO = new GameObject("Debug Panel");
            debugGO.transform.SetParent(parent.transform, false);

            var rectTransform = debugGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 0);
            rectTransform.anchorMax = new Vector2(0.3f, 0.5f);
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // 添加背景
            var image = debugGO.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.7f);

            // 添加调试管理器组件
            var debugManager = debugGO.AddComponent<DebugPanelManager>();

            // 默认隐藏调试面板
            debugGO.SetActive(false);
        }

        /// <summary>
        /// 配置MobileUIRoot组件
        /// </summary>
        private static void ConfigureMobileUIRoot(GameObject rootGO, MobileUIConfig config)
        {
            var mobileUIRoot = rootGO.GetComponent<MobileUIRoot>();
            if (mobileUIRoot == null) return;

            mobileUIRoot.enableSafeAreaAdaptation = config.enableSafeArea;
            mobileUIRoot.enableOrientationAdaptation = config.enableOrientationAdaptation;
            mobileUIRoot.enableDynamicScaling = config.enableDynamicScaling;
            mobileUIRoot.enablePerformanceOptimization = config.enablePerformanceOptimization;

            mobileUIRoot.referenceResolution = config.referenceResolution;
            mobileUIRoot.minScaleFactor = config.minScaleFactor;
            mobileUIRoot.maxScaleFactor = config.maxScaleFactor;
            mobileUIRoot.targetFrameRate = config.targetFrameRate;
        }

        /// <summary>
        /// 创建网络指示器
        /// </summary>
        private static void CreateNetworkIndicator(GameObject parent)
        {
            var networkGO = new GameObject("Network Indicator");
            networkGO.transform.SetParent(parent.transform, false);

            var rectTransform = networkGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 0.5f);
            rectTransform.anchorMax = new Vector2(0, 0.5f);
            rectTransform.anchoredPosition = new Vector2(30, 0);
            rectTransform.sizeDelta = new Vector2(20, 20);

            var image = networkGO.AddComponent<Image>();
            image.color = Color.green;
            image.sprite = CreateCircleSprite(16);

            var networkIndicator = networkGO.AddComponent<NetworkIndicator>();
        }

        /// <summary>
        /// 创建FPS计数器
        /// </summary>
        private static void CreateFPSCounter(GameObject parent)
        {
            var fpsGO = new GameObject("FPS Counter");
            fpsGO.transform.SetParent(parent.transform, false);

            var rectTransform = fpsGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(1, 0.5f);
            rectTransform.anchorMax = new Vector2(1, 0.5f);
            rectTransform.anchoredPosition = new Vector2(-50, 0);
            rectTransform.sizeDelta = new Vector2(80, 30);

            var text = fpsGO.AddComponent<Text>();
            text.text = "60 FPS";
            text.color = Color.white;
            text.fontSize = 14;
            text.alignment = TextAnchor.MiddleCenter;
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");

            var fpsCounter = fpsGO.AddComponent<FPSCounter>();
        }

        /// <summary>
        /// 创建电池指示器
        /// </summary>
        private static void CreateBatteryIndicator(GameObject parent)
        {
            var batteryGO = new GameObject("Battery Indicator");
            batteryGO.transform.SetParent(parent.transform, false);

            var rectTransform = batteryGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(1, 0.5f);
            rectTransform.anchorMax = new Vector2(1, 0.5f);
            rectTransform.anchoredPosition = new Vector2(-150, 0);
            rectTransform.sizeDelta = new Vector2(30, 15);

            var image = batteryGO.AddComponent<Image>();
            image.color = Color.green;

            var batteryIndicator = batteryGO.AddComponent<BatteryIndicator>();
        }

        /// <summary>
        /// 创建圆形精灵
        /// </summary>
        private static Sprite CreateCircleSprite(int size)
        {
            var texture = new Texture2D(size, size);
            var center = new Vector2(size * 0.5f, size * 0.5f);
            var radius = size * 0.4f;

            for (int x = 0; x < size; x++)
            {
                for (int y = 0; y < size; y++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), center);
                    Color color = distance <= radius ? Color.white : Color.clear;
                    texture.SetPixel(x, y, color);
                }
            }

            texture.Apply();
            return Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f));
        }
    }
}
