using UnityEngine;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// Mobile UI 配置类
    /// 定义移动端UI的各种配置参数
    /// </summary>
    [System.Serializable]
    public class MobileUIConfig
    {
        [Header("基础设置")]
        public bool forceEnable = false; // 强制启用（用于编辑器测试）
        public Vector2 referenceResolution = new Vector2(1920, 1080);
        public float minScaleFactor = 0.5f;
        public float maxScaleFactor = 2f;
        public int targetFrameRate = 60;
        
        [Header("Safe Area 设置")]
        public bool enableSafeArea = true;
        public bool adaptToNotch = true;
        public bool adaptToHomeIndicator = true;
        public bool adaptToRoundedCorners = true;
        public bool enableDebugVisualization = false;
        public AdditionalMargins additionalMargins = new AdditionalMargins();
        
        [Header("布局设置")]
        public bool enableOrientationAdaptation = true;
        public bool enableDynamicScaling = true;
        public bool enablePerformanceOptimization = true;
        public float controlsHeightRatio = 0.3f; // 控制区域占屏幕高度的比例
        
        [Header("虚拟摇杆设置")]
        public bool enableVirtualJoystick = true;
        public Vector2 joystickPosition = new Vector2(150, 150);
        public float joystickSize = 120f;
        
        [Header("动作按钮设置")]
        public bool enableActionButtons = true;
        public MobileControlsManager.ActionButtonConfig[] actionButtons;
        
        [Header("手势识别设置")]
        public bool enableGestureRecognition = true;
        public float swipeThreshold = 50f;
        public float tapTimeThreshold = 0.3f;
        
        [Header("触觉反馈设置")]
        public bool enableHapticFeedback = true;
        public MobileControlsManager.HapticFeedbackType defaultHapticType = MobileControlsManager.HapticFeedbackType.Light;
        
        [Header("HUD设置")]
        public bool enableStatusIndicators = true;
        public bool enableNotificationSystem = true;
        public bool enableDebugPanel = false;
        
        [Header("性能设置")]
        public float updateFrequency = 1f;
        public bool enableVisualEffects = true;
        
        [System.Serializable]
        public struct AdditionalMargins
        {
            public float top;
            public float bottom;
            public float left;
            public float right;
            
            public AdditionalMargins(float top = 10f, float bottom = 10f, float left = 10f, float right = 10f)
            {
                this.top = top;
                this.bottom = bottom;
                this.left = left;
                this.right = right;
            }
        }
        
        /// <summary>
        /// 默认配置
        /// </summary>
        public static MobileUIConfig Default
        {
            get
            {
                var config = new MobileUIConfig();
                config.SetupDefaultActionButtons();
                return config;
            }
        }
        
        /// <summary>
        /// MainMenu 场景专用配置
        /// </summary>
        public static MobileUIConfig MainMenu
        {
            get
            {
                var config = new MobileUIConfig();
                config.enableVirtualJoystick = false; // 主菜单不需要摇杆
                config.controlsHeightRatio = 0.2f; // 减少控制区域高度
                config.SetupMainMenuActionButtons();
                return config;
            }
        }
        
        /// <summary>
        /// Lobby 场景专用配置
        /// </summary>
        public static MobileUIConfig Lobby
        {
            get
            {
                var config = new MobileUIConfig();
                config.enableVirtualJoystick = false; // 大厅不需要摇杆
                config.controlsHeightRatio = 0.25f;
                config.SetupLobbyActionButtons();
                return config;
            }
        }
        
        /// <summary>
        /// Game 场景专用配置
        /// </summary>
        public static MobileUIConfig Game
        {
            get
            {
                var config = new MobileUIConfig();
                config.enableVirtualJoystick = true; // 游戏需要摇杆
                config.controlsHeightRatio = 0.4f; // 增加控制区域高度
                config.enableDebugPanel = true; // 游戏中可能需要调试
                config.SetupGameActionButtons();
                return config;
            }
        }
        
        /// <summary>
        /// 低端设备优化配置
        /// </summary>
        public static MobileUIConfig LowEnd
        {
            get
            {
                var config = Default;
                config.enableVisualEffects = false;
                config.updateFrequency = 0.5f; // 降低更新频率
                config.enableHapticFeedback = false; // 关闭触觉反馈
                config.enableDebugVisualization = false;
                config.targetFrameRate = 30; // 降低目标帧率
                return config;
            }
        }
        
        /// <summary>
        /// 高端设备配置
        /// </summary>
        public static MobileUIConfig HighEnd
        {
            get
            {
                var config = Default;
                config.enableVisualEffects = true;
                config.updateFrequency = 2f; // 提高更新频率
                config.enableHapticFeedback = true;
                config.targetFrameRate = 60;
                return config;
            }
        }
        
        /// <summary>
        /// 设置默认动作按钮
        /// </summary>
        private void SetupDefaultActionButtons()
        {
            actionButtons = new MobileControlsManager.ActionButtonConfig[]
            {
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Settings",
                    position = new Vector2(120, 120),
                    size = 80f,
                    color = Color.white,
                    isVisible = true,
                    keyCode = KeyCode.Escape
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Menu",
                    position = new Vector2(220, 120),
                    size = 80f,
                    color = Color.cyan,
                    isVisible = true,
                    keyCode = KeyCode.Tab
                }
            };
        }
        
        /// <summary>
        /// 设置主菜单动作按钮
        /// </summary>
        private void SetupMainMenuActionButtons()
        {
            actionButtons = new MobileControlsManager.ActionButtonConfig[]
            {
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Settings",
                    position = new Vector2(100, 100),
                    size = 70f,
                    color = new Color(0.8f, 0.8f, 0.8f),
                    isVisible = true,
                    keyCode = KeyCode.Escape
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Credits",
                    position = new Vector2(180, 100),
                    size = 60f,
                    color = new Color(0.6f, 0.8f, 1f),
                    isVisible = true,
                    keyCode = KeyCode.C
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Quit",
                    position = new Vector2(250, 100),
                    size = 60f,
                    color = new Color(1f, 0.6f, 0.6f),
                    isVisible = true,
                    keyCode = KeyCode.Q
                }
            };
        }
        
        /// <summary>
        /// 设置大厅动作按钮
        /// </summary>
        private void SetupLobbyActionButtons()
        {
            actionButtons = new MobileControlsManager.ActionButtonConfig[]
            {
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Ready",
                    position = new Vector2(120, 120),
                    size = 90f,
                    color = new Color(0.6f, 1f, 0.6f),
                    isVisible = true,
                    keyCode = KeyCode.R
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Settings",
                    position = new Vector2(230, 120),
                    size = 70f,
                    color = new Color(0.8f, 0.8f, 0.8f),
                    isVisible = true,
                    keyCode = KeyCode.Escape
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Leave",
                    position = new Vector2(320, 120),
                    size = 70f,
                    color = new Color(1f, 0.7f, 0.7f),
                    isVisible = true,
                    keyCode = KeyCode.L
                }
            };
        }
        
        /// <summary>
        /// 设置游戏动作按钮
        /// </summary>
        private void SetupGameActionButtons()
        {
            actionButtons = new MobileControlsManager.ActionButtonConfig[]
            {
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Use",
                    position = new Vector2(120, 120),
                    size = 80f,
                    color = new Color(0.6f, 0.8f, 1f),
                    isVisible = true,
                    keyCode = KeyCode.E
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Kill",
                    position = new Vector2(220, 120),
                    size = 80f,
                    color = new Color(1f, 0.4f, 0.4f),
                    isVisible = false, // 默认隐藏，根据角色显示
                    keyCode = KeyCode.Q
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Report",
                    position = new Vector2(120, 220),
                    size = 70f,
                    color = new Color(1f, 0.8f, 0.2f),
                    isVisible = true,
                    keyCode = KeyCode.R
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Emergency",
                    position = new Vector2(220, 220),
                    size = 70f,
                    color = new Color(1f, 0.2f, 0.2f),
                    isVisible = true,
                    keyCode = KeyCode.Space
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Vent",
                    position = new Vector2(320, 120),
                    size = 70f,
                    color = new Color(0.5f, 0.5f, 0.5f),
                    isVisible = false, // 默认隐藏，鸭子角色显示
                    keyCode = KeyCode.V
                },
                new MobileControlsManager.ActionButtonConfig
                {
                    buttonName = "Map",
                    position = new Vector2(320, 220),
                    size = 60f,
                    color = new Color(0.8f, 0.8f, 0.6f),
                    isVisible = true,
                    keyCode = KeyCode.M
                }
            };
        }
        
        /// <summary>
        /// 根据设备性能自动选择配置
        /// </summary>
        public static MobileUIConfig GetOptimalConfig()
        {
            // 简单的设备性能检测
            int processorCount = SystemInfo.processorCount;
            int systemMemorySize = SystemInfo.systemMemorySize;
            
            // 高端设备判断条件
            bool isHighEnd = processorCount >= 6 && systemMemorySize >= 4096;
            
            // 低端设备判断条件
            bool isLowEnd = processorCount <= 2 || systemMemorySize <= 2048;
            
            if (isHighEnd)
            {
                Debug.Log("检测到高端设备，使用高端配置");
                return HighEnd;
            }
            else if (isLowEnd)
            {
                Debug.Log("检测到低端设备，使用低端配置");
                return LowEnd;
            }
            else
            {
                Debug.Log("使用默认配置");
                return Default;
            }
        }
        
        /// <summary>
        /// 根据场景名称获取配置
        /// </summary>
        public static MobileUIConfig GetConfigForScene(string sceneName)
        {
            switch (sceneName.ToLower())
            {
                case "mainmenu":
                case "main menu":
                    return MainMenu;
                    
                case "lobby":
                    return Lobby;
                    
                case "skeld":
                case "mira":
                case "polus":
                    return Game;
                    
                default:
                    return Default;
            }
        }
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        public bool ValidateConfig()
        {
            bool isValid = true;
            
            if (referenceResolution.x <= 0 || referenceResolution.y <= 0)
            {
                Debug.LogError("参考分辨率必须大于0");
                isValid = false;
            }
            
            if (minScaleFactor <= 0 || maxScaleFactor <= minScaleFactor)
            {
                Debug.LogError("缩放因子配置错误");
                isValid = false;
            }
            
            if (controlsHeightRatio <= 0 || controlsHeightRatio > 1)
            {
                Debug.LogError("控制区域高度比例必须在0-1之间");
                isValid = false;
            }
            
            if (joystickSize <= 0)
            {
                Debug.LogError("摇杆大小必须大于0");
                isValid = false;
            }
            
            return isValid;
        }
    }
}
