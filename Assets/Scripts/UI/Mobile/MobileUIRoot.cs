using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// Mobile UI Root 控制器
    /// 管理移动端UI的根节点和自适应布局
    /// </summary>
    public class MobileUIRoot : MonoBehaviour
    {
        [Header("UI根节点引用")]
        [SerializeField] private RectTransform safeAreaRoot;
        [SerializeField] private RectTransform mobileControlsRoot;
        [SerializeField] private RectTransform mobileHUDRoot;

        [Header("自适应设置")]
        [SerializeField] public bool enableSafeAreaAdaptation = true;
        [SerializeField] public bool enableOrientationAdaptation = true;
        [SerializeField] public bool enableDynamicScaling = true;

        [Header("布局设置")]
        [SerializeField] public Vector2 referenceResolution = new Vector2(1920, 1080);
        [SerializeField] public float minScaleFactor = 0.5f;
        [SerializeField] public float maxScaleFactor = 2f;

        [Header("性能优化")]
        [SerializeField] public bool enablePerformanceOptimization = true;
        [SerializeField] public int targetFrameRate = 60;

        // 组件引用
        private SafeAreaHandler safeAreaHandler;
        private MobileControlsManager controlsManager;
        private MobileHUDManager hudManager;
        private Canvas parentCanvas;
        private CanvasScaler canvasScaler;

        // 状态变量
        private ScreenOrientation lastOrientation;
        private Vector2 lastScreenSize;
        private bool isInitialized = false;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            SetupMobileUI();
            ApplyInitialLayout();

            // 只在移动平台上激活
            gameObject.SetActive(Application.isMobilePlatform);
        }

        private void Update()
        {
            if (!isInitialized) return;

            CheckForLayoutChanges();

            if (enablePerformanceOptimization)
            {
                MonitorPerformance();
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 获取父级Canvas组件
            parentCanvas = GetComponentInParent<Canvas>();
            canvasScaler = GetComponentInParent<CanvasScaler>();

            // 创建子节点结构
            CreateMobileUIStructure();

            // 获取或添加管理组件
            safeAreaHandler = GetOrAddComponent<SafeAreaHandler>();
            controlsManager = GetOrAddComponent<MobileControlsManager>();
            hudManager = GetOrAddComponent<MobileHUDManager>();
        }

        /// <summary>
        /// 创建移动端UI结构
        /// </summary>
        private void CreateMobileUIStructure()
        {
            var rectTransform = GetComponent<RectTransform>();

            // 设置为全屏
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            // 创建Safe Area根节点
            if (safeAreaRoot == null)
            {
                safeAreaRoot = CreateChildUI("Safe Area Root");
                safeAreaRoot.anchorMin = Vector2.zero;
                safeAreaRoot.anchorMax = Vector2.one;
            }

            // 创建Mobile Controls根节点
            if (mobileControlsRoot == null)
            {
                mobileControlsRoot = CreateChildUI("Mobile Controls Root", safeAreaRoot);
                SetupControlsLayout(mobileControlsRoot);
            }

            // 创建Mobile HUD根节点
            if (mobileHUDRoot == null)
            {
                mobileHUDRoot = CreateChildUI("Mobile HUD Root", safeAreaRoot);
                SetupHUDLayout(mobileHUDRoot);
            }
        }

        /// <summary>
        /// 创建子UI节点
        /// </summary>
        private RectTransform CreateChildUI(string name, RectTransform parent = null)
        {
            var go = new GameObject(name);
            go.transform.SetParent(parent ?? transform, false);

            var rectTransform = go.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            return rectTransform;
        }

        /// <summary>
        /// 设置控制区域布局
        /// </summary>
        private void SetupControlsLayout(RectTransform controlsRoot)
        {
            // 控制区域通常在屏幕底部
            controlsRoot.anchorMin = new Vector2(0, 0);
            controlsRoot.anchorMax = new Vector2(1, 0.3f); // 占屏幕下方30%
            controlsRoot.offsetMin = Vector2.zero;
            controlsRoot.offsetMax = Vector2.zero;
        }

        /// <summary>
        /// 设置HUD布局
        /// </summary>
        private void SetupHUDLayout(RectTransform hudRoot)
        {
            // HUD区域覆盖整个安全区域
            hudRoot.anchorMin = Vector2.zero;
            hudRoot.anchorMax = Vector2.one;
            hudRoot.offsetMin = Vector2.zero;
            hudRoot.offsetMax = Vector2.zero;
        }

        /// <summary>
        /// 设置移动端UI
        /// </summary>
        private void SetupMobileUI()
        {
            // 配置Safe Area适配
            if (enableSafeAreaAdaptation && safeAreaHandler != null)
            {
                safeAreaHandler.SetSafeAreaRoot(safeAreaRoot);
                safeAreaHandler.ApplySafeArea();
            }

            // 配置控制管理器
            if (controlsManager != null)
            {
                controlsManager.SetControlsRoot(mobileControlsRoot);
                controlsManager.InitializeControls();
            }

            // 配置HUD管理器
            if (hudManager != null)
            {
                hudManager.SetHUDRoot(mobileHUDRoot);
                hudManager.InitializeHUD();
            }

            // 记录初始状态
            lastOrientation = Screen.orientation;
            lastScreenSize = new Vector2(Screen.width, Screen.height);

            isInitialized = true;

            Debug.Log("Mobile UI Root 初始化完成");
        }

        /// <summary>
        /// 应用初始布局
        /// </summary>
        private void ApplyInitialLayout()
        {
            if (enableDynamicScaling)
            {
                ApplyDynamicScaling();
            }

            if (enableOrientationAdaptation)
            {
                AdaptToOrientation();
            }
        }

        /// <summary>
        /// 检查布局变化
        /// </summary>
        private void CheckForLayoutChanges()
        {
            bool needsUpdate = false;

            // 检查屏幕方向变化
            if (enableOrientationAdaptation && Screen.orientation != lastOrientation)
            {
                lastOrientation = Screen.orientation;
                needsUpdate = true;
            }

            // 检查屏幕尺寸变化
            Vector2 currentScreenSize = new Vector2(Screen.width, Screen.height);
            if (Vector2.Distance(currentScreenSize, lastScreenSize) > 10f)
            {
                lastScreenSize = currentScreenSize;
                needsUpdate = true;
            }

            if (needsUpdate)
            {
                UpdateLayout();
            }
        }

        /// <summary>
        /// 更新布局
        /// </summary>
        private void UpdateLayout()
        {
            if (enableSafeAreaAdaptation && safeAreaHandler != null)
            {
                safeAreaHandler.ApplySafeArea();
            }

            if (enableDynamicScaling)
            {
                ApplyDynamicScaling();
            }

            if (enableOrientationAdaptation)
            {
                AdaptToOrientation();
            }

            // 通知子组件布局已更新
            controlsManager?.OnLayoutUpdated();
            hudManager?.OnLayoutUpdated();
        }

        /// <summary>
        /// 应用动态缩放
        /// </summary>
        private void ApplyDynamicScaling()
        {
            if (canvasScaler == null) return;

            float screenAspect = (float)Screen.width / Screen.height;
            float referenceAspect = referenceResolution.x / referenceResolution.y;

            // 根据屏幕比例调整缩放
            float scaleFactor = screenAspect > referenceAspect ?
                Screen.height / referenceResolution.y :
                Screen.width / referenceResolution.x;

            scaleFactor = Mathf.Clamp(scaleFactor, minScaleFactor, maxScaleFactor);

            // 应用缩放（如果使用CanvasScaler）
            if (canvasScaler.uiScaleMode == CanvasScaler.ScaleMode.ScaleWithScreenSize)
            {
                canvasScaler.matchWidthOrHeight = screenAspect > referenceAspect ? 1f : 0f;
            }
        }

        /// <summary>
        /// 适配屏幕方向
        /// </summary>
        private void AdaptToOrientation()
        {
            bool isLandscape = Screen.width > Screen.height;

            // 根据方向调整控制区域大小
            if (mobileControlsRoot != null)
            {
                float controlsHeight = isLandscape ? 0.4f : 0.3f;
                mobileControlsRoot.anchorMax = new Vector2(1, controlsHeight);
            }

            // 通知控制管理器方向变化
            controlsManager?.OnOrientationChanged(isLandscape);
            hudManager?.OnOrientationChanged(isLandscape);
        }

        /// <summary>
        /// 性能监控
        /// </summary>
        private void MonitorPerformance()
        {
            float currentFPS = 1f / Time.deltaTime;

            if (currentFPS < targetFrameRate * 0.8f)
            {
                // 性能不足时降低UI质量
                OptimizeForPerformance();
            }
        }

        /// <summary>
        /// 性能优化
        /// </summary>
        private void OptimizeForPerformance()
        {
            // 降低UI更新频率
            controlsManager?.SetUpdateFrequency(0.5f);
            hudManager?.SetUpdateFrequency(0.5f);

            // 禁用一些视觉效果
            controlsManager?.SetVisualEffectsEnabled(false);
        }

        /// <summary>
        /// 获取或添加组件
        /// </summary>
        private T GetOrAddComponent<T>() where T : Component
        {
            var component = GetComponent<T>();
            if (component == null)
            {
                component = gameObject.AddComponent<T>();
            }
            return component;
        }

        /// <summary>
        /// 获取Safe Area根节点
        /// </summary>
        public RectTransform GetSafeAreaRoot()
        {
            return safeAreaRoot;
        }

        /// <summary>
        /// 获取Mobile Controls根节点
        /// </summary>
        public RectTransform GetMobileControlsRoot()
        {
            return mobileControlsRoot;
        }

        /// <summary>
        /// 获取Mobile HUD根节点
        /// </summary>
        public RectTransform GetMobileHUDRoot()
        {
            return mobileHUDRoot;
        }

        /// <summary>
        /// 强制更新布局
        /// </summary>
        public void ForceUpdateLayout()
        {
            UpdateLayout();
        }

        /// <summary>
        /// 设置是否启用Safe Area适配
        /// </summary>
        public void SetSafeAreaAdaptationEnabled(bool enabled)
        {
            enableSafeAreaAdaptation = enabled;
            if (enabled && safeAreaHandler != null)
            {
                safeAreaHandler.ApplySafeArea();
            }
        }

        /// <summary>
        /// 屏幕方向变化回调
        /// </summary>
        public void OnOrientationChanged(bool isLandscape)
        {
            if (enableOrientationAdaptation)
            {
                AdaptToOrientation(isLandscape);
            }
        }

        /// <summary>
        /// 适配屏幕方向
        /// </summary>
        private void AdaptToOrientation(bool isLandscape)
        {
            // 通知子组件屏幕方向变化
            if (safeAreaHandler != null)
            {
                safeAreaHandler.OnOrientationChanged(isLandscape);
            }

            if (controlsManager != null)
            {
                controlsManager.OnOrientationChanged(isLandscape);
            }

            if (hudManager != null)
            {
                hudManager.OnOrientationChanged(isLandscape);
            }

            Debug.Log($"Mobile UI 适配屏幕方向: {(isLandscape ? "横屏" : "竖屏")}");
        }
    }
}
