using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 移动端HUD管理器
    /// 管理移动端的状态指示器、通知系统和调试面板
    /// </summary>
    public class MobileHUDManager : MonoBehaviour
    {
        [Header("HUD根节点")]
        [SerializeField] private RectTransform hudRoot;
        
        [Header("功能开关")]
        [SerializeField] public bool enableStatusIndicators = true;
        [SerializeField] public bool enableNotificationSystem = true;
        [SerializeField] public bool enableDebugPanel = false;
        
        [Header("状态指示器设置")]
        [SerializeField] private bool showNetworkStatus = true;
        [SerializeField] private bool showFPSCounter = true;
        [SerializeField] private bool showBatteryStatus = true;
        [SerializeField] private bool showPerformanceMonitor = false;
        
        [Header("通知系统设置")]
        [SerializeField] private float notificationDuration = 3f;
        [SerializeField] private int maxNotifications = 5;
        [SerializeField] private Vector2 notificationSize = new Vector2(300, 60);
        
        [Header("调试面板设置")]
        [SerializeField] private KeyCode debugToggleKey = KeyCode.F3;
        [SerializeField] private bool showTouchVisualization = true;
        [SerializeField] private bool showSafeAreaInfo = true;
        [SerializeField] private bool showPerformanceData = true;
        
        // 组件引用
        private GameObject statusIndicatorsPanel;
        private GameObject notificationPanel;
        private GameObject debugPanel;
        
        // 状态指示器组件
        private NetworkIndicator networkIndicator;
        private FPSCounter fpsCounter;
        private BatteryIndicator batteryIndicator;
        private PerformanceMonitor performanceMonitor;
        
        // 通知系统
        private NotificationManager notificationManager;
        
        // 调试系统
        private DebugPanelManager debugPanelManager;
        
        // 更新控制
        private float updateFrequency = 1f;
        private float lastUpdateTime;
        
        private void Awake()
        {
            if (hudRoot == null)
            {
                hudRoot = GetComponent<RectTransform>();
            }
        }
        
        private void Start()
        {
            // 只在移动平台上初始化
            if (Application.isMobilePlatform)
            {
                InitializeHUD();
            }
            else
            {
                gameObject.SetActive(false);
            }
        }
        
        private void Update()
        {
            // 控制更新频率
            if (Time.time - lastUpdateTime < 1f / updateFrequency) return;
            lastUpdateTime = Time.time;
            
            UpdateHUD();
            HandleDebugInput();
        }
        
        /// <summary>
        /// 初始化HUD
        /// </summary>
        public void InitializeHUD()
        {
            CreateStatusIndicators();
            CreateNotificationSystem();
            CreateDebugPanel();
            
            Debug.Log("Mobile HUD 初始化完成");
        }
        
        /// <summary>
        /// 创建状态指示器
        /// </summary>
        private void CreateStatusIndicators()
        {
            if (!enableStatusIndicators) return;
            
            statusIndicatorsPanel = new GameObject("Status Indicators");
            statusIndicatorsPanel.transform.SetParent(hudRoot, false);
            
            var rectTransform = statusIndicatorsPanel.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 1);
            rectTransform.anchorMax = new Vector2(1, 1);
            rectTransform.anchoredPosition = Vector2.zero;
            rectTransform.sizeDelta = new Vector2(0, 40);
            
            // 创建各种状态指示器
            if (showNetworkStatus)
            {
                CreateNetworkIndicator();
            }
            
            if (showFPSCounter)
            {
                CreateFPSCounter();
            }
            
            if (showBatteryStatus)
            {
                CreateBatteryIndicator();
            }
            
            if (showPerformanceMonitor)
            {
                CreatePerformanceMonitor();
            }
        }
        
        /// <summary>
        /// 创建网络状态指示器
        /// </summary>
        private void CreateNetworkIndicator()
        {
            var networkGO = new GameObject("Network Indicator");
            networkGO.transform.SetParent(statusIndicatorsPanel.transform, false);
            
            var rectTransform = networkGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 0.5f);
            rectTransform.anchorMax = new Vector2(0, 0.5f);
            rectTransform.anchoredPosition = new Vector2(30, 0);
            rectTransform.sizeDelta = new Vector2(20, 20);
            
            var image = networkGO.AddComponent<Image>();
            image.color = Color.green;
            
            networkIndicator = networkGO.AddComponent<NetworkIndicator>();
        }
        
        /// <summary>
        /// 创建FPS计数器
        /// </summary>
        private void CreateFPSCounter()
        {
            var fpsGO = new GameObject("FPS Counter");
            fpsGO.transform.SetParent(statusIndicatorsPanel.transform, false);
            
            var rectTransform = fpsGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(1, 0.5f);
            rectTransform.anchorMax = new Vector2(1, 0.5f);
            rectTransform.anchoredPosition = new Vector2(-60, 0);
            rectTransform.sizeDelta = new Vector2(100, 30);
            
            var text = fpsGO.AddComponent<Text>();
            text.text = "60 FPS";
            text.color = Color.white;
            text.fontSize = 12;
            text.alignment = TextAnchor.MiddleCenter;
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            
            fpsCounter = fpsGO.AddComponent<FPSCounter>();
        }
        
        /// <summary>
        /// 创建电池指示器
        /// </summary>
        private void CreateBatteryIndicator()
        {
            var batteryGO = new GameObject("Battery Indicator");
            batteryGO.transform.SetParent(statusIndicatorsPanel.transform, false);
            
            var rectTransform = batteryGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(1, 0.5f);
            rectTransform.anchorMax = new Vector2(1, 0.5f);
            rectTransform.anchoredPosition = new Vector2(-170, 0);
            rectTransform.sizeDelta = new Vector2(80, 20);
            
            var text = batteryGO.AddComponent<Text>();
            text.text = "100%";
            text.color = Color.white;
            text.fontSize = 12;
            text.alignment = TextAnchor.MiddleCenter;
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            
            batteryIndicator = batteryGO.AddComponent<BatteryIndicator>();
        }
        
        /// <summary>
        /// 创建性能监控器
        /// </summary>
        private void CreatePerformanceMonitor()
        {
            var perfGO = new GameObject("Performance Monitor");
            perfGO.transform.SetParent(statusIndicatorsPanel.transform, false);
            
            var rectTransform = perfGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.anchoredPosition = Vector2.zero;
            rectTransform.sizeDelta = new Vector2(120, 30);
            
            var text = perfGO.AddComponent<Text>();
            text.text = "CPU: 0%";
            text.color = Color.white;
            text.fontSize = 10;
            text.alignment = TextAnchor.MiddleCenter;
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            
            performanceMonitor = perfGO.AddComponent<PerformanceMonitor>();
        }
        
        /// <summary>
        /// 创建通知系统
        /// </summary>
        private void CreateNotificationSystem()
        {
            if (!enableNotificationSystem) return;
            
            notificationPanel = new GameObject("Notification Panel");
            notificationPanel.transform.SetParent(hudRoot, false);
            
            var rectTransform = notificationPanel.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 0.8f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.8f);
            rectTransform.anchoredPosition = Vector2.zero;
            rectTransform.sizeDelta = notificationSize;
            
            notificationManager = notificationPanel.AddComponent<NotificationManager>();
            notificationManager.maxNotifications = maxNotifications;
            notificationManager.notificationDuration = notificationDuration;
        }
        
        /// <summary>
        /// 创建调试面板
        /// </summary>
        private void CreateDebugPanel()
        {
            debugPanel = new GameObject("Debug Panel");
            debugPanel.transform.SetParent(hudRoot, false);
            
            var rectTransform = debugPanel.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 0);
            rectTransform.anchorMax = new Vector2(0.4f, 0.6f);
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            
            // 添加背景
            var image = debugPanel.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.8f);
            
            debugPanelManager = debugPanel.AddComponent<DebugPanelManager>();
            debugPanelManager.showTouchVisualization = showTouchVisualization;
            debugPanelManager.showSafeAreaInfo = showSafeAreaInfo;
            debugPanelManager.showPerformanceData = showPerformanceData;
            
            // 默认隐藏调试面板
            debugPanel.SetActive(enableDebugPanel);
        }
        
        /// <summary>
        /// 更新HUD
        /// </summary>
        private void UpdateHUD()
        {
            // 更新状态指示器
            if (networkIndicator != null)
            {
                networkIndicator.UpdateStatus();
            }
            
            if (fpsCounter != null)
            {
                fpsCounter.UpdateFPS();
            }
            
            if (batteryIndicator != null)
            {
                batteryIndicator.UpdateBattery();
            }
            
            if (performanceMonitor != null)
            {
                performanceMonitor.UpdatePerformance();
            }
            
            // 更新通知系统
            if (notificationManager != null)
            {
                notificationManager.UpdateNotifications();
            }
            
            // 更新调试面板
            if (debugPanelManager != null && debugPanel.activeInHierarchy)
            {
                debugPanelManager.UpdateDebugInfo();
            }
        }
        
        /// <summary>
        /// 处理调试输入
        /// </summary>
        private void HandleDebugInput()
        {
            if (Input.GetKeyDown(debugToggleKey))
            {
                ToggleDebugPanel();
            }
        }
        
        /// <summary>
        /// 切换调试面板显示
        /// </summary>
        public void ToggleDebugPanel()
        {
            if (debugPanel != null)
            {
                bool isActive = debugPanel.activeInHierarchy;
                debugPanel.SetActive(!isActive);
                Debug.Log($"调试面板: {(!isActive ? "显示" : "隐藏")}");
            }
        }
        
        /// <summary>
        /// 显示通知
        /// </summary>
        public void ShowNotification(string message, float duration = -1f)
        {
            if (notificationManager != null)
            {
                notificationManager.ShowNotification(message, duration > 0 ? duration : notificationDuration);
            }
        }
        
        /// <summary>
        /// 布局更新回调
        /// </summary>
        public void OnLayoutUpdated()
        {
            // 重新调整HUD元素位置
            AdjustHUDLayout();
        }
        
        /// <summary>
        /// 屏幕方向变化回调
        /// </summary>
        public void OnOrientationChanged(bool isLandscape)
        {
            // 根据方向调整HUD布局
            if (isLandscape)
            {
                AdjustForLandscape();
            }
            else
            {
                AdjustForPortrait();
            }
        }
        
        /// <summary>
        /// 调整HUD布局
        /// </summary>
        private void AdjustHUDLayout()
        {
            // 根据安全区域调整HUD元素位置
            var safeAreaHandler = GetComponentInParent<SafeAreaHandler>();
            if (safeAreaHandler != null)
            {
                var safeAreaInfo = safeAreaHandler.GetSafeAreaInfo();
                AdjustForSafeArea(safeAreaInfo);
            }
        }
        
        /// <summary>
        /// 为横屏调整布局
        /// </summary>
        private void AdjustForLandscape()
        {
            // 横屏时可能需要调整状态指示器的布局
            if (statusIndicatorsPanel != null)
            {
                var rect = statusIndicatorsPanel.GetComponent<RectTransform>();
                rect.sizeDelta = new Vector2(0, 35); // 稍微减小高度
            }
        }
        
        /// <summary>
        /// 为竖屏调整布局
        /// </summary>
        private void AdjustForPortrait()
        {
            // 恢复默认布局
            if (statusIndicatorsPanel != null)
            {
                var rect = statusIndicatorsPanel.GetComponent<RectTransform>();
                rect.sizeDelta = new Vector2(0, 40);
            }
        }
        
        /// <summary>
        /// 根据安全区域调整
        /// </summary>
        private void AdjustForSafeArea(SafeAreaInfo safeAreaInfo)
        {
            // 调整状态指示器位置避开顶部安全区域
            if (statusIndicatorsPanel != null && safeAreaInfo.topInset > 0)
            {
                var rect = statusIndicatorsPanel.GetComponent<RectTransform>();
                rect.anchoredPosition = new Vector2(0, -safeAreaInfo.topInset);
            }
        }
        
        /// <summary>
        /// 设置HUD根节点
        /// </summary>
        public void SetHUDRoot(RectTransform root)
        {
            hudRoot = root;
        }
        
        /// <summary>
        /// 设置更新频率
        /// </summary>
        public void SetUpdateFrequency(float frequency)
        {
            updateFrequency = frequency;
        }
        
        /// <summary>
        /// 获取通知管理器
        /// </summary>
        public NotificationManager GetNotificationManager()
        {
            return notificationManager;
        }
        
        /// <summary>
        /// 获取调试面板管理器
        /// </summary>
        public DebugPanelManager GetDebugPanelManager()
        {
            return debugPanelManager;
        }
    }
}
