using UnityEngine;
using System.Collections.Generic;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 手势识别器
    /// 识别滑动、点击、长按等手势
    /// </summary>
    public class GestureRecognizer : MonoBehaviour
    {
        [Header("手势设置")]
        [SerializeField] private bool enableGestureRecognition = true;
        [SerializeField] private float swipeThreshold = 50f;
        [SerializeField] private float tapTimeThreshold = 0.3f;
        [SerializeField] private float longPressThreshold = 1f;
        [SerializeField] private float multiTouchThreshold = 5f; // 多点触控最小距离阈值（像素）

        [Header("调试设置")]
        [SerializeField] private bool enableDebugVisualization = false;
        [SerializeField] private bool logGestureEvents = false;

        // 触摸状态
        private Dictionary<int, TouchInfo> activeTouches = new Dictionary<int, TouchInfo>();
        private List<Vector2> swipePoints = new List<Vector2>();

        // 手势事件
        public System.Action<Vector2> OnTap;
        public System.Action<Vector2> OnLongPress;
        public System.Action<SwipeDirection, Vector2, float> OnSwipe;
        public System.Action<float> OnPinch;
        public System.Action<Vector2> OnDrag;

        // 手势类型
        public enum SwipeDirection
        {
            Up,
            Down,
            Left,
            Right,
            UpLeft,
            UpRight,
            DownLeft,
            DownRight
        }

        // 触摸信息
        private struct TouchInfo
        {
            public Vector2 startPosition;
            public Vector2 currentPosition;
            public float startTime;
            public bool isLongPress;
            public bool hasMoved;
        }

        private void Update()
        {
            if (!enableGestureRecognition) return;

            ProcessTouchInput();
            ProcessMouseInput(); // 用于编辑器测试
        }

        /// <summary>
        /// 处理触摸输入
        /// </summary>
        private void ProcessTouchInput()
        {
            // 处理所有触摸点
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                ProcessTouch(touch.fingerId, touch.position, touch.phase);
            }

            // 清理已结束的触摸
            var touchesToRemove = new List<int>();
            foreach (var kvp in activeTouches)
            {
                bool touchExists = false;
                for (int i = 0; i < Input.touchCount; i++)
                {
                    if (Input.GetTouch(i).fingerId == kvp.Key)
                    {
                        touchExists = true;
                        break;
                    }
                }

                if (!touchExists)
                {
                    touchesToRemove.Add(kvp.Key);
                }
            }

            foreach (int touchId in touchesToRemove)
            {
                activeTouches.Remove(touchId);
            }

            // 处理多点触控手势
            if (Input.touchCount >= 2)
            {
                ProcessMultiTouch();
            }
        }

        /// <summary>
        /// 处理鼠标输入（编辑器测试用）
        /// </summary>
        private void ProcessMouseInput()
        {
            if (Input.touchCount > 0) return; // 如果有触摸输入，忽略鼠标

            if (Input.GetMouseButtonDown(0))
            {
                ProcessTouch(0, Input.mousePosition, TouchPhase.Began);
            }
            else if (Input.GetMouseButton(0))
            {
                ProcessTouch(0, Input.mousePosition, TouchPhase.Moved);
            }
            else if (Input.GetMouseButtonUp(0))
            {
                ProcessTouch(0, Input.mousePosition, TouchPhase.Ended);
            }
        }

        /// <summary>
        /// 处理单个触摸
        /// </summary>
        private void ProcessTouch(int touchId, Vector2 position, TouchPhase phase)
        {
            switch (phase)
            {
                case TouchPhase.Began:
                    HandleTouchBegan(touchId, position);
                    break;

                case TouchPhase.Moved:
                    HandleTouchMoved(touchId, position);
                    break;

                case TouchPhase.Ended:
                case TouchPhase.Canceled:
                    HandleTouchEnded(touchId, position);
                    break;

                case TouchPhase.Stationary:
                    HandleTouchStationary(touchId, position);
                    break;
            }
        }

        /// <summary>
        /// 处理触摸开始
        /// </summary>
        private void HandleTouchBegan(int touchId, Vector2 position)
        {
            var touchInfo = new TouchInfo
            {
                startPosition = position,
                currentPosition = position,
                startTime = Time.time,
                isLongPress = false,
                hasMoved = false
            };

            activeTouches[touchId] = touchInfo;
            swipePoints.Clear();
            swipePoints.Add(position);

            if (logGestureEvents)
            {
                Debug.Log($"Touch began: {touchId} at {position}");
            }
        }

        /// <summary>
        /// 处理触摸移动
        /// </summary>
        private void HandleTouchMoved(int touchId, Vector2 position)
        {
            if (!activeTouches.ContainsKey(touchId)) return;

            var touchInfo = activeTouches[touchId];
            touchInfo.currentPosition = position;

            // 检查是否移动了足够的距离
            float distance = Vector2.Distance(touchInfo.startPosition, position);
            if (distance > 10f) // 最小移动距离
            {
                touchInfo.hasMoved = true;

                // 触发拖拽事件
                OnDrag?.Invoke(position);
            }

            activeTouches[touchId] = touchInfo;
            swipePoints.Add(position);

            if (logGestureEvents && touchInfo.hasMoved)
            {
                Debug.Log($"Touch moved: {touchId} to {position}, distance: {distance}");
            }
        }

        /// <summary>
        /// 处理触摸结束
        /// </summary>
        private void HandleTouchEnded(int touchId, Vector2 position)
        {
            if (!activeTouches.ContainsKey(touchId)) return;

            var touchInfo = activeTouches[touchId];
            float touchDuration = Time.time - touchInfo.startTime;
            float distance = Vector2.Distance(touchInfo.startPosition, position);

            // 判断手势类型
            if (!touchInfo.hasMoved && touchDuration < tapTimeThreshold)
            {
                // 点击
                OnTap?.Invoke(position);
                if (logGestureEvents)
                {
                    Debug.Log($"Tap detected at {position}");
                }
            }
            else if (!touchInfo.hasMoved && touchDuration >= longPressThreshold)
            {
                // 长按
                OnLongPress?.Invoke(position);
                if (logGestureEvents)
                {
                    Debug.Log($"Long press detected at {position}");
                }
            }
            else if (touchInfo.hasMoved && distance >= swipeThreshold)
            {
                // 滑动
                var swipeDirection = GetSwipeDirection(touchInfo.startPosition, position);
                OnSwipe?.Invoke(swipeDirection, touchInfo.startPosition, distance);
                if (logGestureEvents)
                {
                    Debug.Log($"Swipe detected: {swipeDirection}, distance: {distance}");
                }
            }

            activeTouches.Remove(touchId);
        }

        /// <summary>
        /// 处理触摸静止
        /// </summary>
        private void HandleTouchStationary(int touchId, Vector2 position)
        {
            if (!activeTouches.ContainsKey(touchId)) return;

            var touchInfo = activeTouches[touchId];
            float touchDuration = Time.time - touchInfo.startTime;

            // 检查长按
            if (!touchInfo.isLongPress && !touchInfo.hasMoved && touchDuration >= longPressThreshold)
            {
                touchInfo.isLongPress = true;
                activeTouches[touchId] = touchInfo;

                OnLongPress?.Invoke(position);
                if (logGestureEvents)
                {
                    Debug.Log($"Long press detected at {position}");
                }
            }
        }

        /// <summary>
        /// 处理多点触控
        /// </summary>
        private void ProcessMultiTouch()
        {
            if (Input.touchCount == 2)
            {
                Touch touch1 = Input.GetTouch(0);
                Touch touch2 = Input.GetTouch(1);

                // 计算两点间距离
                float currentDistance = Vector2.Distance(touch1.position, touch2.position);

                // 如果两个触摸点都在移动，可能是缩放手势
                if (touch1.phase == TouchPhase.Moved || touch2.phase == TouchPhase.Moved)
                {
                    // 计算上一帧的距离
                    Vector2 touch1PrevPos = touch1.position - touch1.deltaPosition;
                    Vector2 touch2PrevPos = touch2.position - touch2.deltaPosition;
                    float prevDistance = Vector2.Distance(touch1PrevPos, touch2PrevPos);

                    // 计算缩放比例 - 使用multiTouchThreshold作为最小缩放阈值
                    float deltaDistance = currentDistance - prevDistance;
                    if (Mathf.Abs(deltaDistance) > multiTouchThreshold)
                    {
                        float pinchRatio = currentDistance / prevDistance;
                        OnPinch?.Invoke(pinchRatio);

                        if (logGestureEvents)
                        {
                            Debug.Log($"Pinch detected: ratio {pinchRatio}");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取滑动方向
        /// </summary>
        private SwipeDirection GetSwipeDirection(Vector2 startPos, Vector2 endPos)
        {
            Vector2 direction = endPos - startPos;
            float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;

            // 将角度转换为0-360度
            if (angle < 0) angle += 360;

            // 根据角度确定方向
            if (angle >= 337.5f || angle < 22.5f)
                return SwipeDirection.Right;
            else if (angle >= 22.5f && angle < 67.5f)
                return SwipeDirection.UpRight;
            else if (angle >= 67.5f && angle < 112.5f)
                return SwipeDirection.Up;
            else if (angle >= 112.5f && angle < 157.5f)
                return SwipeDirection.UpLeft;
            else if (angle >= 157.5f && angle < 202.5f)
                return SwipeDirection.Left;
            else if (angle >= 202.5f && angle < 247.5f)
                return SwipeDirection.DownLeft;
            else if (angle >= 247.5f && angle < 292.5f)
                return SwipeDirection.Down;
            else
                return SwipeDirection.DownRight;
        }

        /// <summary>
        /// 设置滑动阈值
        /// </summary>
        public void SetSwipeThreshold(float threshold)
        {
            swipeThreshold = threshold;
        }

        /// <summary>
        /// 设置点击时间阈值
        /// </summary>
        public void SetTapTimeThreshold(float threshold)
        {
            tapTimeThreshold = threshold;
        }

        /// <summary>
        /// 启用/禁用手势识别
        /// </summary>
        public void EnableGestureRecognition(bool enabled)
        {
            enableGestureRecognition = enabled;

            if (!enabled)
            {
                // 清理所有活动触摸
                activeTouches.Clear();
                swipePoints.Clear();
            }
        }

        /// <summary>
        /// 设置调试可视化
        /// </summary>
        public void SetDebugVisualization(bool enabled)
        {
            enableDebugVisualization = enabled;
        }

        /// <summary>
        /// 获取当前活动触摸数量
        /// </summary>
        public int GetActiveTouchCount()
        {
            return activeTouches.Count;
        }

        /// <summary>
        /// 获取触摸信息
        /// </summary>
        public bool GetTouchInfo(int touchId, out Vector2 startPos, out Vector2 currentPos, out float duration)
        {
            if (activeTouches.ContainsKey(touchId))
            {
                var touchInfo = activeTouches[touchId];
                startPos = touchInfo.startPosition;
                currentPos = touchInfo.currentPosition;
                duration = Time.time - touchInfo.startTime;
                return true;
            }

            startPos = Vector2.zero;
            currentPos = Vector2.zero;
            duration = 0f;
            return false;
        }

        private void OnGUI()
        {
            if (!enableDebugVisualization) return;

            // 显示调试信息
            GUILayout.BeginArea(new Rect(10, 300, 300, 200));
            GUILayout.Label("=== 手势识别调试 ===");
            GUILayout.Label($"活动触摸数: {activeTouches.Count}");
            GUILayout.Label($"滑动阈值: {swipeThreshold}");
            GUILayout.Label($"点击时间阈值: {tapTimeThreshold}");

            foreach (var kvp in activeTouches)
            {
                var touchInfo = kvp.Value;
                float duration = Time.time - touchInfo.startTime;
                GUILayout.Label($"触摸 {kvp.Key}: {touchInfo.currentPosition}, 时长: {duration:F2}s");
            }

            GUILayout.EndArea();
        }
    }
}
