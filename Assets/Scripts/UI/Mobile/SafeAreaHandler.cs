using UnityEngine;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// Safe Area 处理器
    /// 处理移动设备的安全区域适配，避免刘海屏、圆角等遮挡UI
    /// </summary>
    public class SafeAreaHandler : MonoBehaviour
    {
        [Header("Safe Area 设置")]
        [SerializeField] private RectTransform safeAreaRoot;
        [SerializeField] public bool enableSafeArea = true;
        [SerializeField] public bool enableDebugVisualization = false;

        [Header("边距设置")]
        [SerializeField] private float additionalTopMargin = 0f;
        [SerializeField] private float additionalBottomMargin = 0f;
        [SerializeField] private float additionalLeftMargin = 0f;
        [SerializeField] private float additionalRightMargin = 0f;

        [Header("适配选项")]
        [SerializeField] public bool adaptToNotch = true;
        [SerializeField] public bool adaptToHomeIndicator = true;
        [SerializeField] public bool adaptToRoundedCorners = true;

        // 私有变量
        private Rect lastSafeArea;
        private Vector2Int lastScreenSize;
        private ScreenOrientation lastOrientation;

        // 调试可视化
        private GameObject debugVisualization;

        private void Start()
        {
            if (safeAreaRoot == null)
            {
                safeAreaRoot = GetComponent<RectTransform>();
            }

            ApplySafeArea();

            if (enableDebugVisualization)
            {
                CreateDebugVisualization();
            }
        }

        private void Update()
        {
            // 检查Safe Area是否发生变化
            if (HasSafeAreaChanged())
            {
                ApplySafeArea();
            }
        }

        /// <summary>
        /// 检查Safe Area是否发生变化
        /// </summary>
        private bool HasSafeAreaChanged()
        {
            return Screen.safeArea != lastSafeArea ||
                   new Vector2Int(Screen.width, Screen.height) != lastScreenSize ||
                   Screen.orientation != lastOrientation;
        }

        /// <summary>
        /// 应用Safe Area设置
        /// </summary>
        public void ApplySafeArea()
        {
            if (!enableSafeArea || safeAreaRoot == null) return;

            var safeArea = Screen.safeArea;
            var screenSize = new Vector2(Screen.width, Screen.height);

            // 记录当前状态
            lastSafeArea = safeArea;
            lastScreenSize = new Vector2Int(Screen.width, Screen.height);
            lastOrientation = Screen.orientation;

            // 计算Safe Area的相对位置和大小
            Vector2 anchorMin = safeArea.position;
            Vector2 anchorMax = safeArea.position + safeArea.size;

            // 转换为相对坐标 (0-1)
            anchorMin.x /= screenSize.x;
            anchorMin.y /= screenSize.y;
            anchorMax.x /= screenSize.x;
            anchorMax.y /= screenSize.y;

            // 应用额外边距
            ApplyAdditionalMargins(ref anchorMin, ref anchorMax);

            // 设置RectTransform
            safeAreaRoot.anchorMin = anchorMin;
            safeAreaRoot.anchorMax = anchorMax;
            safeAreaRoot.offsetMin = Vector2.zero;
            safeAreaRoot.offsetMax = Vector2.zero;

            // 更新调试可视化
            if (enableDebugVisualization)
            {
                UpdateDebugVisualization();
            }

            Debug.Log($"Safe Area 已应用: {safeArea} (屏幕: {screenSize})");
        }

        /// <summary>
        /// 应用额外边距
        /// </summary>
        private void ApplyAdditionalMargins(ref Vector2 anchorMin, ref Vector2 anchorMax)
        {
            var screenSize = new Vector2(Screen.width, Screen.height);

            // 转换像素边距为相对坐标
            float leftMargin = additionalLeftMargin / screenSize.x;
            float rightMargin = additionalRightMargin / screenSize.x;
            float bottomMargin = additionalBottomMargin / screenSize.y;
            float topMargin = additionalTopMargin / screenSize.y;

            // 应用边距
            anchorMin.x += leftMargin;
            anchorMin.y += bottomMargin;
            anchorMax.x -= rightMargin;
            anchorMax.y -= topMargin;

            // 确保边距不会导致负值
            anchorMin.x = Mathf.Max(0, anchorMin.x);
            anchorMin.y = Mathf.Max(0, anchorMin.y);
            anchorMax.x = Mathf.Min(1, anchorMax.x);
            anchorMax.y = Mathf.Min(1, anchorMax.y);
        }

        /// <summary>
        /// 创建调试可视化
        /// </summary>
        private void CreateDebugVisualization()
        {
            if (debugVisualization != null) return;

            debugVisualization = new GameObject("Safe Area Debug");
            debugVisualization.transform.SetParent(transform, false);

            var rectTransform = debugVisualization.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;

            var image = debugVisualization.AddComponent<UnityEngine.UI.Image>();
            image.color = new Color(1, 0, 0, 0.1f); // 半透明红色
            image.raycastTarget = false;

            // 添加边框
            var outline = debugVisualization.AddComponent<UnityEngine.UI.Outline>();
            outline.effectColor = Color.red;
            outline.effectDistance = new Vector2(2, 2);
        }

        /// <summary>
        /// 更新调试可视化
        /// </summary>
        private void UpdateDebugVisualization()
        {
            if (debugVisualization == null) return;

            var rectTransform = debugVisualization.GetComponent<RectTransform>();
            rectTransform.anchorMin = safeAreaRoot.anchorMin;
            rectTransform.anchorMax = safeAreaRoot.anchorMax;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
        }

        /// <summary>
        /// 设置Safe Area根节点
        /// </summary>
        public void SetSafeAreaRoot(RectTransform root)
        {
            safeAreaRoot = root;
            ApplySafeArea();
        }

        /// <summary>
        /// 启用/禁用Safe Area适配
        /// </summary>
        public void SetSafeAreaEnabled(bool enabled)
        {
            enableSafeArea = enabled;

            if (enabled)
            {
                ApplySafeArea();
            }
            else
            {
                // 重置为全屏
                if (safeAreaRoot != null)
                {
                    safeAreaRoot.anchorMin = Vector2.zero;
                    safeAreaRoot.anchorMax = Vector2.one;
                    safeAreaRoot.offsetMin = Vector2.zero;
                    safeAreaRoot.offsetMax = Vector2.zero;
                }
            }
        }

        /// <summary>
        /// 设置额外边距
        /// </summary>
        public void SetAdditionalMargins(float top, float bottom, float left, float right)
        {
            additionalTopMargin = top;
            additionalBottomMargin = bottom;
            additionalLeftMargin = left;
            additionalRightMargin = right;

            ApplySafeArea();
        }

        /// <summary>
        /// 获取当前Safe Area信息
        /// </summary>
        public SafeAreaInfo GetSafeAreaInfo()
        {
            var safeArea = Screen.safeArea;
            var screenSize = new Vector2(Screen.width, Screen.height);

            return new SafeAreaInfo
            {
                safeArea = safeArea,
                screenSize = screenSize,
                topInset = screenSize.y - (safeArea.y + safeArea.height),
                bottomInset = safeArea.y,
                leftInset = safeArea.x,
                rightInset = screenSize.x - (safeArea.x + safeArea.width),
                hasNotch = HasNotch(),
                hasHomeIndicator = HasHomeIndicator()
            };
        }

        /// <summary>
        /// 检查是否有刘海屏
        /// </summary>
        private bool HasNotch()
        {
            var safeArea = Screen.safeArea;
            var screenSize = new Vector2(Screen.width, Screen.height);

            // 简单检测：如果顶部安全区域小于屏幕高度，可能有刘海
            return safeArea.y + safeArea.height < screenSize.y - 10;
        }

        /// <summary>
        /// 检查是否有Home指示器
        /// </summary>
        private bool HasHomeIndicator()
        {
            var safeArea = Screen.safeArea;

            // 简单检测：如果底部安全区域不从0开始，可能有Home指示器
            return safeArea.y > 10;
        }

        /// <summary>
        /// 启用/禁用调试可视化
        /// </summary>
        public void SetDebugVisualizationEnabled(bool enabled)
        {
            enableDebugVisualization = enabled;

            if (enabled && debugVisualization == null)
            {
                CreateDebugVisualization();
            }
            else if (!enabled && debugVisualization != null)
            {
                DestroyImmediate(debugVisualization);
                debugVisualization = null;
            }
        }

        private void OnValidate()
        {
            // 在编辑器中实时预览效果
            if (Application.isPlaying)
            {
                ApplySafeArea();
            }
        }

        /// <summary>
        /// 屏幕方向变化回调
        /// </summary>
        public void OnOrientationChanged(bool isLandscape)
        {
            // 屏幕方向变化时重新应用安全区域
            ApplySafeArea();

            if (enableDebugVisualization)
            {
                Debug.Log($"SafeArea 适配屏幕方向: {(isLandscape ? "横屏" : "竖屏")}");
            }
        }
    }

    /// <summary>
    /// Safe Area 信息结构
    /// </summary>
    [System.Serializable]
    public struct SafeAreaInfo
    {
        public Rect safeArea;
        public Vector2 screenSize;
        public float topInset;
        public float bottomInset;
        public float leftInset;
        public float rightInset;
        public bool hasNotch;
        public bool hasHomeIndicator;

        public override string ToString()
        {
            return $"SafeArea: {safeArea}, Screen: {screenSize}, " +
                   $"Insets: T{topInset} B{bottomInset} L{leftInset} R{rightInset}, " +
                   $"Notch: {hasNotch}, HomeIndicator: {hasHomeIndicator}";
        }
    }
}
