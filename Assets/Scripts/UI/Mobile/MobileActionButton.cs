using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 移动端动作按钮
    /// 处理触摸输入并模拟键盘按键
    /// </summary>
    public class MobileActionButton : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler
    {
        [Header("按钮设置")]
        [SerializeField] private KeyCode keyCode = KeyCode.None;
        [SerializeField] private bool enableHapticFeedback = true;
        [SerializeField] private bool enableVisualEffects = true;
        [SerializeField] private bool enableSoundEffects = true;

        [Header("视觉效果")]
        [SerializeField] private float pressedScale = 0.9f;
        [SerializeField] private float animationDuration = 0.1f;
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color pressedColor = Color.gray;
        [SerializeField] private Color disabledColor = Color.gray;

        [Header("触觉反馈")]
        [SerializeField] private MobileControlsManager.HapticFeedbackType hapticType = MobileControlsManager.HapticFeedbackType.Light;

        // 组件引用
        private Button button;
        private Image buttonImage;
        private RectTransform rectTransform;
        private HapticFeedbackController hapticController;

        // 状态变量
        private bool isPressed = false;
        private bool isEnabled = true;
        private Vector3 originalScale;
        private Color originalColor;

        // 动画相关
        private Coroutine scaleAnimation;
        private Coroutine colorAnimation;

        // 输入模拟
        private bool simulateKeyInput = true;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            SetupButton();

            // 获取触觉反馈控制器 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            hapticController = FindFirstObjectByType<HapticFeedbackController>();
        }

        private void Update()
        {
            // 模拟键盘输入（用于测试）
            if (simulateKeyInput && keyCode != KeyCode.None)
            {
                if (Input.GetKeyDown(keyCode))
                {
                    SimulatePress();
                }
                else if (Input.GetKeyUp(keyCode))
                {
                    SimulateRelease();
                }
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            button = GetComponent<Button>();
            if (button == null)
            {
                button = gameObject.AddComponent<Button>();
            }

            buttonImage = GetComponent<Image>();
            if (buttonImage == null)
            {
                buttonImage = gameObject.AddComponent<Image>();
            }

            rectTransform = GetComponent<RectTransform>();
        }

        /// <summary>
        /// 设置按钮
        /// </summary>
        private void SetupButton()
        {
            // 保存原始值
            originalScale = rectTransform.localScale;
            originalColor = buttonImage.color;

            // 设置按钮颜色
            if (normalColor != Color.white)
            {
                buttonImage.color = normalColor;
                originalColor = normalColor;
            }

            // 配置Button组件
            button.targetGraphic = buttonImage;
            button.transition = Selectable.Transition.None; // 我们自己处理视觉效果

            // 添加点击事件
            button.onClick.AddListener(OnButtonClick);
        }

        /// <summary>
        /// 按钮点击事件
        /// </summary>
        private void OnButtonClick()
        {
            if (!isEnabled) return;

            // 触发按键事件
            TriggerKeyEvent();

            // 播放音效
            if (enableSoundEffects)
            {
                PlayClickSound();
            }

            Debug.Log($"Mobile Action Button clicked: {keyCode}");
        }

        /// <summary>
        /// 触发按键事件
        /// </summary>
        private void TriggerKeyEvent()
        {
            if (keyCode == KeyCode.None) return;

            // 这里可以发送自定义事件或调用游戏逻辑
            // 例如：GameEvents.OnKeyPressed?.Invoke(keyCode);

            // 或者直接调用对应的游戏功能
            HandleKeyAction(keyCode);
        }

        /// <summary>
        /// 处理按键动作
        /// </summary>
        private void HandleKeyAction(KeyCode key)
        {
            switch (key)
            {
                case KeyCode.Escape:
                    // 打开设置菜单
                    Debug.Log("打开设置菜单");
                    break;
                case KeyCode.Tab:
                    // 打开游戏菜单
                    Debug.Log("打开游戏菜单");
                    break;
                case KeyCode.E:
                    // 使用/交互
                    Debug.Log("使用/交互");
                    break;
                case KeyCode.Q:
                    // 杀死（鸭子专用）
                    Debug.Log("杀死动作");
                    break;
                case KeyCode.R:
                    // 报告尸体
                    Debug.Log("报告尸体");
                    break;
                case KeyCode.Space:
                    // 紧急会议
                    Debug.Log("紧急会议");
                    break;
                case KeyCode.V:
                    // 通风口（鸭子专用）
                    Debug.Log("使用通风口");
                    break;
                case KeyCode.M:
                    // 地图
                    Debug.Log("打开地图");
                    break;
                default:
                    Debug.Log($"未处理的按键: {key}");
                    break;
            }
        }

        /// <summary>
        /// 指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            if (!isEnabled) return;

            isPressed = true;

            // 触觉反馈
            if (enableHapticFeedback && hapticController != null)
            {
                hapticController.TriggerHaptic(hapticType);
            }

            // 视觉效果
            if (enableVisualEffects)
            {
                StartPressedAnimation();
            }
        }

        /// <summary>
        /// 指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            if (!isEnabled) return;

            isPressed = false;

            // 视觉效果
            if (enableVisualEffects)
            {
                StartReleasedAnimation();
            }
        }

        /// <summary>
        /// 指针进入事件
        /// </summary>
        public void OnPointerEnter(PointerEventData eventData)
        {
            if (!isEnabled) return;

            // 可以添加悬停效果
        }

        /// <summary>
        /// 指针退出事件
        /// </summary>
        public void OnPointerExit(PointerEventData eventData)
        {
            if (!isEnabled) return;

            // 如果指针离开时还在按下状态，取消按下
            if (isPressed)
            {
                isPressed = false;
                if (enableVisualEffects)
                {
                    StartReleasedAnimation();
                }
            }
        }

        /// <summary>
        /// 开始按下动画
        /// </summary>
        private void StartPressedAnimation()
        {
            if (scaleAnimation != null)
            {
                StopCoroutine(scaleAnimation);
            }
            if (colorAnimation != null)
            {
                StopCoroutine(colorAnimation);
            }

            scaleAnimation = StartCoroutine(AnimateScale(originalScale * pressedScale));
            colorAnimation = StartCoroutine(AnimateColor(pressedColor));
        }

        /// <summary>
        /// 开始释放动画
        /// </summary>
        private void StartReleasedAnimation()
        {
            if (scaleAnimation != null)
            {
                StopCoroutine(scaleAnimation);
            }
            if (colorAnimation != null)
            {
                StopCoroutine(colorAnimation);
            }

            scaleAnimation = StartCoroutine(AnimateScale(originalScale));
            colorAnimation = StartCoroutine(AnimateColor(originalColor));
        }

        /// <summary>
        /// 缩放动画
        /// </summary>
        private System.Collections.IEnumerator AnimateScale(Vector3 targetScale)
        {
            Vector3 startScale = rectTransform.localScale;
            float elapsed = 0f;

            while (elapsed < animationDuration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / animationDuration;

                rectTransform.localScale = Vector3.Lerp(startScale, targetScale, progress);

                yield return null;
            }

            rectTransform.localScale = targetScale;
        }

        /// <summary>
        /// 颜色动画
        /// </summary>
        private System.Collections.IEnumerator AnimateColor(Color targetColor)
        {
            Color startColor = buttonImage.color;
            float elapsed = 0f;

            while (elapsed < animationDuration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / animationDuration;

                buttonImage.color = Color.Lerp(startColor, targetColor, progress);

                yield return null;
            }

            buttonImage.color = targetColor;
        }

        /// <summary>
        /// 模拟按下
        /// </summary>
        public void SimulatePress()
        {
            OnPointerDown(null);
        }

        /// <summary>
        /// 模拟释放
        /// </summary>
        public void SimulateRelease()
        {
            OnPointerUp(null);
        }

        /// <summary>
        /// 播放点击音效
        /// </summary>
        private void PlayClickSound()
        {
            // 这里可以播放音效
            // AudioManager.Instance?.PlaySFX("button_click");
        }

        /// <summary>
        /// 设置按键码
        /// </summary>
        public void SetKeyCode(KeyCode key)
        {
            keyCode = key;
        }

        /// <summary>
        /// 设置触觉反馈
        /// </summary>
        public void SetHapticFeedback(bool enabled)
        {
            enableHapticFeedback = enabled;
        }

        /// <summary>
        /// 设置视觉效果
        /// </summary>
        public void SetVisualEffectsEnabled(bool enabled)
        {
            enableVisualEffects = enabled;
        }

        /// <summary>
        /// 设置按钮启用状态
        /// </summary>
        public void SetEnabled(bool enabled)
        {
            isEnabled = enabled;
            button.interactable = enabled;

            if (enabled)
            {
                buttonImage.color = originalColor;
            }
            else
            {
                buttonImage.color = disabledColor;
            }
        }

        /// <summary>
        /// 设置按钮可见性
        /// </summary>
        public void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        public void UpdateButton()
        {
            // 这里可以根据游戏状态更新按钮的可用性
            // 例如：根据玩家角色显示/隐藏特定按钮
        }

        /// <summary>
        /// 获取按键码
        /// </summary>
        public KeyCode GetKeyCode()
        {
            return keyCode;
        }

        /// <summary>
        /// 获取按钮是否被按下
        /// </summary>
        public bool IsPressed()
        {
            return isPressed;
        }

        /// <summary>
        /// 获取按钮是否启用
        /// </summary>
        public bool IsEnabled()
        {
            return isEnabled;
        }
    }
}
