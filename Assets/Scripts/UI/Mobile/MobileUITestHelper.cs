using UnityEngine;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// Mobile UI 测试辅助工具
    /// 用于验证所有组件是否正常工作
    /// </summary>
    public class MobileUITestHelper : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool enableAutoTest = false;
        [SerializeField] private float testInterval = 5f;

        private float lastTestTime = 0f;

        private void Update()
        {
            if (enableAutoTest && Time.time - lastTestTime > testInterval)
            {
                RunComponentTest();
                lastTestTime = Time.time;
            }

            // 手动测试快捷键
            if (Input.GetKeyDown(KeyCode.F2))
            {
                RunComponentTest();
            }

            if (Input.GetKeyDown(KeyCode.F3))
            {
                TestNotifications();
            }

            if (Input.GetKeyDown(KeyCode.F4))
            {
                TestAudioManager();
            }
        }

        /// <summary>
        /// 运行组件测试
        /// </summary>
        [ContextMenu("Run Component Test")]
        public void RunComponentTest()
        {
            Debug.Log("=== Mobile UI Component Test ===");

            // 测试 MobileUIRoot - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var mobileUIRoot = FindFirstObjectByType<MobileUIRoot>();
            if (mobileUIRoot != null)
            {
                Debug.Log("✅ MobileUIRoot - OK");
                mobileUIRoot.ForceUpdateLayout();
            }
            else
            {
                Debug.LogWarning("❌ MobileUIRoot - Missing");
            }

            // 测试 SafeAreaHandler - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var safeAreaHandler = FindFirstObjectByType<SafeAreaHandler>();
            if (safeAreaHandler != null)
            {
                Debug.Log("✅ SafeAreaHandler - OK");
                var safeAreaInfo = safeAreaHandler.GetSafeAreaInfo();
                Debug.Log($"Safe Area Info: {safeAreaInfo}");
            }
            else
            {
                Debug.LogWarning("❌ SafeAreaHandler - Missing");
            }

            // 测试 MobileControlsManager - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var controlsManager = FindFirstObjectByType<MobileControlsManager>();
            if (controlsManager != null)
            {
                Debug.Log("✅ MobileControlsManager - OK");
            }
            else
            {
                Debug.LogWarning("❌ MobileControlsManager - Missing");
            }

            // 测试 SimpleVirtualJoystick - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var virtualJoystick = FindFirstObjectByType<SimpleVirtualJoystick>();
            if (virtualJoystick != null)
            {
                Debug.Log("✅ SimpleVirtualJoystick - OK");
                var inputVector = virtualJoystick.GetInputVector();
                Debug.Log($"Joystick Input: {inputVector}");
            }
            else
            {
                Debug.LogWarning("❌ SimpleVirtualJoystick - Missing");
            }

            // 测试 MobileHUDManager - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var hudManager = FindFirstObjectByType<MobileHUDManager>();
            if (hudManager != null)
            {
                Debug.Log("✅ MobileHUDManager - OK");
            }
            else
            {
                Debug.LogWarning("❌ MobileHUDManager - Missing");
            }

            // 测试 UI管理器组件
            TestUIManagers();

            Debug.Log("=== Component Test Complete ===");
        }

        /// <summary>
        /// 测试UI管理器组件
        /// </summary>
        private void TestUIManagers()
        {
            // 测试 UIManager - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var uiManager = FindFirstObjectByType<GooseDuckKill.UI.UIManager>();
            if (uiManager != null)
            {
                Debug.Log("✅ UIManager - OK");
                Debug.Log($"UI System Status: {uiManager.GetUISystemStatus()}");
            }
            else
            {
                Debug.LogWarning("❌ UIManager - Missing");
            }

            // 测试 SettingsManager - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var settingsManager = FindFirstObjectByType<GooseDuckKill.UI.SettingsManager>();
            if (settingsManager != null)
            {
                Debug.Log("✅ SettingsManager - OK");
                var settings = settingsManager.GetCurrentSettings();
                Debug.Log($"Current Settings: Master={settings.masterVolume}, Music={settings.musicVolume}");
            }
            else
            {
                Debug.LogWarning("❌ SettingsManager - Missing");
            }

            // 测试 MobileUIAdapter - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var mobileUIAdapter = FindFirstObjectByType<GooseDuckKill.UI.MobileUIAdapter>();
            if (mobileUIAdapter != null)
            {
                Debug.Log("✅ MobileUIAdapter - OK");
                Debug.Log($"Mobile UI Active: {mobileUIAdapter.IsMobileUIActive()}");
                Debug.Log($"Platform Info: {mobileUIAdapter.GetPlatformInfo()}");
            }
            else
            {
                Debug.LogWarning("❌ MobileUIAdapter - Missing");
            }

            // 测试 ConnectionUI - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var connectionUI = FindFirstObjectByType<GooseDuckKill.UI.ConnectionUI>();
            if (connectionUI != null)
            {
                Debug.Log("✅ ConnectionUI - OK");
            }
            else
            {
                Debug.LogWarning("❌ ConnectionUI - Missing");
            }
        }

        /// <summary>
        /// 测试通知系统
        /// </summary>
        [ContextMenu("Test Notifications")]
        public void TestNotifications()
        {
            var notificationManager = FindFirstObjectByType<NotificationManager>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (notificationManager != null)
            {
                notificationManager.ShowNotification("测试通知 - 信息", 3f, NotificationManager.NotificationType.Info);

                // 延迟显示其他类型的通知
                StartCoroutine(ShowDelayedNotifications(notificationManager));
            }
            else
            {
                Debug.LogWarning("NotificationManager not found");
            }
        }

        /// <summary>
        /// 延迟显示通知
        /// </summary>
        private System.Collections.IEnumerator ShowDelayedNotifications(NotificationManager manager)
        {
            yield return new WaitForSeconds(1f);
            manager.ShowNotification("测试通知 - 警告", 3f, NotificationManager.NotificationType.Warning);

            yield return new WaitForSeconds(1f);
            manager.ShowNotification("测试通知 - 错误", 3f, NotificationManager.NotificationType.Error);

            yield return new WaitForSeconds(1f);
            manager.ShowNotification("测试通知 - 成功", 3f, NotificationManager.NotificationType.Success);
        }

        /// <summary>
        /// 测试音频管理器
        /// </summary>
        [ContextMenu("Test Audio Manager")]
        public void TestAudioManager()
        {
            var simpleAudioManager = GooseDuckKill.UI.SimpleAudioManager.Instance;
            if (simpleAudioManager != null)
            {
                Debug.Log("✅ SimpleAudioManager - OK");

                // 测试UI音效
                simpleAudioManager.PlayButtonClick();

                // 测试音量设置
                var volumes = simpleAudioManager.GetVolumeSettings();
                Debug.Log($"Volume Settings: Master={volumes.master}, Music={volumes.music}, SFX={volumes.sfx}");

                // 测试背景音乐状态
                Debug.Log($"Playing Music: {simpleAudioManager.IsPlayingMusic()}");
                Debug.Log($"Current Music: {simpleAudioManager.GetCurrentMusic()}");
            }
            else
            {
                Debug.LogWarning("❌ SimpleAudioManager - Missing");

                // 备用测试：查找AudioManager GameObject
                var audioManagerGO = GameObject.Find("Audio Manager");
                if (audioManagerGO != null)
                {
                    Debug.Log("✅ AudioManager GameObject - OK");
                    var audioSources = audioManagerGO.GetComponentsInChildren<AudioSource>();
                    Debug.Log($"Found {audioSources.Length} AudioSource components");
                }
            }
        }

        /// <summary>
        /// 测试屏幕方向变化
        /// </summary>
        [ContextMenu("Test Orientation Change")]
        public void TestOrientationChange()
        {
            var mobileUIRoot = FindFirstObjectByType<MobileUIRoot>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (mobileUIRoot != null)
            {
                bool isLandscape = Screen.width > Screen.height;
                mobileUIRoot.OnOrientationChanged(!isLandscape); // 模拟方向变化
                Debug.Log($"Simulated orientation change to: {(!isLandscape ? "Landscape" : "Portrait")}");
            }
        }

        /// <summary>
        /// 测试UI模式切换
        /// </summary>
        [ContextMenu("Test UI Mode Toggle")]
        public void TestUIModeToggle()
        {
            var mobileUIAdapter = FindFirstObjectByType<GooseDuckKill.UI.MobileUIAdapter>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.ToggleMobileUI();
                Debug.Log($"UI Mode toggled. Mobile UI Active: {mobileUIAdapter.IsMobileUIActive()}");
            }
        }

        /// <summary>
        /// 显示系统信息
        /// </summary>
        [ContextMenu("Show System Info")]
        public void ShowSystemInfo()
        {
            Debug.Log("=== System Information ===");
            Debug.Log($"Platform: {Application.platform}");
            Debug.Log($"Is Mobile Platform: {Application.isMobilePlatform}");
            Debug.Log($"Screen Size: {Screen.width}x{Screen.height}");
            Debug.Log($"Screen DPI: {Screen.dpi}");
            Debug.Log($"Safe Area: {Screen.safeArea}");
            Debug.Log($"Device Model: {SystemInfo.deviceModel}");
            Debug.Log($"Operating System: {SystemInfo.operatingSystem}");
            Debug.Log($"Battery Level: {SystemInfo.batteryLevel * 100:F0}%");
            Debug.Log($"Battery Status: {SystemInfo.batteryStatus}");
            Debug.Log("========================");
        }

        private void OnGUI()
        {
            if (!enableAutoTest) return;

            // 显示测试信息
            GUILayout.BeginArea(new Rect(10, Screen.height - 150, 300, 140));
            GUILayout.Label("=== Mobile UI Test Helper ===");
            GUILayout.Label("F2: Run Component Test");
            GUILayout.Label("F3: Test Notifications");
            GUILayout.Label("F4: Test Audio Manager");
            GUILayout.Label($"Auto Test: {(enableAutoTest ? "ON" : "OFF")}");
            GUILayout.EndArea();
        }
    }
}
