using UnityEngine;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// Mobile UI 自动设置脚本
    /// 简化版本，用于快速测试和设置
    /// </summary>
    public class MobileUIAutoSetup : MonoBehaviour
    {
        [Header("自动设置选项")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool onlyOnMobilePlatform = false; // 设为false以便在编辑器中测试

        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupMobileUI();
            }
        }

        /// <summary>
        /// 设置Mobile UI
        /// </summary>
        public void SetupMobileUI()
        {
            // 平台检查
            if (onlyOnMobilePlatform && !Application.isMobilePlatform)
            {
                Debug.Log("非移动平台，跳过Mobile UI设置");
                return;
            }

            var canvas = FindFirstObjectByType<Canvas>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (canvas == null)
            {
                Debug.LogError("未找到Canvas，无法创建Mobile UI");
                return;
            }

            // 检查是否已存在
            if (canvas.transform.Find("Mobile UI Root") != null)
            {
                Debug.Log("Mobile UI Root 已存在，跳过创建");
                return;
            }

            // 使用工厂类创建Mobile UI Root
            try
            {
                var config = MobileUIConfig.MainMenu;
                var mobileUIRoot = MobileUIRootFactory.CreateMobileUIRoot(canvas, config);

                if (mobileUIRoot != null)
                {
                    Debug.Log("Mobile UI Root 自动设置完成");
                }
                else
                {
                    Debug.LogError("Mobile UI Root 创建失败");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Mobile UI Root 创建出错: {e.Message}");
            }
        }

        /// <summary>
        /// 移除Mobile UI
        /// </summary>
        public void RemoveMobileUI()
        {
            var canvas = FindFirstObjectByType<Canvas>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (canvas != null)
            {
                var mobileUIRoot = canvas.transform.Find("Mobile UI Root");
                if (mobileUIRoot != null)
                {
                    DestroyImmediate(mobileUIRoot.gameObject);
                    Debug.Log("Mobile UI Root 已移除");
                }
                else
                {
                    Debug.Log("未找到Mobile UI Root");
                }
            }
        }

        /// <summary>
        /// 编辑器快捷菜单
        /// </summary>
        [ContextMenu("Setup Mobile UI Now")]
        private void SetupMobileUIContextMenu()
        {
            SetupMobileUI();
        }

        [ContextMenu("Remove Mobile UI")]
        private void RemoveMobileUIContextMenu()
        {
            RemoveMobileUI();
        }

        [ContextMenu("Test Mobile UI")]
        private void TestMobileUIContextMenu()
        {
            // 先移除再创建，用于测试
            RemoveMobileUI();
            SetupMobileUI();
        }
    }
}
