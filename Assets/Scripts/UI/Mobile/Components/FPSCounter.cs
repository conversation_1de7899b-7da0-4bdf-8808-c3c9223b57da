using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// FPS计数器
    /// 显示当前帧率
    /// </summary>
    public class FPSCounter : MonoBehaviour
    {
        [Header("显示设置")]
        [SerializeField] private Text fpsText;
        [SerializeField] private bool showAverage = false;
        [SerializeField] private bool showMinMax = false;
        
        [Header("更新设置")]
        [SerializeField] private float updateInterval = 0.5f;
        
        [Header("颜色设置")]
        [SerializeField] private Color goodFPSColor = Color.green;
        [SerializeField] private Color averageFPSColor = Color.yellow;
        [SerializeField] private Color poorFPSColor = Color.red;
        [SerializeField] private float goodFPSThreshold = 50f;
        [SerializeField] private float averageFPSThreshold = 30f;
        
        // FPS计算
        private float deltaTime = 0f;
        private float lastUpdateTime = 0f;
        private float frameCount = 0f;
        private float totalTime = 0f;
        
        // 统计数据
        private float currentFPS = 0f;
        private float averageFPS = 0f;
        private float minFPS = float.MaxValue;
        private float maxFPS = 0f;
        
        private void Awake()
        {
            if (fpsText == null)
                fpsText = GetComponent<Text>();
        }
        
        private void Update()
        {
            // 计算帧时间
            deltaTime += (Time.unscaledDeltaTime - deltaTime) * 0.1f;
            frameCount++;
            totalTime += Time.unscaledDeltaTime;
            
            // 更新显示
            if (Time.unscaledTime - lastUpdateTime >= updateInterval)
            {
                UpdateFPS();
                lastUpdateTime = Time.unscaledTime;
            }
        }
        
        /// <summary>
        /// 更新FPS显示
        /// </summary>
        public void UpdateFPS()
        {
            // 计算当前FPS
            currentFPS = 1f / deltaTime;
            
            // 计算平均FPS
            if (totalTime > 0)
            {
                averageFPS = frameCount / totalTime;
            }
            
            // 更新最小最大值
            if (currentFPS < minFPS && currentFPS > 0)
                minFPS = currentFPS;
            if (currentFPS > maxFPS)
                maxFPS = currentFPS;
            
            // 更新显示
            UpdateDisplay();
        }
        
        /// <summary>
        /// 更新显示内容
        /// </summary>
        private void UpdateDisplay()
        {
            if (fpsText == null) return;
            
            string displayText = $"{currentFPS:F0} FPS";
            
            if (showAverage)
            {
                displayText += $"\nAvg: {averageFPS:F0}";
            }
            
            if (showMinMax)
            {
                displayText += $"\nMin: {minFPS:F0} Max: {maxFPS:F0}";
            }
            
            fpsText.text = displayText;
            
            // 根据FPS设置颜色
            Color fpsColor = GetFPSColor(currentFPS);
            fpsText.color = fpsColor;
        }
        
        /// <summary>
        /// 根据FPS获取颜色
        /// </summary>
        private Color GetFPSColor(float fps)
        {
            if (fps >= goodFPSThreshold)
                return goodFPSColor;
            else if (fps >= averageFPSThreshold)
                return averageFPSColor;
            else
                return poorFPSColor;
        }
        
        /// <summary>
        /// 重置统计数据
        /// </summary>
        public void ResetStats()
        {
            frameCount = 0f;
            totalTime = 0f;
            minFPS = float.MaxValue;
            maxFPS = 0f;
            averageFPS = 0f;
        }
        
        /// <summary>
        /// 获取当前FPS
        /// </summary>
        public float GetCurrentFPS()
        {
            return currentFPS;
        }
        
        /// <summary>
        /// 获取平均FPS
        /// </summary>
        public float GetAverageFPS()
        {
            return averageFPS;
        }
        
        /// <summary>
        /// 获取最小FPS
        /// </summary>
        public float GetMinFPS()
        {
            return minFPS == float.MaxValue ? 0f : minFPS;
        }
        
        /// <summary>
        /// 获取最大FPS
        /// </summary>
        public float GetMaxFPS()
        {
            return maxFPS;
        }
    }
}
