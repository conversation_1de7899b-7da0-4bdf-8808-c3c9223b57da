using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 通知管理器
    /// 管理移动端的通知显示
    /// </summary>
    public class NotificationManager : MonoBehaviour
    {
        [Header("通知设置")]
        [SerializeField] public int maxNotifications = 5;
        [SerializeField] public float notificationDuration = 3f;
        [SerializeField] private Vector2 notificationSize = new Vector2(300, 60);
        [SerializeField] private float notificationSpacing = 10f;

        [Header("动画设置")]
        [SerializeField] private float slideInDuration = 0.3f;
        [SerializeField] private float slideOutDuration = 0.2f;
        [SerializeField] private AnimationCurve slideInCurve = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1));
        [SerializeField] private AnimationCurve slideOutCurve = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1));

        [Header("样式设置")]
        [SerializeField] private Color backgroundColor = new Color(0, 0, 0, 0.8f);
        [SerializeField] private Color textColor = Color.white;
        [SerializeField] private Font textFont;
        [SerializeField] private int fontSize = 14;

        // 通知列表
        private List<NotificationItem> activeNotifications = new List<NotificationItem>();
        private Queue<NotificationData> pendingNotifications = new Queue<NotificationData>();

        // 通知数据结构
        private struct NotificationData
        {
            public string message;
            public float duration;
            public NotificationType type;
        }

        // 通知类型
        public enum NotificationType
        {
            Info,
            Warning,
            Error,
            Success
        }

        // 通知项
        private class NotificationItem
        {
            public GameObject gameObject;
            public RectTransform rectTransform;
            public Text textComponent;
            public Image backgroundImage;
            public float remainingTime;
            public bool isAnimating;
        }

        private void Update()
        {
            UpdateNotifications();
            ProcessPendingNotifications();
        }

        /// <summary>
        /// 显示通知
        /// </summary>
        public void ShowNotification(string message, float duration = -1f, NotificationType type = NotificationType.Info)
        {
            if (string.IsNullOrEmpty(message)) return;

            float actualDuration = duration > 0 ? duration : notificationDuration;

            var notificationData = new NotificationData
            {
                message = message,
                duration = actualDuration,
                type = type
            };

            // 如果当前通知数量已达上限，加入待处理队列
            if (activeNotifications.Count >= maxNotifications)
            {
                pendingNotifications.Enqueue(notificationData);
            }
            else
            {
                CreateNotification(notificationData);
            }
        }

        /// <summary>
        /// 创建通知
        /// </summary>
        private void CreateNotification(NotificationData data)
        {
            // 创建通知GameObject
            var notificationGO = new GameObject($"Notification_{activeNotifications.Count}");
            notificationGO.transform.SetParent(transform, false);

            // 设置RectTransform
            var rectTransform = notificationGO.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 1f);
            rectTransform.anchorMax = new Vector2(0.5f, 1f);
            rectTransform.sizeDelta = notificationSize;

            // 计算位置
            float yPosition = -(notificationSize.y + notificationSpacing) * activeNotifications.Count;
            rectTransform.anchoredPosition = new Vector2(0, yPosition);

            // 添加背景
            var backgroundImage = notificationGO.AddComponent<Image>();
            backgroundImage.color = GetNotificationColor(data.type);

            // 添加文本
            var textGO = new GameObject("Text");
            textGO.transform.SetParent(notificationGO.transform, false);

            var textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(10, 5);
            textRect.offsetMax = new Vector2(-10, -5);

            var textComponent = textGO.AddComponent<Text>();
            textComponent.text = data.message;
            textComponent.color = textColor;
            textComponent.font = textFont ?? Resources.GetBuiltinResource<Font>("Arial.ttf");
            textComponent.fontSize = fontSize;
            textComponent.alignment = TextAnchor.MiddleCenter;
            textComponent.horizontalOverflow = HorizontalWrapMode.Wrap;
            textComponent.verticalOverflow = VerticalWrapMode.Truncate;

            // 创建通知项
            var notificationItem = new NotificationItem
            {
                gameObject = notificationGO,
                rectTransform = rectTransform,
                textComponent = textComponent,
                backgroundImage = backgroundImage,
                remainingTime = data.duration,
                isAnimating = true
            };

            activeNotifications.Add(notificationItem);

            // 播放滑入动画
            StartCoroutine(SlideInAnimation(notificationItem));
        }

        /// <summary>
        /// 滑入动画
        /// </summary>
        private System.Collections.IEnumerator SlideInAnimation(NotificationItem item)
        {
            Vector2 startPos = item.rectTransform.anchoredPosition;
            Vector2 targetPos = startPos;
            startPos.x = notificationSize.x; // 从右侧滑入

            item.rectTransform.anchoredPosition = startPos;

            float elapsed = 0f;
            while (elapsed < slideInDuration)
            {
                elapsed += Time.deltaTime;
                float progress = slideInCurve.Evaluate(elapsed / slideInDuration);

                item.rectTransform.anchoredPosition = Vector2.Lerp(startPos, targetPos, progress);

                yield return null;
            }

            item.rectTransform.anchoredPosition = targetPos;
            item.isAnimating = false;
        }

        /// <summary>
        /// 滑出动画
        /// </summary>
        private System.Collections.IEnumerator SlideOutAnimation(NotificationItem item)
        {
            item.isAnimating = true;

            Vector2 startPos = item.rectTransform.anchoredPosition;
            Vector2 targetPos = startPos;
            targetPos.x = -notificationSize.x; // 向左侧滑出

            float elapsed = 0f;
            while (elapsed < slideOutDuration)
            {
                elapsed += Time.deltaTime;
                float progress = slideOutCurve.Evaluate(elapsed / slideOutDuration);

                item.rectTransform.anchoredPosition = Vector2.Lerp(startPos, targetPos, progress);

                // 淡出效果
                Color bgColor = item.backgroundImage.color;
                Color textColor = item.textComponent.color;
                bgColor.a = 1f - progress;
                textColor.a = 1f - progress;
                item.backgroundImage.color = bgColor;
                item.textComponent.color = textColor;

                yield return null;
            }

            // 销毁通知
            if (item.gameObject != null)
            {
                Destroy(item.gameObject);
            }
        }

        /// <summary>
        /// 更新通知
        /// </summary>
        public void UpdateNotifications()
        {
            for (int i = activeNotifications.Count - 1; i >= 0; i--)
            {
                var notification = activeNotifications[i];

                if (notification.isAnimating) continue;

                notification.remainingTime -= Time.deltaTime;

                if (notification.remainingTime <= 0)
                {
                    // 开始滑出动画
                    StartCoroutine(SlideOutAndRemove(notification, i));
                }
            }
        }

        /// <summary>
        /// 滑出并移除通知
        /// </summary>
        private System.Collections.IEnumerator SlideOutAndRemove(NotificationItem item, int index)
        {
            yield return StartCoroutine(SlideOutAnimation(item));

            // 从列表中移除
            if (index < activeNotifications.Count)
            {
                activeNotifications.RemoveAt(index);
            }

            // 重新排列剩余通知
            RearrangeNotifications();
        }

        /// <summary>
        /// 重新排列通知
        /// </summary>
        private void RearrangeNotifications()
        {
            for (int i = 0; i < activeNotifications.Count; i++)
            {
                var notification = activeNotifications[i];
                float targetY = -(notificationSize.y + notificationSpacing) * i;

                // 平滑移动到新位置
                StartCoroutine(MoveToPosition(notification, new Vector2(0, targetY)));
            }
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        private System.Collections.IEnumerator MoveToPosition(NotificationItem item, Vector2 targetPosition)
        {
            Vector2 startPosition = item.rectTransform.anchoredPosition;
            float duration = 0.3f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;

                item.rectTransform.anchoredPosition = Vector2.Lerp(startPosition, targetPosition, progress);

                yield return null;
            }

            item.rectTransform.anchoredPosition = targetPosition;
        }

        /// <summary>
        /// 处理待处理的通知
        /// </summary>
        private void ProcessPendingNotifications()
        {
            while (pendingNotifications.Count > 0 && activeNotifications.Count < maxNotifications)
            {
                var pendingNotification = pendingNotifications.Dequeue();
                CreateNotification(pendingNotification);
            }
        }

        /// <summary>
        /// 获取通知颜色
        /// </summary>
        private Color GetNotificationColor(NotificationType type)
        {
            switch (type)
            {
                case NotificationType.Info:
                    return new Color(0.2f, 0.6f, 1f, 0.9f); // 蓝色
                case NotificationType.Warning:
                    return new Color(1f, 0.8f, 0.2f, 0.9f); // 黄色
                case NotificationType.Error:
                    return new Color(1f, 0.3f, 0.3f, 0.9f); // 红色
                case NotificationType.Success:
                    return new Color(0.3f, 1f, 0.3f, 0.9f); // 绿色
                default:
                    return backgroundColor;
            }
        }

        /// <summary>
        /// 清除所有通知
        /// </summary>
        public void ClearAllNotifications()
        {
            foreach (var notification in activeNotifications)
            {
                if (notification.gameObject != null)
                {
                    Destroy(notification.gameObject);
                }
            }

            activeNotifications.Clear();
            pendingNotifications.Clear();
        }

        /// <summary>
        /// 获取活动通知数量
        /// </summary>
        public int GetActiveNotificationCount()
        {
            return activeNotifications.Count;
        }

        /// <summary>
        /// 获取待处理通知数量
        /// </summary>
        public int GetPendingNotificationCount()
        {
            return pendingNotifications.Count;
        }
    }
}
