using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 网络状态指示器
    /// 显示网络连接状态
    /// </summary>
    public class NetworkIndicator : MonoBehaviour
    {
        [Header("指示器设置")]
        [SerializeField] private Image statusImage;
        [SerializeField] private Text statusText;
        
        [Header("状态颜色")]
        [SerializeField] private Color connectedColor = Color.green;
        [SerializeField] private Color disconnectedColor = Color.red;
        [SerializeField] private Color poorConnectionColor = Color.yellow;
        
        [Header("更新设置")]
        [SerializeField] private float updateInterval = 1f;
        
        // 网络状态
        public enum NetworkStatus
        {
            Connected,
            Disconnected,
            PoorConnection
        }
        
        private NetworkStatus currentStatus = NetworkStatus.Disconnected;
        private float lastUpdateTime;
        
        private void Awake()
        {
            if (statusImage == null)
                statusImage = GetComponent<Image>();
            
            if (statusText == null)
                statusText = GetComponentInChildren<Text>();
        }
        
        private void Start()
        {
            UpdateStatus();
        }
        
        /// <summary>
        /// 更新网络状态
        /// </summary>
        public void UpdateStatus()
        {
            if (Time.time - lastUpdateTime < updateInterval) return;
            lastUpdateTime = Time.time;
            
            // 检查网络状态
            NetworkStatus newStatus = CheckNetworkStatus();
            
            if (newStatus != currentStatus)
            {
                currentStatus = newStatus;
                UpdateVisual();
            }
        }
        
        /// <summary>
        /// 检查网络状态
        /// </summary>
        private NetworkStatus CheckNetworkStatus()
        {
            // 检查网络可达性
            if (Application.internetReachability == NetworkReachability.NotReachable)
            {
                return NetworkStatus.Disconnected;
            }
            
            // 这里可以添加更复杂的网络质量检测
            // 例如ping测试、下载速度测试等
            
            return NetworkStatus.Connected;
        }
        
        /// <summary>
        /// 更新视觉显示
        /// </summary>
        private void UpdateVisual()
        {
            Color statusColor;
            string statusString;
            
            switch (currentStatus)
            {
                case NetworkStatus.Connected:
                    statusColor = connectedColor;
                    statusString = "●";
                    break;
                case NetworkStatus.PoorConnection:
                    statusColor = poorConnectionColor;
                    statusString = "◐";
                    break;
                case NetworkStatus.Disconnected:
                default:
                    statusColor = disconnectedColor;
                    statusString = "○";
                    break;
            }
            
            if (statusImage != null)
            {
                statusImage.color = statusColor;
            }
            
            if (statusText != null)
            {
                statusText.text = statusString;
                statusText.color = statusColor;
            }
        }
        
        /// <summary>
        /// 设置网络状态
        /// </summary>
        public void SetNetworkStatus(NetworkStatus status)
        {
            currentStatus = status;
            UpdateVisual();
        }
        
        /// <summary>
        /// 获取当前网络状态
        /// </summary>
        public NetworkStatus GetNetworkStatus()
        {
            return currentStatus;
        }
    }
}
