using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 电池指示器
    /// 显示设备电池状态
    /// </summary>
    public class BatteryIndicator : MonoBehaviour
    {
        [Header("显示组件")]
        [SerializeField] private Text batteryText;
        [SerializeField] private Image batteryIcon;
        [SerializeField] private Image batteryFill;
        
        [Header("颜色设置")]
        [SerializeField] private Color highBatteryColor = Color.green;
        [SerializeField] private Color mediumBatteryColor = Color.yellow;
        [SerializeField] private Color lowBatteryColor = Color.red;
        [SerializeField] private Color chargingColor = Color.cyan;
        
        [Header("阈值设置")]
        [SerializeField] private float lowBatteryThreshold = 0.2f;
        [SerializeField] private float mediumBatteryThreshold = 0.5f;
        
        [Header("更新设置")]
        [SerializeField] private float updateInterval = 5f; // 电池状态更新较慢
        
        // 电池状态
        private float batteryLevel = 1f;
        private BatteryStatus batteryStatus = BatteryStatus.Unknown;
        private float lastUpdateTime = 0f;
        
        private void Awake()
        {
            if (batteryText == null)
                batteryText = GetComponentInChildren<Text>();
            
            if (batteryIcon == null)
                batteryIcon = GetComponent<Image>();
        }
        
        private void Start()
        {
            UpdateBattery();
        }
        
        /// <summary>
        /// 更新电池状态
        /// </summary>
        public void UpdateBattery()
        {
            if (Time.time - lastUpdateTime < updateInterval) return;
            lastUpdateTime = Time.time;
            
            // 获取电池信息
            batteryLevel = SystemInfo.batteryLevel;
            batteryStatus = SystemInfo.batteryStatus;
            
            // 更新显示
            UpdateDisplay();
        }
        
        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            // 更新文本
            if (batteryText != null)
            {
                string batteryText = GetBatteryText();
                this.batteryText.text = batteryText;
                this.batteryText.color = GetBatteryColor();
            }
            
            // 更新图标
            if (batteryIcon != null)
            {
                batteryIcon.color = GetBatteryColor();
            }
            
            // 更新电池填充
            if (batteryFill != null)
            {
                batteryFill.fillAmount = batteryLevel;
                batteryFill.color = GetBatteryColor();
            }
        }
        
        /// <summary>
        /// 获取电池文本
        /// </summary>
        private string GetBatteryText()
        {
            if (batteryLevel < 0)
            {
                return "N/A";
            }
            
            string percentage = $"{(batteryLevel * 100):F0}%";
            
            switch (batteryStatus)
            {
                case BatteryStatus.Charging:
                    return $"{percentage} ⚡";
                case BatteryStatus.Full:
                    return $"{percentage} ✓";
                case BatteryStatus.NotCharging:
                case BatteryStatus.Discharging:
                    return percentage;
                default:
                    return percentage;
            }
        }
        
        /// <summary>
        /// 获取电池颜色
        /// </summary>
        private Color GetBatteryColor()
        {
            // 充电时使用特殊颜色
            if (batteryStatus == BatteryStatus.Charging)
            {
                return chargingColor;
            }
            
            // 根据电量设置颜色
            if (batteryLevel >= mediumBatteryThreshold)
            {
                return highBatteryColor;
            }
            else if (batteryLevel >= lowBatteryThreshold)
            {
                return mediumBatteryColor;
            }
            else
            {
                return lowBatteryColor;
            }
        }
        
        /// <summary>
        /// 获取当前电池电量
        /// </summary>
        public float GetBatteryLevel()
        {
            return batteryLevel;
        }
        
        /// <summary>
        /// 获取电池状态
        /// </summary>
        public BatteryStatus GetBatteryStatus()
        {
            return batteryStatus;
        }
        
        /// <summary>
        /// 是否为低电量
        /// </summary>
        public bool IsLowBattery()
        {
            return batteryLevel > 0 && batteryLevel < lowBatteryThreshold;
        }
        
        /// <summary>
        /// 是否正在充电
        /// </summary>
        public bool IsCharging()
        {
            return batteryStatus == BatteryStatus.Charging;
        }
    }
}
