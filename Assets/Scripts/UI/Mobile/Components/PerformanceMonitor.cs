using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 性能监控器
    /// 监控设备性能指标
    /// </summary>
    public class PerformanceMonitor : MonoBehaviour
    {
        [Header("显示组件")]
        [SerializeField] private Text performanceText;

        [Header("监控设置")]
        [SerializeField] private bool showCPU = true;
        [SerializeField] private bool showMemory = true;
        [SerializeField] private bool showGPU = false;
        [SerializeField] private bool showTemperature = false;

        [Header("更新设置")]
        [SerializeField] private float updateInterval = 2f;

        [Header("警告阈值")]
        [SerializeField] private float memoryWarningThreshold = 0.8f; // 80%
        [SerializeField] private float temperatureWarningThreshold = 70f; // 70°C

        // 性能数据
        private float cpuUsage = 0f;
        private float memoryUsage = 0f;
        private float gpuUsage = 0f;
        private float temperature = 0f;
        private long totalMemory = 0;
        private long usedMemory = 0;

        private float lastUpdateTime = 0f;

        private void Awake()
        {
            if (performanceText == null)
                performanceText = GetComponent<Text>();
        }

        private void Start()
        {
            // 获取系统信息
            totalMemory = SystemInfo.systemMemorySize * 1024 * 1024; // 转换为字节

            UpdatePerformance();
        }

        /// <summary>
        /// 更新性能数据
        /// </summary>
        public void UpdatePerformance()
        {
            if (Time.time - lastUpdateTime < updateInterval) return;
            lastUpdateTime = Time.time;

            // 更新各项性能指标
            if (showCPU) UpdateCPUUsage();
            if (showMemory) UpdateMemoryUsage();
            if (showGPU) UpdateGPUUsage();
            if (showTemperature) UpdateTemperature();

            // 更新显示
            UpdateDisplay();
        }

        /// <summary>
        /// 更新CPU使用率
        /// </summary>
        private void UpdateCPUUsage()
        {
            // Unity没有直接的CPU使用率API
            // 这里使用帧时间作为CPU负载的近似指标
            float frameTime = Time.deltaTime;
            float targetFrameTime = 1f / 60f; // 60 FPS目标

            cpuUsage = Mathf.Clamp01(frameTime / targetFrameTime) * 100f;
        }

        /// <summary>
        /// 更新内存使用率
        /// </summary>
        private void UpdateMemoryUsage()
        {
            // 获取Unity分配的内存 - 使用GetTotalAllocatedMemoryLong替代已弃用的GetTotalAllocatedMemory
            usedMemory = (long)UnityEngine.Profiling.Profiler.GetTotalAllocatedMemoryLong();

            if (totalMemory > 0)
            {
                memoryUsage = (float)usedMemory / totalMemory * 100f;
            }
        }

        /// <summary>
        /// 更新GPU使用率
        /// </summary>
        private void UpdateGPUUsage()
        {
            // Unity没有直接的GPU使用率API
            // 这里使用简单的估算方法
            float frameTime = Time.deltaTime;
            float targetFrameTime = 1f / 60f;

            // 简单的GPU使用率估算（基于帧时间）
            gpuUsage = Mathf.Clamp01(frameTime / targetFrameTime) * 100f;
        }

        /// <summary>
        /// 更新温度
        /// </summary>
        private void UpdateTemperature()
        {
            // Unity没有直接的温度API
            // 在实际项目中可能需要使用原生插件
            temperature = 0f; // 占位符
        }

        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            if (performanceText == null) return;

            string displayText = "";

            if (showCPU)
            {
                displayText += $"CPU: {cpuUsage:F0}%";
            }

            if (showMemory)
            {
                if (displayText.Length > 0) displayText += "\n";
                displayText += $"RAM: {memoryUsage:F0}%";
                displayText += $" ({FormatBytes(usedMemory)})";
            }

            if (showGPU && gpuUsage > 0)
            {
                if (displayText.Length > 0) displayText += "\n";
                displayText += $"GPU: {gpuUsage:F0}%";
            }

            if (showTemperature && temperature > 0)
            {
                if (displayText.Length > 0) displayText += "\n";
                displayText += $"Temp: {temperature:F0}°C";
            }

            performanceText.text = displayText;

            // 根据性能状态设置颜色
            performanceText.color = GetPerformanceColor();
        }

        /// <summary>
        /// 获取性能状态颜色
        /// </summary>
        private Color GetPerformanceColor()
        {
            // 检查是否有警告状态
            bool hasWarning = false;

            if (showMemory && memoryUsage > memoryWarningThreshold * 100f)
                hasWarning = true;

            if (showTemperature && temperature > temperatureWarningThreshold)
                hasWarning = true;

            if (showCPU && cpuUsage > 80f)
                hasWarning = true;

            return hasWarning ? Color.red : Color.white;
        }

        /// <summary>
        /// 格式化字节数
        /// </summary>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int suffixIndex = 0;
            double size = bytes;

            while (size >= 1024 && suffixIndex < suffixes.Length - 1)
            {
                size /= 1024;
                suffixIndex++;
            }

            return $"{size:F1}{suffixes[suffixIndex]}";
        }

        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        public float GetCPUUsage()
        {
            return cpuUsage;
        }

        /// <summary>
        /// 获取内存使用率
        /// </summary>
        public float GetMemoryUsage()
        {
            return memoryUsage;
        }

        /// <summary>
        /// 获取已使用内存（字节）
        /// </summary>
        public long GetUsedMemory()
        {
            return usedMemory;
        }

        /// <summary>
        /// 获取总内存（字节）
        /// </summary>
        public long GetTotalMemory()
        {
            return totalMemory;
        }

        /// <summary>
        /// 是否有性能警告
        /// </summary>
        public bool HasPerformanceWarning()
        {
            return memoryUsage > memoryWarningThreshold * 100f ||
                   temperature > temperatureWarningThreshold ||
                   cpuUsage > 80f;
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        public string GetPerformanceReport()
        {
            return $"CPU: {cpuUsage:F1}%, Memory: {memoryUsage:F1}% ({FormatBytes(usedMemory)}/{FormatBytes(totalMemory)}), GPU: {gpuUsage:F1}%";
        }
    }
}
