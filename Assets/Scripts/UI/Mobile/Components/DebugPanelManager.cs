using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 调试面板管理器
    /// 显示移动端调试信息
    /// </summary>
    public class DebugPanelManager : MonoBehaviour
    {
        [Header("调试选项")]
        [SerializeField] public bool showTouchVisualization = true;
        [SerializeField] public bool showSafeAreaInfo = true;
        [SerializeField] public bool showPerformanceData = true;
        [SerializeField] public bool showInputInfo = true;
        [SerializeField] public bool showSystemInfo = false;

        [Header("显示设置")]
        [SerializeField] private ScrollRect scrollRect;
        [SerializeField] private Text debugText;
        [SerializeField] private int maxLogLines = 50;

        [Header("触摸可视化")]
        [SerializeField] private GameObject touchVisualizationPrefab;
        [SerializeField] private Color touchColor = Color.red;
        [SerializeField] private float touchVisualizationSize = 50f;

        // 调试数据
        private List<string> debugLines = new List<string>();
        private Dictionary<int, GameObject> touchVisualizations = new Dictionary<int, GameObject>();

        // 组件引用
        private SafeAreaHandler safeAreaHandler;
        private PerformanceMonitor performanceMonitor;
        private SimpleVirtualJoystick virtualJoystick;
        private GestureRecognizer gestureRecognizer;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            // 获取相关组件 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            safeAreaHandler = FindFirstObjectByType<SafeAreaHandler>();
            performanceMonitor = FindFirstObjectByType<PerformanceMonitor>();
            virtualJoystick = FindFirstObjectByType<SimpleVirtualJoystick>();
            gestureRecognizer = FindFirstObjectByType<GestureRecognizer>();

            // 初始化调试面板
            SetupDebugPanel();
        }

        private void Update()
        {
            UpdateDebugInfo();
            UpdateTouchVisualization();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 创建滚动视图
            if (scrollRect == null)
            {
                CreateScrollView();
            }

            // 创建调试文本
            if (debugText == null)
            {
                CreateDebugText();
            }
        }

        /// <summary>
        /// 创建滚动视图
        /// </summary>
        private void CreateScrollView()
        {
            var scrollViewGO = new GameObject("Scroll View");
            scrollViewGO.transform.SetParent(transform, false);

            var scrollRect = scrollViewGO.AddComponent<ScrollRect>();
            var scrollRectTransform = scrollViewGO.GetComponent<RectTransform>();
            scrollRectTransform.anchorMin = Vector2.zero;
            scrollRectTransform.anchorMax = Vector2.one;
            scrollRectTransform.offsetMin = Vector2.zero;
            scrollRectTransform.offsetMax = Vector2.zero;

            // 创建Viewport
            var viewportGO = new GameObject("Viewport");
            viewportGO.transform.SetParent(scrollViewGO.transform, false);

            var viewportRect = viewportGO.AddComponent<RectTransform>();
            viewportRect.anchorMin = Vector2.zero;
            viewportRect.anchorMax = Vector2.one;
            viewportRect.offsetMin = Vector2.zero;
            viewportRect.offsetMax = Vector2.zero;

            var mask = viewportGO.AddComponent<Mask>();
            mask.showMaskGraphic = false;

            var viewportImage = viewportGO.AddComponent<Image>();
            viewportImage.color = Color.clear;

            // 创建Content
            var contentGO = new GameObject("Content");
            contentGO.transform.SetParent(viewportGO.transform, false);

            var contentRect = contentGO.AddComponent<RectTransform>();
            contentRect.anchorMin = new Vector2(0, 1);
            contentRect.anchorMax = Vector2.one;
            contentRect.offsetMin = Vector2.zero;
            contentRect.offsetMax = Vector2.zero;

            var contentSizeFitter = contentGO.AddComponent<ContentSizeFitter>();
            contentSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

            var verticalLayoutGroup = contentGO.AddComponent<VerticalLayoutGroup>();
            verticalLayoutGroup.childForceExpandHeight = false;
            verticalLayoutGroup.childControlHeight = true;

            // 配置ScrollRect
            scrollRect.content = contentRect;
            scrollRect.viewport = viewportRect;
            scrollRect.horizontal = false;
            scrollRect.vertical = true;

            this.scrollRect = scrollRect;
        }

        /// <summary>
        /// 创建调试文本
        /// </summary>
        private void CreateDebugText()
        {
            var textGO = new GameObject("Debug Text");
            textGO.transform.SetParent(scrollRect.content, false);

            var textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(10, 0);
            textRect.offsetMax = new Vector2(-10, 0);

            debugText = textGO.AddComponent<Text>();
            debugText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            debugText.fontSize = 10;
            debugText.color = Color.white;
            debugText.alignment = TextAnchor.UpperLeft;
            debugText.horizontalOverflow = HorizontalWrapMode.Wrap;
            debugText.verticalOverflow = VerticalWrapMode.Overflow;

            var layoutElement = textGO.AddComponent<LayoutElement>();
            layoutElement.flexibleHeight = 1;
        }

        /// <summary>
        /// 设置调试面板
        /// </summary>
        private void SetupDebugPanel()
        {
            // 添加标题
            AddDebugLine("=== Mobile Debug Panel ===");
            AddDebugLine($"Platform: {Application.platform}");
            AddDebugLine($"Device: {SystemInfo.deviceModel}");
            AddDebugLine("");
        }

        /// <summary>
        /// 更新调试信息
        /// </summary>
        public void UpdateDebugInfo()
        {
            debugLines.Clear();

            // 标题
            AddDebugLine("=== Mobile Debug Info ===");
            AddDebugLine($"Time: {Time.time:F1}s");
            AddDebugLine("");

            // 安全区域信息
            if (showSafeAreaInfo && safeAreaHandler != null)
            {
                var safeAreaInfo = safeAreaHandler.GetSafeAreaInfo();
                AddDebugLine("--- Safe Area ---");
                AddDebugLine($"Screen: {safeAreaInfo.screenSize}");
                AddDebugLine($"Safe Area: {safeAreaInfo.safeArea}");
                AddDebugLine($"Insets: T{safeAreaInfo.topInset:F0} B{safeAreaInfo.bottomInset:F0} L{safeAreaInfo.leftInset:F0} R{safeAreaInfo.rightInset:F0}");
                AddDebugLine($"Notch: {safeAreaInfo.hasNotch}, Home: {safeAreaInfo.hasHomeIndicator}");
                AddDebugLine("");
            }

            // 性能数据
            if (showPerformanceData && performanceMonitor != null)
            {
                AddDebugLine("--- Performance ---");
                AddDebugLine($"CPU: {performanceMonitor.GetCPUUsage():F1}%");
                AddDebugLine($"Memory: {performanceMonitor.GetMemoryUsage():F1}%");
                AddDebugLine($"Used: {FormatBytes(performanceMonitor.GetUsedMemory())}");
                AddDebugLine($"Total: {FormatBytes(performanceMonitor.GetTotalMemory())}");
                AddDebugLine("");
            }

            // 输入信息
            if (showInputInfo)
            {
                AddDebugLine("--- Input ---");
                AddDebugLine($"Touch Count: {Input.touchCount}");

                if (virtualJoystick != null)
                {
                    var inputVector = virtualJoystick.GetInputVector();
                    AddDebugLine($"Joystick: ({inputVector.x:F2}, {inputVector.y:F2})");
                    AddDebugLine($"Dragging: {virtualJoystick.IsDragging()}");
                }

                if (gestureRecognizer != null)
                {
                    AddDebugLine($"Active Touches: {gestureRecognizer.GetActiveTouchCount()}");
                }

                AddDebugLine("");
            }

            // 系统信息
            if (showSystemInfo)
            {
                AddDebugLine("--- System ---");
                AddDebugLine($"Battery: {SystemInfo.batteryLevel * 100:F0}%");
                AddDebugLine($"Battery Status: {SystemInfo.batteryStatus}");
                AddDebugLine($"Network: {Application.internetReachability}");
                AddDebugLine($"Target FPS: {Application.targetFrameRate}");
                AddDebugLine("");
            }

            // 更新显示
            UpdateDebugDisplay();
        }

        /// <summary>
        /// 更新触摸可视化
        /// </summary>
        private void UpdateTouchVisualization()
        {
            if (!showTouchVisualization) return;

            // 清理不存在的触摸点
            var touchesToRemove = new List<int>();
            foreach (var kvp in touchVisualizations)
            {
                bool touchExists = false;
                for (int i = 0; i < Input.touchCount; i++)
                {
                    if (Input.GetTouch(i).fingerId == kvp.Key)
                    {
                        touchExists = true;
                        break;
                    }
                }

                if (!touchExists)
                {
                    touchesToRemove.Add(kvp.Key);
                }
            }

            foreach (int touchId in touchesToRemove)
            {
                if (touchVisualizations[touchId] != null)
                {
                    Destroy(touchVisualizations[touchId]);
                }
                touchVisualizations.Remove(touchId);
            }

            // 更新现有触摸点
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);

                if (!touchVisualizations.ContainsKey(touch.fingerId))
                {
                    CreateTouchVisualization(touch.fingerId);
                }

                UpdateTouchVisualizationPosition(touch.fingerId, touch.position);
            }
        }

        /// <summary>
        /// 创建触摸可视化
        /// </summary>
        private void CreateTouchVisualization(int touchId)
        {
            GameObject touchVis;

            if (touchVisualizationPrefab != null)
            {
                touchVis = Instantiate(touchVisualizationPrefab);
            }
            else
            {
                touchVis = CreateDefaultTouchVisualization();
            }

            touchVis.name = $"Touch_{touchId}";
            touchVisualizations[touchId] = touchVis;
        }

        /// <summary>
        /// 创建默认触摸可视化
        /// </summary>
        private GameObject CreateDefaultTouchVisualization()
        {
            var touchGO = new GameObject("Touch Visualization");

            var canvas = FindFirstObjectByType<Canvas>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (canvas != null)
            {
                touchGO.transform.SetParent(canvas.transform, false);
            }

            var rectTransform = touchGO.AddComponent<RectTransform>();
            rectTransform.sizeDelta = Vector2.one * touchVisualizationSize;

            var image = touchGO.AddComponent<Image>();
            image.color = touchColor;
            image.sprite = CreateCircleSprite();

            return touchGO;
        }

        /// <summary>
        /// 更新触摸可视化位置
        /// </summary>
        private void UpdateTouchVisualizationPosition(int touchId, Vector2 screenPosition)
        {
            if (!touchVisualizations.ContainsKey(touchId)) return;

            var touchVis = touchVisualizations[touchId];
            var rectTransform = touchVis.GetComponent<RectTransform>();

            // 将屏幕坐标转换为UI坐标 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var canvas = FindFirstObjectByType<Canvas>();
            if (canvas != null)
            {
                Vector2 localPoint;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    canvas.GetComponent<RectTransform>(),
                    screenPosition,
                    canvas.worldCamera,
                    out localPoint);

                rectTransform.anchoredPosition = localPoint;
            }
        }

        /// <summary>
        /// 添加调试行
        /// </summary>
        private void AddDebugLine(string line)
        {
            debugLines.Add(line);

            // 限制行数
            if (debugLines.Count > maxLogLines)
            {
                debugLines.RemoveAt(0);
            }
        }

        /// <summary>
        /// 更新调试显示
        /// </summary>
        private void UpdateDebugDisplay()
        {
            if (debugText != null)
            {
                debugText.text = string.Join("\n", debugLines);
            }
        }

        /// <summary>
        /// 格式化字节数
        /// </summary>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int suffixIndex = 0;
            double size = bytes;

            while (size >= 1024 && suffixIndex < suffixes.Length - 1)
            {
                size /= 1024;
                suffixIndex++;
            }

            return $"{size:F1}{suffixes[suffixIndex]}";
        }

        /// <summary>
        /// 创建圆形精灵
        /// </summary>
        private Sprite CreateCircleSprite()
        {
            var texture = new Texture2D(32, 32);
            var center = new Vector2(16, 16);

            for (int x = 0; x < 32; x++)
            {
                for (int y = 0; y < 32; y++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), center);
                    Color color = distance <= 14 ? Color.white : Color.clear;
                    texture.SetPixel(x, y, color);
                }
            }

            texture.Apply();
            return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// 清除调试日志
        /// </summary>
        public void ClearDebugLog()
        {
            debugLines.Clear();
            UpdateDebugDisplay();
        }

        /// <summary>
        /// 添加自定义调试信息
        /// </summary>
        public void AddCustomDebugInfo(string info)
        {
            AddDebugLine($"[Custom] {info}");
        }
    }
}
