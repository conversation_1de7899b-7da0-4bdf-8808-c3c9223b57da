using UnityEngine;
using GooseDuckKill.Core.Debug;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// DebugConsole UI适配器
    /// 负责监听DebugConsole事件并更新UI组件
    /// </summary>
    public class DebugConsoleUIAdapter : MonoBehaviour
    {
        [Header("UI组件引用")]
        [SerializeField] private DebugPanelManager debugPanelManager;
        [SerializeField] private bool autoFindDebugPanel = true;
        
        [Header("适配器设置")]
        [SerializeField] private bool enableEventListening = true;
        [SerializeField] private bool logEventActivity = false;
        
        private void Awake()
        {
            // 自动查找DebugPanelManager
            if (autoFindDebugPanel && debugPanelManager == null)
            {
                debugPanelManager = FindFirstObjectByType<DebugPanelManager>();
            }
        }
        
        private void OnEnable()
        {
            if (enableEventListening)
            {
                SubscribeToEvents();
            }
        }
        
        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }
        
        /// <summary>
        /// 订阅DebugConsole事件
        /// </summary>
        private void SubscribeToEvents()
        {
            DebugConsole.OnLogEntryAdded += HandleLogEntryAdded;
            DebugConsole.OnMobileUIToggled += HandleMobileUIToggled;
            DebugConsole.OnDesktopUIToggled += HandleDesktopUIToggled;
            DebugConsole.OnCommandExecuted += HandleCommandExecuted;
            
            if (logEventActivity)
            {
                Debug.Log("[DebugConsoleUIAdapter] Subscribed to DebugConsole events");
            }
        }
        
        /// <summary>
        /// 取消订阅DebugConsole事件
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            DebugConsole.OnLogEntryAdded -= HandleLogEntryAdded;
            DebugConsole.OnMobileUIToggled -= HandleMobileUIToggled;
            DebugConsole.OnDesktopUIToggled -= HandleDesktopUIToggled;
            DebugConsole.OnCommandExecuted -= HandleCommandExecuted;
            
            if (logEventActivity)
            {
                Debug.Log("[DebugConsoleUIAdapter] Unsubscribed from DebugConsole events");
            }
        }
        
        /// <summary>
        /// 处理日志条目添加事件
        /// </summary>
        private void HandleLogEntryAdded(DebugLogEntry entry)
        {
            if (debugPanelManager != null)
            {
                debugPanelManager.AddCustomDebugInfo(entry.FormattedMessage);
                
                if (logEventActivity)
                {
                    Debug.Log($"[DebugConsoleUIAdapter] Log entry added: {entry.Message}");
                }
            }
        }
        
        /// <summary>
        /// 处理移动端UI切换事件
        /// </summary>
        private void HandleMobileUIToggled(bool isVisible)
        {
            if (debugPanelManager != null)
            {
                debugPanelManager.gameObject.SetActive(isVisible);
                
                if (logEventActivity)
                {
                    Debug.Log($"[DebugConsoleUIAdapter] Mobile UI toggled: {isVisible}");
                }
            }
        }
        
        /// <summary>
        /// 处理桌面端UI切换事件
        /// </summary>
        private void HandleDesktopUIToggled(bool isVisible)
        {
            // 移动端适配器通常不处理桌面端UI事件
            // 但可以在这里添加相关逻辑，比如隐藏移动端UI
            
            if (logEventActivity)
            {
                Debug.Log($"[DebugConsoleUIAdapter] Desktop UI toggled: {isVisible}");
            }
        }
        
        /// <summary>
        /// 处理命令执行事件
        /// </summary>
        private void HandleCommandExecuted(string command)
        {
            if (debugPanelManager != null)
            {
                debugPanelManager.AddCustomDebugInfo($"[Command] {command}");
                
                if (logEventActivity)
                {
                    Debug.Log($"[DebugConsoleUIAdapter] Command executed: {command}");
                }
            }
        }
        
        /// <summary>
        /// 手动设置DebugPanelManager引用
        /// </summary>
        public void SetDebugPanelManager(DebugPanelManager panelManager)
        {
            debugPanelManager = panelManager;
        }
        
        /// <summary>
        /// 获取当前的DebugPanelManager引用
        /// </summary>
        public DebugPanelManager GetDebugPanelManager()
        {
            return debugPanelManager;
        }
        
        /// <summary>
        /// 启用/禁用事件监听
        /// </summary>
        public void SetEventListening(bool enabled)
        {
            if (enabled && !enableEventListening)
            {
                enableEventListening = true;
                SubscribeToEvents();
            }
            else if (!enabled && enableEventListening)
            {
                enableEventListening = false;
                UnsubscribeFromEvents();
            }
        }
        
        /// <summary>
        /// 测试适配器功能
        /// </summary>
        [ContextMenu("Test Adapter")]
        private void TestAdapter()
        {
            if (debugPanelManager != null)
            {
                debugPanelManager.AddCustomDebugInfo("[Test] DebugConsoleUIAdapter is working!");
                Debug.Log("[DebugConsoleUIAdapter] Test completed - check debug panel for message");
            }
            else
            {
                Debug.LogWarning("[DebugConsoleUIAdapter] DebugPanelManager not found - cannot test");
            }
        }
        
        #if UNITY_EDITOR
        /// <summary>
        /// 编辑器中的验证方法
        /// </summary>
        private void OnValidate()
        {
            if (autoFindDebugPanel && debugPanelManager == null)
            {
                debugPanelManager = FindFirstObjectByType<DebugPanelManager>();
            }
        }
        #endif
    }
}
