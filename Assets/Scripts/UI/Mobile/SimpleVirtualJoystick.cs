using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 简单虚拟摇杆
    /// 提供移动端的方向输入控制
    /// </summary>
    public class SimpleVirtualJoystick : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IDragHandler
    {
        [Header("摇杆组件")]
        public RectTransform background;
        public RectTransform handle;

        [Header("摇杆设置")]
        [SerializeField] private float handleRange = 50f;
        [SerializeField] private bool snapToCenter = true;
        [SerializeField] private float returnSpeed = 10f;
        [SerializeField] private bool enableVisualEffects = true;

        [Header("输入设置")]
        [SerializeField] private float deadZone = 0.1f;
        [SerializeField] private bool normalizeInput = true;
        [SerializeField] private bool invertX = false;
        [SerializeField] private bool invertY = false;

        // 输入状态
        private Vector2 inputVector = Vector2.zero;
        private bool isDragging = false;
        private Vector2 centerPosition;

        // 组件引用
        private Image backgroundImage;
        private Image handleImage;
        private Canvas parentCanvas;

        // 输入事件
        public System.Action<Vector2> OnInputChanged;
        public System.Action OnInputStarted;
        public System.Action OnInputEnded;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            SetupJoystick();
        }

        private void Update()
        {
            if (!isDragging && snapToCenter)
            {
                ReturnToCenter();
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 获取父级Canvas
            parentCanvas = GetComponentInParent<Canvas>();

            // 如果没有指定背景和手柄，尝试自动查找
            if (background == null)
            {
                background = transform.Find("Background")?.GetComponent<RectTransform>();
                if (background == null)
                {
                    background = GetComponent<RectTransform>();
                }
            }

            if (handle == null)
            {
                handle = transform.Find("Handle")?.GetComponent<RectTransform>();
            }

            // 获取Image组件
            if (background != null)
            {
                backgroundImage = background.GetComponent<Image>();
            }

            if (handle != null)
            {
                handleImage = handle.GetComponent<Image>();
            }
        }

        /// <summary>
        /// 设置摇杆
        /// </summary>
        private void SetupJoystick()
        {
            // 记录中心位置
            centerPosition = handle != null ? handle.anchoredPosition : Vector2.zero;
        }

        /// <summary>
        /// 指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            isDragging = true;
            OnInputStarted?.Invoke();
            OnDrag(eventData);
        }

        /// <summary>
        /// 指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            isDragging = false;
            inputVector = Vector2.zero;
            OnInputChanged?.Invoke(inputVector);
            OnInputEnded?.Invoke();
        }

        /// <summary>
        /// 拖拽事件
        /// </summary>
        public void OnDrag(PointerEventData eventData)
        {
            if (!isDragging || handle == null || background == null) return;

            // 将屏幕坐标转换为本地坐标
            Vector2 localPoint;
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                background, eventData.position, parentCanvas.worldCamera, out localPoint))
            {
                // 限制在摇杆范围内
                Vector2 clampedPosition = Vector2.ClampMagnitude(localPoint, handleRange);

                // 设置手柄位置
                handle.anchoredPosition = clampedPosition;

                // 计算输入向量
                inputVector = clampedPosition / handleRange;

                // 应用死区
                if (inputVector.magnitude < deadZone)
                {
                    inputVector = Vector2.zero;
                }
                else if (normalizeInput)
                {
                    // 重新映射到0-1范围，考虑死区
                    float magnitude = (inputVector.magnitude - deadZone) / (1f - deadZone);
                    inputVector = inputVector.normalized * magnitude;
                }

                // 应用反转设置
                if (invertX) inputVector.x = -inputVector.x;
                if (invertY) inputVector.y = -inputVector.y;

                // 触发输入事件
                OnInputChanged?.Invoke(inputVector);
            }
        }

        /// <summary>
        /// 返回中心位置
        /// </summary>
        private void ReturnToCenter()
        {
            if (handle == null) return;

            // 平滑移动到中心
            handle.anchoredPosition = Vector2.Lerp(
                handle.anchoredPosition,
                centerPosition,
                returnSpeed * Time.deltaTime);

            // 如果足够接近中心，直接设置为中心
            if (Vector2.Distance(handle.anchoredPosition, centerPosition) < 1f)
            {
                handle.anchoredPosition = centerPosition;
            }
        }

        /// <summary>
        /// 更新摇杆
        /// </summary>
        public void UpdateJoystick()
        {
            // 这个方法由MobileControlsManager调用
        }

        /// <summary>
        /// 设置视觉效果启用状态
        /// </summary>
        public void SetVisualEffectsEnabled(bool enabled)
        {
            enableVisualEffects = enabled;
        }

        /// <summary>
        /// 获取当前输入向量
        /// </summary>
        public Vector2 GetInputVector()
        {
            return inputVector;
        }

        /// <summary>
        /// 获取是否正在拖拽
        /// </summary>
        public bool IsDragging()
        {
            return isDragging;
        }

        /// <summary>
        /// 重置摇杆
        /// </summary>
        public void ResetJoystick()
        {
            isDragging = false;
            inputVector = Vector2.zero;

            if (handle != null)
            {
                handle.anchoredPosition = centerPosition;
            }

            OnInputChanged?.Invoke(inputVector);
        }

        /// <summary>
        /// 设置摇杆启用状态
        /// </summary>
        public void SetEnabled(bool enabled)
        {
            gameObject.SetActive(enabled);

            if (!enabled)
            {
                ResetJoystick();
            }
        }

        /// <summary>
        /// 设置摇杆范围
        /// </summary>
        public void SetHandleRange(float range)
        {
            handleRange = Mathf.Max(0, range);
        }
    }
}
