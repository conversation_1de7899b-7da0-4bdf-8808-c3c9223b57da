using UnityEngine;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 触觉反馈控制器
    /// 管理移动设备的震动反馈
    /// </summary>
    public class HapticFeedbackController : MonoBehaviour
    {
        [Header("触觉反馈设置")]
        [SerializeField] private bool enableHapticFeedback = true;
        [SerializeField] private MobileControlsManager.HapticFeedbackType defaultFeedbackType = MobileControlsManager.HapticFeedbackType.Light;
        
        [Header("震动强度设置")]
        [SerializeField] private float lightIntensity = 0.3f;
        [SerializeField] private float mediumIntensity = 0.6f;
        [SerializeField] private float heavyIntensity = 1f;
        
        [Header("震动持续时间设置")]
        [SerializeField] private float lightDuration = 0.1f;
        [SerializeField] private float mediumDuration = 0.2f;
        [SerializeField] private float heavyDuration = 0.3f;
        
        [Header("频率限制")]
        [SerializeField] private float minTimeBetweenHaptics = 0.05f; // 最小间隔时间
        [SerializeField] private int maxHapticsPerSecond = 10; // 每秒最大震动次数
        
        [Header("调试设置")]
        [SerializeField] private bool logHapticEvents = false;
        [SerializeField] private bool enableInEditor = false; // 编辑器中是否启用（用于测试）
        
        // 状态变量
        private float lastHapticTime = 0f;
        private int hapticCountThisSecond = 0;
        private float lastSecondTime = 0f;
        
        // 平台支持检测
        private bool isHapticSupported = false;
        
        private void Start()
        {
            InitializeHapticSupport();
        }
        
        private void Update()
        {
            // 重置每秒计数器
            if (Time.time - lastSecondTime >= 1f)
            {
                hapticCountThisSecond = 0;
                lastSecondTime = Time.time;
            }
        }
        
        /// <summary>
        /// 初始化触觉反馈支持
        /// </summary>
        private void InitializeHapticSupport()
        {
            // 检查平台支持
            #if UNITY_ANDROID || UNITY_IOS
            isHapticSupported = true;
            #elif UNITY_EDITOR
            isHapticSupported = enableInEditor;
            #else
            isHapticSupported = false;
            #endif
            
            if (logHapticEvents)
            {
                Debug.Log($"触觉反馈支持: {isHapticSupported}");
            }
        }
        
        /// <summary>
        /// 触发触觉反馈
        /// </summary>
        public void TriggerHaptic(MobileControlsManager.HapticFeedbackType feedbackType)
        {
            if (!CanTriggerHaptic()) return;
            
            switch (feedbackType)
            {
                case MobileControlsManager.HapticFeedbackType.Light:
                    TriggerLightHaptic();
                    break;
                case MobileControlsManager.HapticFeedbackType.Medium:
                    TriggerMediumHaptic();
                    break;
                case MobileControlsManager.HapticFeedbackType.Heavy:
                    TriggerHeavyHaptic();
                    break;
                case MobileControlsManager.HapticFeedbackType.None:
                default:
                    return;
            }
            
            // 更新状态
            lastHapticTime = Time.time;
            hapticCountThisSecond++;
            
            if (logHapticEvents)
            {
                Debug.Log($"触觉反馈触发: {feedbackType}");
            }
        }
        
        /// <summary>
        /// 触发轻度震动
        /// </summary>
        public void TriggerLightHaptic()
        {
            if (!CanTriggerHaptic()) return;
            
            #if UNITY_ANDROID && !UNITY_EDITOR
            TriggerAndroidHaptic(lightDuration, lightIntensity);
            #elif UNITY_IOS && !UNITY_EDITOR
            TriggerIOSHaptic(0); // iOS轻度反馈
            #elif UNITY_EDITOR
            if (enableInEditor)
            {
                Debug.Log($"[模拟] 轻度震动: 强度{lightIntensity}, 持续{lightDuration}s");
            }
            #endif
        }
        
        /// <summary>
        /// 触发中度震动
        /// </summary>
        public void TriggerMediumHaptic()
        {
            if (!CanTriggerHaptic()) return;
            
            #if UNITY_ANDROID && !UNITY_EDITOR
            TriggerAndroidHaptic(mediumDuration, mediumIntensity);
            #elif UNITY_IOS && !UNITY_EDITOR
            TriggerIOSHaptic(1); // iOS中度反馈
            #elif UNITY_EDITOR
            if (enableInEditor)
            {
                Debug.Log($"[模拟] 中度震动: 强度{mediumIntensity}, 持续{mediumDuration}s");
            }
            #endif
        }
        
        /// <summary>
        /// 触发重度震动
        /// </summary>
        public void TriggerHeavyHaptic()
        {
            if (!CanTriggerHaptic()) return;
            
            #if UNITY_ANDROID && !UNITY_EDITOR
            TriggerAndroidHaptic(heavyDuration, heavyIntensity);
            #elif UNITY_IOS && !UNITY_EDITOR
            TriggerIOSHaptic(2); // iOS重度反馈
            #elif UNITY_EDITOR
            if (enableInEditor)
            {
                Debug.Log($"[模拟] 重度震动: 强度{heavyIntensity}, 持续{heavyDuration}s");
            }
            #endif
        }
        
        /// <summary>
        /// 触发自定义震动
        /// </summary>
        public void TriggerCustomHaptic(float duration, float intensity)
        {
            if (!CanTriggerHaptic()) return;
            
            // 限制参数范围
            duration = Mathf.Clamp(duration, 0.01f, 1f);
            intensity = Mathf.Clamp01(intensity);
            
            #if UNITY_ANDROID && !UNITY_EDITOR
            TriggerAndroidHaptic(duration, intensity);
            #elif UNITY_IOS && !UNITY_EDITOR
            // iOS根据强度选择反馈类型
            int feedbackType = intensity < 0.4f ? 0 : (intensity < 0.7f ? 1 : 2);
            TriggerIOSHaptic(feedbackType);
            #elif UNITY_EDITOR
            if (enableInEditor)
            {
                Debug.Log($"[模拟] 自定义震动: 强度{intensity}, 持续{duration}s");
            }
            #endif
            
            // 更新状态
            lastHapticTime = Time.time;
            hapticCountThisSecond++;
        }
        
        /// <summary>
        /// 触发震动序列
        /// </summary>
        public void TriggerHapticSequence(MobileControlsManager.HapticFeedbackType[] sequence, float[] intervals)
        {
            if (sequence == null || intervals == null || sequence.Length != intervals.Length)
            {
                Debug.LogError("震动序列参数错误");
                return;
            }
            
            StartCoroutine(PlayHapticSequence(sequence, intervals));
        }
        
        /// <summary>
        /// 播放震动序列协程
        /// </summary>
        private System.Collections.IEnumerator PlayHapticSequence(MobileControlsManager.HapticFeedbackType[] sequence, float[] intervals)
        {
            for (int i = 0; i < sequence.Length; i++)
            {
                TriggerHaptic(sequence[i]);
                
                if (i < intervals.Length - 1)
                {
                    yield return new WaitForSeconds(intervals[i]);
                }
            }
        }
        
        /// <summary>
        /// 检查是否可以触发震动
        /// </summary>
        private bool CanTriggerHaptic()
        {
            if (!enableHapticFeedback || !isHapticSupported)
                return false;
            
            // 检查频率限制
            if (Time.time - lastHapticTime < minTimeBetweenHaptics)
                return false;
            
            // 检查每秒次数限制
            if (hapticCountThisSecond >= maxHapticsPerSecond)
                return false;
            
            return true;
        }
        
        #if UNITY_ANDROID && !UNITY_EDITOR
        /// <summary>
        /// Android平台震动实现
        /// </summary>
        private void TriggerAndroidHaptic(float duration, float intensity)
        {
            try
            {
                // 使用Android的Vibrator API
                using (var vibrator = new AndroidJavaClass("android.os.Vibrator"))
                using (var context = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
                using (var currentActivity = context.GetStatic<AndroidJavaObject>("currentActivity"))
                using (var vibratorService = currentActivity.Call<AndroidJavaObject>("getSystemService", "vibrator"))
                {
                    if (vibratorService.Call<bool>("hasVibrator"))
                    {
                        long durationMs = (long)(duration * 1000);
                        
                        // Android API 26+ 支持强度控制
                        if (Application.platform == RuntimePlatform.Android)
                        {
                            int amplitudeValue = (int)(intensity * 255);
                            vibratorService.Call("vibrate", durationMs, amplitudeValue);
                        }
                        else
                        {
                            // 旧版本Android只支持持续时间
                            vibratorService.Call("vibrate", durationMs);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Android震动失败: {e.Message}");
            }
        }
        #endif
        
        #if UNITY_IOS && !UNITY_EDITOR
        /// <summary>
        /// iOS平台震动实现
        /// </summary>
        private void TriggerIOSHaptic(int feedbackType)
        {
            try
            {
                // 使用iOS的UIImpactFeedbackGenerator
                switch (feedbackType)
                {
                    case 0: // Light
                        Handheld.Vibrate(); // Unity内置的轻度震动
                        break;
                    case 1: // Medium
                        Handheld.Vibrate();
                        break;
                    case 2: // Heavy
                        Handheld.Vibrate();
                        break;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"iOS震动失败: {e.Message}");
            }
        }
        #endif
        
        /// <summary>
        /// 设置默认反馈类型
        /// </summary>
        public void SetDefaultFeedbackType(MobileControlsManager.HapticFeedbackType feedbackType)
        {
            defaultFeedbackType = feedbackType;
        }
        
        /// <summary>
        /// 启用/禁用触觉反馈
        /// </summary>
        public void EnableHapticFeedback(bool enabled)
        {
            enableHapticFeedback = enabled;
            
            if (logHapticEvents)
            {
                Debug.Log($"触觉反馈: {(enabled ? "启用" : "禁用")}");
            }
        }
        
        /// <summary>
        /// 设置震动强度
        /// </summary>
        public void SetHapticIntensity(MobileControlsManager.HapticFeedbackType type, float intensity)
        {
            intensity = Mathf.Clamp01(intensity);
            
            switch (type)
            {
                case MobileControlsManager.HapticFeedbackType.Light:
                    lightIntensity = intensity;
                    break;
                case MobileControlsManager.HapticFeedbackType.Medium:
                    mediumIntensity = intensity;
                    break;
                case MobileControlsManager.HapticFeedbackType.Heavy:
                    heavyIntensity = intensity;
                    break;
            }
        }
        
        /// <summary>
        /// 设置震动持续时间
        /// </summary>
        public void SetHapticDuration(MobileControlsManager.HapticFeedbackType type, float duration)
        {
            duration = Mathf.Clamp(duration, 0.01f, 1f);
            
            switch (type)
            {
                case MobileControlsManager.HapticFeedbackType.Light:
                    lightDuration = duration;
                    break;
                case MobileControlsManager.HapticFeedbackType.Medium:
                    mediumDuration = duration;
                    break;
                case MobileControlsManager.HapticFeedbackType.Heavy:
                    heavyDuration = duration;
                    break;
            }
        }
        
        /// <summary>
        /// 获取触觉反馈是否启用
        /// </summary>
        public bool IsHapticFeedbackEnabled()
        {
            return enableHapticFeedback && isHapticSupported;
        }
        
        /// <summary>
        /// 获取平台是否支持触觉反馈
        /// </summary>
        public bool IsHapticSupported()
        {
            return isHapticSupported;
        }
        
        /// <summary>
        /// 测试所有震动类型
        /// </summary>
        [ContextMenu("Test All Haptic Types")]
        public void TestAllHapticTypes()
        {
            StartCoroutine(TestHapticSequence());
        }
        
        private System.Collections.IEnumerator TestHapticSequence()
        {
            Debug.Log("开始测试触觉反馈...");
            
            TriggerLightHaptic();
            yield return new WaitForSeconds(0.5f);
            
            TriggerMediumHaptic();
            yield return new WaitForSeconds(0.5f);
            
            TriggerHeavyHaptic();
            yield return new WaitForSeconds(0.5f);
            
            Debug.Log("触觉反馈测试完成");
        }
    }
}
