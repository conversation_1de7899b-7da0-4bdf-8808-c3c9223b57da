using UnityEngine;
using UnityEngine.UI;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 移动端控制管理器
    /// 管理虚拟摇杆、触摸按钮等移动端专用控制元素
    /// </summary>
    public class MobileControlsManager : MonoBehaviour
    {
        [Header("控制根节点")]
        [SerializeField] private RectTransform controlsRoot;

        [Header("虚拟摇杆设置")]
        [SerializeField] public bool enableVirtualJoystick = true;
        [SerializeField] public GameObject virtualJoystickPrefab;
        [SerializeField] public Vector2 joystickPosition = new Vector2(150, 150);
        [SerializeField] public float joystickSize = 120f;

        [Header("动作按钮设置")]
        [SerializeField] public bool enableActionButtons = true;
        [SerializeField] public GameObject actionButtonPrefab;
        [SerializeField] public ActionButtonConfig[] actionButtons;

        [Header("手势识别")]
        [SerializeField] public bool enableGestureRecognition = true;
        [SerializeField] public float swipeThreshold = 50f;
        [SerializeField] public float tapTimeThreshold = 0.3f;

        [Header("触觉反馈")]
        [SerializeField] public bool enableHapticFeedback = true;
        [SerializeField] public HapticFeedbackType defaultHapticType = HapticFeedbackType.Light;

        [Header("性能设置")]
        [SerializeField] public float updateFrequency = 1f; // 每秒更新次数
        [SerializeField] public bool enableVisualEffects = true;

        // 组件引用
        private SimpleVirtualJoystick virtualJoystick;
        private MobileActionButton[] mobileActionButtons;
        private GestureRecognizer gestureRecognizer;
        private HapticFeedbackController hapticController;

        // 状态变量
        private float lastUpdateTime;
        private bool isInitialized = false;

        [System.Serializable]
        public struct ActionButtonConfig
        {
            public string buttonName;
            public Vector2 position;
            public float size;
            public Sprite icon;
            public Color color;
            public bool isVisible;
            public KeyCode keyCode; // 对应的键盘按键
        }

        public enum HapticFeedbackType
        {
            None,
            Light,
            Medium,
            Heavy
        }

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            if (Application.isMobilePlatform)
            {
                InitializeControls();
            }
            else
            {
                // 非移动平台禁用
                gameObject.SetActive(false);
            }
        }

        private void Update()
        {
            if (!isInitialized) return;

            // 控制更新频率以优化性能
            if (Time.time - lastUpdateTime < 1f / updateFrequency) return;
            lastUpdateTime = Time.time;

            UpdateControls();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            if (controlsRoot == null)
            {
                controlsRoot = GetComponent<RectTransform>();
            }

            // 获取或添加子组件
            gestureRecognizer = GetOrAddComponent<GestureRecognizer>();
            hapticController = GetOrAddComponent<HapticFeedbackController>();
        }

        /// <summary>
        /// 初始化控制元素
        /// </summary>
        public void InitializeControls()
        {
            CreateVirtualJoystick();
            CreateActionButtons();
            SetupGestureRecognition();
            SetupHapticFeedback();

            isInitialized = true;
            Debug.Log("Mobile Controls 初始化完成");
        }

        /// <summary>
        /// 创建虚拟摇杆
        /// </summary>
        private void CreateVirtualJoystick()
        {
            if (!enableVirtualJoystick) return;

            GameObject joystickGO;

            if (virtualJoystickPrefab != null)
            {
                joystickGO = Instantiate(virtualJoystickPrefab, controlsRoot);
            }
            else
            {
                joystickGO = CreateDefaultJoystick();
            }

            virtualJoystick = joystickGO.GetComponent<SimpleVirtualJoystick>();
            if (virtualJoystick == null)
            {
                virtualJoystick = joystickGO.AddComponent<SimpleVirtualJoystick>();
            }

            // 设置摇杆位置和大小
            var rectTransform = joystickGO.GetComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.zero;
            rectTransform.anchoredPosition = joystickPosition;
            rectTransform.sizeDelta = Vector2.one * joystickSize;

            Debug.Log("虚拟摇杆创建完成");
        }

        /// <summary>
        /// 创建默认摇杆
        /// </summary>
        private GameObject CreateDefaultJoystick()
        {
            var joystickGO = new GameObject("Virtual Joystick");
            joystickGO.transform.SetParent(controlsRoot, false);

            // 添加背景
            var background = new GameObject("Background");
            background.transform.SetParent(joystickGO.transform, false);
            var bgImage = background.AddComponent<Image>();
            bgImage.color = new Color(1, 1, 1, 0.3f);
            bgImage.sprite = CreateCircleSprite();

            var bgRect = background.GetComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;

            // 添加手柄
            var handle = new GameObject("Handle");
            handle.transform.SetParent(joystickGO.transform, false);
            var handleImage = handle.AddComponent<Image>();
            handleImage.color = new Color(1, 1, 1, 0.8f);
            handleImage.sprite = CreateCircleSprite();

            var handleRect = handle.GetComponent<RectTransform>();
            handleRect.anchorMin = new Vector2(0.5f, 0.5f);
            handleRect.anchorMax = new Vector2(0.5f, 0.5f);
            handleRect.sizeDelta = Vector2.one * 40f;

            return joystickGO;
        }

        /// <summary>
        /// 创建动作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            if (!enableActionButtons || actionButtons == null) return;

            mobileActionButtons = new MobileActionButton[actionButtons.Length];

            for (int i = 0; i < actionButtons.Length; i++)
            {
                var config = actionButtons[i];
                if (!config.isVisible) continue;

                GameObject buttonGO = CreateActionButton(config);
                mobileActionButtons[i] = buttonGO.GetComponent<MobileActionButton>();
            }

            Debug.Log($"创建了 {actionButtons.Length} 个动作按钮");
        }

        /// <summary>
        /// 创建单个动作按钮
        /// </summary>
        private GameObject CreateActionButton(ActionButtonConfig config)
        {
            GameObject buttonGO;

            if (actionButtonPrefab != null)
            {
                buttonGO = Instantiate(actionButtonPrefab, controlsRoot);
            }
            else
            {
                buttonGO = CreateDefaultActionButton(config);
            }

            buttonGO.name = config.buttonName;

            // 设置位置和大小
            var rectTransform = buttonGO.GetComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.one;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.anchoredPosition = -config.position; // 右下角为基准
            rectTransform.sizeDelta = Vector2.one * config.size;

            // 配置按钮组件
            var actionButton = buttonGO.GetComponent<MobileActionButton>();
            if (actionButton == null)
            {
                actionButton = buttonGO.AddComponent<MobileActionButton>();
            }

            actionButton.SetKeyCode(config.keyCode);
            actionButton.SetHapticFeedback(enableHapticFeedback);

            return buttonGO;
        }

        /// <summary>
        /// 创建默认动作按钮
        /// </summary>
        private GameObject CreateDefaultActionButton(ActionButtonConfig config)
        {
            var buttonGO = new GameObject(config.buttonName);
            buttonGO.transform.SetParent(controlsRoot, false);

            var image = buttonGO.AddComponent<Image>();
            image.color = config.color;
            image.sprite = config.icon ?? CreateCircleSprite();

            var button = buttonGO.AddComponent<Button>();
            button.targetGraphic = image;

            return buttonGO;
        }

        /// <summary>
        /// 设置手势识别
        /// </summary>
        private void SetupGestureRecognition()
        {
            if (!enableGestureRecognition || gestureRecognizer == null) return;

            gestureRecognizer.SetSwipeThreshold(swipeThreshold);
            gestureRecognizer.SetTapTimeThreshold(tapTimeThreshold);
            gestureRecognizer.EnableGestureRecognition(true);
        }

        /// <summary>
        /// 设置触觉反馈
        /// </summary>
        private void SetupHapticFeedback()
        {
            if (!enableHapticFeedback || hapticController == null) return;

            hapticController.SetDefaultFeedbackType(defaultHapticType);
            hapticController.EnableHapticFeedback(true);
        }

        /// <summary>
        /// 更新控制元素
        /// </summary>
        private void UpdateControls()
        {
            // 更新虚拟摇杆
            if (virtualJoystick != null)
            {
                virtualJoystick.UpdateJoystick();
            }

            // 更新动作按钮
            if (mobileActionButtons != null)
            {
                foreach (var button in mobileActionButtons)
                {
                    if (button != null)
                    {
                        button.UpdateButton();
                    }
                }
            }
        }

        /// <summary>
        /// 布局更新回调
        /// </summary>
        public void OnLayoutUpdated()
        {
            // 重新调整控制元素位置
            AdjustControlsLayout();
        }

        /// <summary>
        /// 屏幕方向变化回调
        /// </summary>
        public void OnOrientationChanged(bool isLandscape)
        {
            // 根据方向调整控制布局
            if (isLandscape)
            {
                // 横屏时调整摇杆和按钮位置
                AdjustForLandscape();
            }
            else
            {
                // 竖屏时恢复默认位置
                AdjustForPortrait();
            }
        }

        /// <summary>
        /// 调整控制布局
        /// </summary>
        private void AdjustControlsLayout()
        {
            // 根据安全区域调整控制元素位置
            var safeAreaHandler = GetComponentInParent<SafeAreaHandler>();
            if (safeAreaHandler != null)
            {
                var safeAreaInfo = safeAreaHandler.GetSafeAreaInfo();

                // 调整摇杆位置避开安全区域
                if (virtualJoystick != null)
                {
                    AdjustJoystickForSafeArea(safeAreaInfo);
                }

                // 调整按钮位置
                if (mobileActionButtons != null)
                {
                    AdjustButtonsForSafeArea(safeAreaInfo);
                }
            }
        }

        /// <summary>
        /// 为横屏调整布局
        /// </summary>
        private void AdjustForLandscape()
        {
            // 横屏时可能需要调整控制元素的间距和大小
            if (virtualJoystick != null)
            {
                var rect = virtualJoystick.GetComponent<RectTransform>();
                rect.anchoredPosition = new Vector2(joystickPosition.x * 0.8f, joystickPosition.y);
            }
        }

        /// <summary>
        /// 为竖屏调整布局
        /// </summary>
        private void AdjustForPortrait()
        {
            // 恢复默认位置
            if (virtualJoystick != null)
            {
                var rect = virtualJoystick.GetComponent<RectTransform>();
                rect.anchoredPosition = joystickPosition;
            }
        }

        /// <summary>
        /// 根据安全区域调整摇杆
        /// </summary>
        private void AdjustJoystickForSafeArea(SafeAreaInfo safeAreaInfo)
        {
            if (virtualJoystick == null) return;

            var rect = virtualJoystick.GetComponent<RectTransform>();
            var adjustedPosition = joystickPosition;

            // 避开底部安全区域
            if (safeAreaInfo.bottomInset > 0)
            {
                adjustedPosition.y += safeAreaInfo.bottomInset;
            }

            // 避开左侧安全区域
            if (safeAreaInfo.leftInset > 0)
            {
                adjustedPosition.x += safeAreaInfo.leftInset;
            }

            rect.anchoredPosition = adjustedPosition;
        }

        /// <summary>
        /// 根据安全区域调整按钮
        /// </summary>
        private void AdjustButtonsForSafeArea(SafeAreaInfo safeAreaInfo)
        {
            if (mobileActionButtons == null) return;

            for (int i = 0; i < mobileActionButtons.Length; i++)
            {
                if (mobileActionButtons[i] == null) continue;

                var rect = mobileActionButtons[i].GetComponent<RectTransform>();
                var config = actionButtons[i];
                var adjustedPosition = -config.position;

                // 避开底部和右侧安全区域
                if (safeAreaInfo.bottomInset > 0)
                {
                    adjustedPosition.y -= safeAreaInfo.bottomInset;
                }
                if (safeAreaInfo.rightInset > 0)
                {
                    adjustedPosition.x += safeAreaInfo.rightInset;
                }

                rect.anchoredPosition = adjustedPosition;
            }
        }

        /// <summary>
        /// 创建圆形精灵
        /// </summary>
        private Sprite CreateCircleSprite()
        {
            // 创建一个简单的圆形纹理
            var texture = new Texture2D(64, 64);
            var center = new Vector2(32, 32);

            for (int x = 0; x < 64; x++)
            {
                for (int y = 0; y < 64; y++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), center);
                    Color color = distance <= 30 ? Color.white : Color.clear;
                    texture.SetPixel(x, y, color);
                }
            }

            texture.Apply();
            return Sprite.Create(texture, new Rect(0, 0, 64, 64), new Vector2(0.5f, 0.5f));
        }

        /// <summary>
        /// 获取或添加组件
        /// </summary>
        private T GetOrAddComponent<T>() where T : Component
        {
            var component = GetComponent<T>();
            if (component == null)
            {
                component = gameObject.AddComponent<T>();
            }
            return component;
        }

        /// <summary>
        /// 设置控制根节点
        /// </summary>
        public void SetControlsRoot(RectTransform root)
        {
            controlsRoot = root;
        }

        /// <summary>
        /// 设置更新频率
        /// </summary>
        public void SetUpdateFrequency(float frequency)
        {
            updateFrequency = frequency;
        }

        /// <summary>
        /// 设置视觉效果启用状态
        /// </summary>
        public void SetVisualEffectsEnabled(bool enabled)
        {
            enableVisualEffects = enabled;

            // 通知所有控制元素更新视觉效果状态
            if (virtualJoystick != null)
            {
                virtualJoystick.SetVisualEffectsEnabled(enabled);
            }

            if (mobileActionButtons != null)
            {
                foreach (var button in mobileActionButtons)
                {
                    if (button != null)
                    {
                        button.SetVisualEffectsEnabled(enabled);
                    }
                }
            }
        }
    }
}
