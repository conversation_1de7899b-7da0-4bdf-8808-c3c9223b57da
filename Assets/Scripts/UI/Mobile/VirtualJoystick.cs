using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace GooseDuckKill.UI.Mobile
{
    /// <summary>
    /// 虚拟摇杆组件
    /// 支持动态和固定两种模式
    /// </summary>
    public class VirtualJoystick : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IPointerUpHandler
    {
        [Header("摇杆设置")]
        [SerializeField] private RectTransform joystickBackground;
        [SerializeField] private RectTransform joystickHandle;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image handleImage;

        [Header("行为设置")]
        [SerializeField] private JoystickType joystickType = JoystickType.Dynamic;
        [SerializeField] private float joystickRange = 50f;
        [SerializeField] private bool snapToCenter = true;
        [SerializeField] private float returnSpeed = 10f;

        [Header("视觉设置")]
        [SerializeField] private bool showBackground = true;
        [SerializeField] private float backgroundAlpha = 0.5f;
        [SerializeField] private float handleAlpha = 0.8f;
        [SerializeField] private Color joystickColor = Color.white;

        [Header("移动优化")]
        [SerializeField] private bool enableSmoothing = true;
        [SerializeField] private float smoothingFactor = 5f;
        [SerializeField] private bool enableDeadZone = true;
        [SerializeField] private float deadZone = 0.1f;

        // 输入状态
        public Vector2 InputVector { get; private set; }
        public bool IsPressed { get; private set; }

        // 内部状态
        private Vector2 _startPosition;
        private Vector2 _currentPosition;
        private bool _isDragging;
        private Canvas _parentCanvas;
        private Camera _canvasCamera;

        // 性能优化
        private Vector2 _smoothedInput;
        private float _lastUpdateTime;

        public enum JoystickType
        {
            Fixed,      // 固定位置
            Dynamic,    // 动态位置
            Floating    // 浮动跟随
        }

        private void Start()
        {
            InitializeJoystick();
        }

        private void Update()
        {
            if (enableSmoothing && _isDragging)
            {
                UpdateSmoothedInput();
            }

            if (!_isDragging && snapToCenter)
            {
                ReturnToCenter();
            }
        }

        /// <summary>
        /// 初始化摇杆
        /// </summary>
        private void InitializeJoystick()
        {
            // 获取Canvas信息
            _parentCanvas = GetComponentInParent<Canvas>();
            if (_parentCanvas != null)
            {
                _canvasCamera = _parentCanvas.worldCamera;
            }

            // 设置初始位置
            _startPosition = joystickBackground.anchoredPosition;

            // 设置视觉效果
            SetupVisuals();

            // 根据类型设置初始状态
            if (joystickType == JoystickType.Dynamic)
            {
                SetJoystickVisibility(false);
            }
        }

        /// <summary>
        /// 设置视觉效果
        /// </summary>
        private void SetupVisuals()
        {
            if (backgroundImage != null)
            {
                backgroundImage.color = new Color(joystickColor.r, joystickColor.g, joystickColor.b, backgroundAlpha);
                backgroundImage.gameObject.SetActive(showBackground);
            }

            if (handleImage != null)
            {
                handleImage.color = new Color(joystickColor.r, joystickColor.g, joystickColor.b, handleAlpha);
            }
        }

        /// <summary>
        /// 指针按下事件
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            IsPressed = true;
            _isDragging = true;

            Vector2 localPoint;
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                joystickBackground, eventData.position, _canvasCamera, out localPoint))
            {
                if (joystickType == JoystickType.Dynamic)
                {
                    // 动态摇杆：在触摸位置显示
                    joystickBackground.anchoredPosition = localPoint;
                    SetJoystickVisibility(true);
                }

                _currentPosition = localPoint;
                UpdateJoystickHandle();
            }
        }

        /// <summary>
        /// 拖拽事件
        /// </summary>
        public void OnDrag(PointerEventData eventData)
        {
            if (!_isDragging) return;

            Vector2 localPoint;
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                joystickBackground, eventData.position, _canvasCamera, out localPoint))
            {
                _currentPosition = localPoint;
                UpdateJoystickHandle();
            }
        }

        /// <summary>
        /// 指针抬起事件
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            IsPressed = false;
            _isDragging = false;

            if (joystickType == JoystickType.Dynamic)
            {
                SetJoystickVisibility(false);
            }

            // 重置输入
            InputVector = Vector2.zero;
            _smoothedInput = Vector2.zero;

            if (snapToCenter)
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
        }

        /// <summary>
        /// 更新摇杆手柄位置
        /// </summary>
        private void UpdateJoystickHandle()
        {
            Vector2 direction = _currentPosition;
            float distance = direction.magnitude;

            // 限制在摇杆范围内
            if (distance > joystickRange)
            {
                direction = direction.normalized * joystickRange;
                distance = joystickRange;
            }

            // 更新手柄位置
            joystickHandle.anchoredPosition = direction;

            // 计算输入值
            Vector2 inputValue = direction / joystickRange;

            // 应用死区
            if (enableDeadZone && inputValue.magnitude < deadZone)
            {
                inputValue = Vector2.zero;
            }

            // 更新输入向量
            if (enableSmoothing)
            {
                _smoothedInput = inputValue;
            }
            else
            {
                InputVector = inputValue;
            }
        }

        /// <summary>
        /// 更新平滑输入
        /// </summary>
        private void UpdateSmoothedInput()
        {
            InputVector = Vector2.Lerp(InputVector, _smoothedInput, smoothingFactor * Time.deltaTime);
        }

        /// <summary>
        /// 返回中心位置
        /// </summary>
        private void ReturnToCenter()
        {
            if (joystickHandle.anchoredPosition.magnitude > 0.1f)
            {
                joystickHandle.anchoredPosition = Vector2.Lerp(
                    joystickHandle.anchoredPosition,
                    Vector2.zero,
                    returnSpeed * Time.deltaTime
                );
            }
            else
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
        }

        /// <summary>
        /// 设置摇杆可见性
        /// </summary>
        private void SetJoystickVisibility(bool visible)
        {
            if (backgroundImage != null)
            {
                backgroundImage.gameObject.SetActive(visible && showBackground);
            }

            if (handleImage != null)
            {
                handleImage.gameObject.SetActive(visible);
            }
        }

        /// <summary>
        /// 设置摇杆类型
        /// </summary>
        public void SetJoystickType(JoystickType type)
        {
            joystickType = type;

            if (type == JoystickType.Fixed)
            {
                joystickBackground.anchoredPosition = _startPosition;
                SetJoystickVisibility(true);
            }
            else if (type == JoystickType.Dynamic)
            {
                SetJoystickVisibility(false);
            }
        }

        /// <summary>
        /// 设置摇杆范围
        /// </summary>
        public void SetJoystickRange(float range)
        {
            joystickRange = range;
        }

        /// <summary>
        /// 设置摇杆颜色
        /// </summary>
        public void SetJoystickColor(Color color)
        {
            joystickColor = color;
            SetupVisuals();
        }

        /// <summary>
        /// 启用/禁用摇杆
        /// </summary>
        public void SetEnabled(bool enabled)
        {
            this.enabled = enabled;

            if (!enabled)
            {
                InputVector = Vector2.zero;
                IsPressed = false;
                _isDragging = false;

                if (joystickType == JoystickType.Dynamic)
                {
                    SetJoystickVisibility(false);
                }
            }
        }

        /// <summary>
        /// 重置摇杆状态
        /// </summary>
        public void ResetJoystick()
        {
            InputVector = Vector2.zero;
            IsPressed = false;
            _isDragging = false;

            if (joystickHandle != null)
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
        }

        /// <summary>
        /// 更新摇杆 (由MobileControlsManager调用)
        /// </summary>
        public void UpdateJoystick()
        {
            // 这个方法由MobileControlsManager调用
            // 可以在这里添加额外的更新逻辑
        }

        /// <summary>
        /// 获取当前输入向量
        /// </summary>
        public Vector2 GetInputVector()
        {
            return InputVector;
        }

        /// <summary>
        /// 获取是否正在拖拽
        /// </summary>
        public bool IsDragging()
        {
            return _isDragging;
        }

        /// <summary>
        /// 设置视觉效果启用状态
        /// </summary>
        public void SetVisualEffectsEnabled(bool enabled)
        {
            // 可以在这里控制视觉效果的启用状态
            if (backgroundImage != null)
            {
                backgroundImage.enabled = enabled && showBackground;
            }
            if (handleImage != null)
            {
                handleImage.enabled = enabled;
            }
        }

        /// <summary>
        /// 获取背景RectTransform (用于工厂类)
        /// </summary>
        public RectTransform background
        {
            get { return joystickBackground; }
            set { joystickBackground = value; }
        }

        /// <summary>
        /// 获取手柄RectTransform (用于工厂类)
        /// </summary>
        public RectTransform handle
        {
            get { return joystickHandle; }
            set { joystickHandle = value; }
        }

        #if UNITY_EDITOR
        private void OnValidate()
        {
            if (Application.isPlaying)
            {
                SetupVisuals();
            }
        }
        #endif
    }
}
