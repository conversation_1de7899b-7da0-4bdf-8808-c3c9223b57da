using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Audio;

namespace GooseDuckKill.UI
{
    /// <summary>
    /// 设置管理器
    /// 管理游戏的各种设置选项
    /// </summary>
    public class SettingsManager : MonoBehaviour
    {
        [Header("音频设置")]
        [SerializeField] private AudioMixer audioMixer;
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Slider sfxVolumeSlider;

        [Header("图形设置")]
        [SerializeField] private Dropdown qualityDropdown;
        [SerializeField] private Dropdown resolutionDropdown;
        [SerializeField] private Toggle fullscreenToggle;
        [SerializeField] private Toggle vsyncToggle;

        [Header("游戏设置")]
        [SerializeField] private Slider mouseSensitivitySlider;
        [SerializeField] private Toggle invertMouseToggle;
        [SerializeField] private Dropdown languageDropdown;

        [Header("移动端设置")]
        [SerializeField] private Toggle enableMobileUIToggle;
        [SerializeField] private Slider joystickSizeSlider;
        [SerializeField] private Toggle hapticFeedbackToggle;

        // 设置数据
        private GameSettings gameSettings;
        private Resolution[] availableResolutions;

        // 设置键名
        private const string MASTER_VOLUME_KEY = "MasterVolume";
        private const string MUSIC_VOLUME_KEY = "MusicVolume";
        private const string SFX_VOLUME_KEY = "SFXVolume";
        private const string QUALITY_KEY = "QualityLevel";
        private const string RESOLUTION_KEY = "ResolutionIndex";
        private const string FULLSCREEN_KEY = "Fullscreen";
        private const string VSYNC_KEY = "VSync";
        private const string MOUSE_SENSITIVITY_KEY = "MouseSensitivity";
        private const string INVERT_MOUSE_KEY = "InvertMouse";
        private const string LANGUAGE_KEY = "Language";
        private const string ENABLE_MOBILE_UI_KEY = "EnableMobileUI";
        private const string JOYSTICK_SIZE_KEY = "JoystickSize";
        private const string HAPTIC_FEEDBACK_KEY = "HapticFeedback";

        private void Awake()
        {
            InitializeSettings();
        }

        private void Start()
        {
            SetupUI();
            LoadSettings();
        }

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            gameSettings = new GameSettings();

            // 获取可用分辨率
            availableResolutions = Screen.resolutions;
        }

        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            // 设置音频滑块事件
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(SetMasterVolume);
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(SetMusicVolume);
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(SetSFXVolume);

            // 设置图形选项事件
            if (qualityDropdown != null)
                qualityDropdown.onValueChanged.AddListener(SetQuality);
            if (resolutionDropdown != null)
                resolutionDropdown.onValueChanged.AddListener(SetResolution);
            if (fullscreenToggle != null)
                fullscreenToggle.onValueChanged.AddListener(SetFullscreen);
            if (vsyncToggle != null)
                vsyncToggle.onValueChanged.AddListener(SetVSync);

            // 设置游戏选项事件
            if (mouseSensitivitySlider != null)
                mouseSensitivitySlider.onValueChanged.AddListener(SetMouseSensitivity);
            if (invertMouseToggle != null)
                invertMouseToggle.onValueChanged.AddListener(SetInvertMouse);
            if (languageDropdown != null)
                languageDropdown.onValueChanged.AddListener(SetLanguage);

            // 设置移动端选项事件
            if (enableMobileUIToggle != null)
                enableMobileUIToggle.onValueChanged.AddListener(SetEnableMobileUI);
            if (joystickSizeSlider != null)
                joystickSizeSlider.onValueChanged.AddListener(SetJoystickSize);
            if (hapticFeedbackToggle != null)
                hapticFeedbackToggle.onValueChanged.AddListener(SetHapticFeedback);

            // 设置分辨率下拉菜单选项
            SetupResolutionDropdown();
        }

        /// <summary>
        /// 设置分辨率下拉菜单
        /// </summary>
        private void SetupResolutionDropdown()
        {
            if (resolutionDropdown == null) return;

            resolutionDropdown.ClearOptions();

            var options = new System.Collections.Generic.List<string>();
            for (int i = 0; i < availableResolutions.Length; i++)
            {
                string option = availableResolutions[i].width + " x " + availableResolutions[i].height;
                options.Add(option);
            }

            resolutionDropdown.AddOptions(options);
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        public void LoadSettings()
        {
            // 加载音频设置
            gameSettings.masterVolume = PlayerPrefs.GetFloat(MASTER_VOLUME_KEY, 0.8f);
            gameSettings.musicVolume = PlayerPrefs.GetFloat(MUSIC_VOLUME_KEY, 0.7f);
            gameSettings.sfxVolume = PlayerPrefs.GetFloat(SFX_VOLUME_KEY, 0.8f);

            // 加载图形设置
            gameSettings.qualityLevel = PlayerPrefs.GetInt(QUALITY_KEY, QualitySettings.GetQualityLevel());
            gameSettings.resolutionIndex = PlayerPrefs.GetInt(RESOLUTION_KEY, availableResolutions.Length - 1);
            gameSettings.isFullscreen = PlayerPrefs.GetInt(FULLSCREEN_KEY, 1) == 1;
            gameSettings.enableVSync = PlayerPrefs.GetInt(VSYNC_KEY, 1) == 1;

            // 加载游戏设置
            gameSettings.mouseSensitivity = PlayerPrefs.GetFloat(MOUSE_SENSITIVITY_KEY, 1f);
            gameSettings.invertMouse = PlayerPrefs.GetInt(INVERT_MOUSE_KEY, 0) == 1;
            gameSettings.languageIndex = PlayerPrefs.GetInt(LANGUAGE_KEY, 0);

            // 加载移动端设置
            gameSettings.enableMobileUI = PlayerPrefs.GetInt(ENABLE_MOBILE_UI_KEY, Application.isMobilePlatform ? 1 : 0) == 1;
            gameSettings.joystickSize = PlayerPrefs.GetFloat(JOYSTICK_SIZE_KEY, 120f);
            gameSettings.enableHapticFeedback = PlayerPrefs.GetInt(HAPTIC_FEEDBACK_KEY, 1) == 1;

            // 应用设置到UI
            ApplySettingsToUI();

            // 应用设置到游戏
            ApplySettings();
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        public void SaveSettings()
        {
            // 保存音频设置
            PlayerPrefs.SetFloat(MASTER_VOLUME_KEY, gameSettings.masterVolume);
            PlayerPrefs.SetFloat(MUSIC_VOLUME_KEY, gameSettings.musicVolume);
            PlayerPrefs.SetFloat(SFX_VOLUME_KEY, gameSettings.sfxVolume);

            // 保存图形设置
            PlayerPrefs.SetInt(QUALITY_KEY, gameSettings.qualityLevel);
            PlayerPrefs.SetInt(RESOLUTION_KEY, gameSettings.resolutionIndex);
            PlayerPrefs.SetInt(FULLSCREEN_KEY, gameSettings.isFullscreen ? 1 : 0);
            PlayerPrefs.SetInt(VSYNC_KEY, gameSettings.enableVSync ? 1 : 0);

            // 保存游戏设置
            PlayerPrefs.SetFloat(MOUSE_SENSITIVITY_KEY, gameSettings.mouseSensitivity);
            PlayerPrefs.SetInt(INVERT_MOUSE_KEY, gameSettings.invertMouse ? 1 : 0);
            PlayerPrefs.SetInt(LANGUAGE_KEY, gameSettings.languageIndex);

            // 保存移动端设置
            PlayerPrefs.SetInt(ENABLE_MOBILE_UI_KEY, gameSettings.enableMobileUI ? 1 : 0);
            PlayerPrefs.SetFloat(JOYSTICK_SIZE_KEY, gameSettings.joystickSize);
            PlayerPrefs.SetInt(HAPTIC_FEEDBACK_KEY, gameSettings.enableHapticFeedback ? 1 : 0);

            PlayerPrefs.Save();

            Debug.Log("设置已保存");
        }

        /// <summary>
        /// 应用设置到UI
        /// </summary>
        private void ApplySettingsToUI()
        {
            // 应用音频设置到UI
            if (masterVolumeSlider != null)
                masterVolumeSlider.value = gameSettings.masterVolume;
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = gameSettings.musicVolume;
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = gameSettings.sfxVolume;

            // 应用图形设置到UI
            if (qualityDropdown != null)
                qualityDropdown.value = gameSettings.qualityLevel;
            if (resolutionDropdown != null)
                resolutionDropdown.value = gameSettings.resolutionIndex;
            if (fullscreenToggle != null)
                fullscreenToggle.isOn = gameSettings.isFullscreen;
            if (vsyncToggle != null)
                vsyncToggle.isOn = gameSettings.enableVSync;

            // 应用游戏设置到UI
            if (mouseSensitivitySlider != null)
                mouseSensitivitySlider.value = gameSettings.mouseSensitivity;
            if (invertMouseToggle != null)
                invertMouseToggle.isOn = gameSettings.invertMouse;
            if (languageDropdown != null)
                languageDropdown.value = gameSettings.languageIndex;

            // 应用移动端设置到UI
            if (enableMobileUIToggle != null)
                enableMobileUIToggle.isOn = gameSettings.enableMobileUI;
            if (joystickSizeSlider != null)
                joystickSizeSlider.value = gameSettings.joystickSize;
            if (hapticFeedbackToggle != null)
                hapticFeedbackToggle.isOn = gameSettings.enableHapticFeedback;
        }

        /// <summary>
        /// 应用设置到游戏
        /// </summary>
        private void ApplySettings()
        {
            // 应用音频设置
            var simpleAudioManager = SimpleAudioManager.Instance;
            if (simpleAudioManager != null)
            {
                simpleAudioManager.SetMasterVolume(gameSettings.masterVolume);
                simpleAudioManager.SetMusicVolume(gameSettings.musicVolume);
                simpleAudioManager.SetSFXVolume(gameSettings.sfxVolume);
            }
            else if (audioMixer != null)
            {
                audioMixer.SetFloat("MasterVolume", Mathf.Log10(gameSettings.masterVolume) * 20);
                audioMixer.SetFloat("MusicVolume", Mathf.Log10(gameSettings.musicVolume) * 20);
                audioMixer.SetFloat("SFXVolume", Mathf.Log10(gameSettings.sfxVolume) * 20);
            }

            // 应用图形设置
            QualitySettings.SetQualityLevel(gameSettings.qualityLevel);

            if (gameSettings.resolutionIndex < availableResolutions.Length)
            {
                Resolution resolution = availableResolutions[gameSettings.resolutionIndex];
                Screen.SetResolution(resolution.width, resolution.height, gameSettings.isFullscreen);
            }

            QualitySettings.vSyncCount = gameSettings.enableVSync ? 1 : 0;
        }

        // 设置方法
        public void SetMasterVolume(float volume)
        {
            gameSettings.masterVolume = volume;

            var simpleAudioManager = SimpleAudioManager.Instance;
            if (simpleAudioManager != null)
            {
                simpleAudioManager.SetMasterVolume(volume);
            }
            else if (audioMixer != null)
            {
                audioMixer.SetFloat("MasterVolume", Mathf.Log10(volume) * 20);
            }
        }

        public void SetMusicVolume(float volume)
        {
            gameSettings.musicVolume = volume;

            var simpleAudioManager = SimpleAudioManager.Instance;
            if (simpleAudioManager != null)
            {
                simpleAudioManager.SetMusicVolume(volume);
            }
            else if (audioMixer != null)
            {
                audioMixer.SetFloat("MusicVolume", Mathf.Log10(volume) * 20);
            }
        }

        public void SetSFXVolume(float volume)
        {
            gameSettings.sfxVolume = volume;

            var simpleAudioManager = SimpleAudioManager.Instance;
            if (simpleAudioManager != null)
            {
                simpleAudioManager.SetSFXVolume(volume);
            }
            else if (audioMixer != null)
            {
                audioMixer.SetFloat("SFXVolume", Mathf.Log10(volume) * 20);
            }
        }

        public void SetQuality(int qualityIndex)
        {
            gameSettings.qualityLevel = qualityIndex;
            QualitySettings.SetQualityLevel(qualityIndex);
        }

        public void SetResolution(int resolutionIndex)
        {
            gameSettings.resolutionIndex = resolutionIndex;
            if (resolutionIndex < availableResolutions.Length)
            {
                Resolution resolution = availableResolutions[resolutionIndex];
                Screen.SetResolution(resolution.width, resolution.height, gameSettings.isFullscreen);
            }
        }

        public void SetFullscreen(bool isFullscreen)
        {
            gameSettings.isFullscreen = isFullscreen;
            Screen.fullScreen = isFullscreen;
        }

        public void SetVSync(bool enableVSync)
        {
            gameSettings.enableVSync = enableVSync;
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;
        }

        public void SetMouseSensitivity(float sensitivity)
        {
            gameSettings.mouseSensitivity = sensitivity;
        }

        public void SetInvertMouse(bool invert)
        {
            gameSettings.invertMouse = invert;
        }

        public void SetLanguage(int languageIndex)
        {
            gameSettings.languageIndex = languageIndex;
            // 这里可以添加语言切换逻辑
        }

        public void SetEnableMobileUI(bool enable)
        {
            gameSettings.enableMobileUI = enable;
            // 通知MobileUIAdapter更新 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var mobileUIAdapter = FindFirstObjectByType<MobileUIAdapter>();
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.SetMobileUIEnabled(enable);
            }
        }

        public void SetJoystickSize(float size)
        {
            gameSettings.joystickSize = size;
            // 通知虚拟摇杆更新大小 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var virtualJoystick = FindFirstObjectByType<GooseDuckKill.UI.Mobile.SimpleVirtualJoystick>();
            if (virtualJoystick != null)
            {
                virtualJoystick.SetHandleRange(size * 0.4f);
            }
        }

        public void SetHapticFeedback(bool enable)
        {
            gameSettings.enableHapticFeedback = enable;
            // 通知触觉反馈控制器 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            var hapticController = FindFirstObjectByType<GooseDuckKill.UI.Mobile.HapticFeedbackController>();
            if (hapticController != null)
            {
                hapticController.EnableHapticFeedback(enable);
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            gameSettings = new GameSettings();
            ApplySettingsToUI();
            ApplySettings();
        }

        /// <summary>
        /// 获取当前设置
        /// </summary>
        public GameSettings GetCurrentSettings()
        {
            return gameSettings;
        }
    }

    /// <summary>
    /// 游戏设置数据结构
    /// </summary>
    [System.Serializable]
    public class GameSettings
    {
        [Header("音频设置")]
        public float masterVolume = 0.8f;
        public float musicVolume = 0.7f;
        public float sfxVolume = 0.8f;

        [Header("图形设置")]
        public int qualityLevel = 2;
        public int resolutionIndex = 0;
        public bool isFullscreen = true;
        public bool enableVSync = true;

        [Header("游戏设置")]
        public float mouseSensitivity = 1f;
        public bool invertMouse = false;
        public int languageIndex = 0;

        [Header("移动端设置")]
        public bool enableMobileUI = false;
        public float joystickSize = 120f;
        public bool enableHapticFeedback = true;
    }
}
