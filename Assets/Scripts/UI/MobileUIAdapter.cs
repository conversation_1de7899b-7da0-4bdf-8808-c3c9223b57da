using UnityEngine;
using GooseDuckKill.UI.Mobile;

namespace GooseDuckKill.UI
{
    /// <summary>
    /// 移动端UI适配器
    /// 负责在桌面端和移动端之间切换UI模式
    /// </summary>
    public class MobileUIAdapter : MonoBehaviour
    {
        [Header("UI根节点引用")]
        [SerializeField] private GameObject mobileUIRoot;
        [SerializeField] private GameObject desktopUIRoot;

        [Header("适配设置")]
        [SerializeField] private bool autoDetectPlatform = true;
        [SerializeField] private bool allowManualToggle = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.F1;

        [Header("性能设置")]
        [SerializeField] private bool optimizeForMobile = true;
        [SerializeField] private int mobileTargetFrameRate = 60;
        [SerializeField] private int desktopTargetFrameRate = -1; // 不限制

        [Header("调试设置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showPlatformInfo = false;

        // 当前状态
        private bool isMobileUIActive = false;
        private bool isInitialized = false;

        // 组件引用
        private MobileUIRoot mobileUIRootComponent;
        private SettingsManager settingsManager;

        // 事件
        public System.Action<bool> OnUIModeChanged;

        private void Awake()
        {
            InitializeComponents();
        }

        private void Start()
        {
            InitializeAdapter();
        }

        private void Update()
        {
            HandleInput();

            if (showPlatformInfo)
            {
                UpdatePlatformInfo();
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 自动查找UI根节点 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (mobileUIRoot == null)
            {
                var canvas = FindFirstObjectByType<Canvas>();
                if (canvas != null)
                {
                    var mobileRoot = canvas.transform.Find("Mobile UI Root");
                    if (mobileRoot != null)
                    {
                        mobileUIRoot = mobileRoot.gameObject;
                    }
                }
            }

            if (desktopUIRoot == null)
            {
                var canvas = FindFirstObjectByType<Canvas>();
                if (canvas != null)
                {
                    var desktopRoot = canvas.transform.Find("Desktop UI Root");
                    if (desktopRoot != null)
                    {
                        desktopUIRoot = desktopRoot.gameObject;
                    }
                }
            }

            // 获取组件引用
            if (mobileUIRoot != null)
            {
                mobileUIRootComponent = mobileUIRoot.GetComponent<MobileUIRoot>();
            }

            settingsManager = FindFirstObjectByType<SettingsManager>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
        }

        /// <summary>
        /// 初始化适配器
        /// </summary>
        private void InitializeAdapter()
        {
            if (autoDetectPlatform)
            {
                // 根据平台自动选择UI模式
                bool shouldUseMobileUI = Application.isMobilePlatform;

                // 检查设置管理器中的用户偏好
                if (settingsManager != null)
                {
                    var settings = settingsManager.GetCurrentSettings();
                    shouldUseMobileUI = settings.enableMobileUI;
                }

                SetMobileUIEnabled(shouldUseMobileUI);
            }

            isInitialized = true;

            if (enableDebugLog)
            {
                Debug.Log($"MobileUIAdapter 初始化完成 - 移动UI: {isMobileUIActive}");
            }
        }

        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            if (!allowManualToggle) return;

            if (Input.GetKeyDown(toggleKey))
            {
                ToggleMobileUI();
            }
        }

        /// <summary>
        /// 设置移动端UI启用状态
        /// </summary>
        public void SetMobileUIEnabled(bool enabled)
        {
            if (isMobileUIActive == enabled) return;

            isMobileUIActive = enabled;

            // 切换UI根节点
            if (mobileUIRoot != null)
            {
                mobileUIRoot.SetActive(enabled);
            }

            if (desktopUIRoot != null)
            {
                desktopUIRoot.SetActive(!enabled);
            }

            // 应用性能优化
            ApplyPerformanceOptimizations(enabled);

            // 触发事件
            OnUIModeChanged?.Invoke(enabled);

            if (enableDebugLog)
            {
                Debug.Log($"UI模式切换: {(enabled ? "移动端" : "桌面端")}");
            }
        }

        /// <summary>
        /// 切换移动端UI
        /// </summary>
        public void ToggleMobileUI()
        {
            SetMobileUIEnabled(!isMobileUIActive);
        }

        /// <summary>
        /// 应用性能优化
        /// </summary>
        private void ApplyPerformanceOptimizations(bool isMobile)
        {
            if (!optimizeForMobile) return;

            if (isMobile)
            {
                // 移动端优化
                Application.targetFrameRate = mobileTargetFrameRate;

                // 降低渲染质量（如果需要）
                if (QualitySettings.GetQualityLevel() > 2)
                {
                    QualitySettings.SetQualityLevel(2, true);
                }

                // 禁用一些桌面端特效
                DisableDesktopEffects();
            }
            else
            {
                // 桌面端优化
                Application.targetFrameRate = desktopTargetFrameRate;

                // 恢复桌面端特效
                EnableDesktopEffects();
            }
        }

        /// <summary>
        /// 禁用桌面端特效
        /// </summary>
        private void DisableDesktopEffects()
        {
            // 这里可以禁用一些移动端不需要的特效
            // 例如：粒子效果、后处理效果等
        }

        /// <summary>
        /// 启用桌面端特效
        /// </summary>
        private void EnableDesktopEffects()
        {
            // 这里可以启用桌面端的特效
        }

        /// <summary>
        /// 更新平台信息显示
        /// </summary>
        private void UpdatePlatformInfo()
        {
            // 这里可以更新调试信息显示
        }

        /// <summary>
        /// 获取当前UI模式
        /// </summary>
        public bool IsMobileUIActive()
        {
            return isMobileUIActive;
        }

        /// <summary>
        /// 获取平台信息
        /// </summary>
        public string GetPlatformInfo()
        {
            return $"Platform: {Application.platform}, Mobile UI: {isMobileUIActive}, " +
                   $"Screen: {Screen.width}x{Screen.height}, DPI: {Screen.dpi}";
        }

        /// <summary>
        /// 强制刷新UI适配
        /// </summary>
        public void RefreshUIAdaptation()
        {
            if (!isInitialized) return;

            // 重新检测平台
            if (autoDetectPlatform)
            {
                bool shouldUseMobileUI = Application.isMobilePlatform;

                // 检查屏幕尺寸（可能是平板或大屏手机）
                if (Screen.width < 1024 || Screen.height < 768)
                {
                    shouldUseMobileUI = true;
                }

                SetMobileUIEnabled(shouldUseMobileUI);
            }

            // 刷新移动端UI组件
            if (isMobileUIActive && mobileUIRootComponent != null)
            {
                mobileUIRootComponent.ForceUpdateLayout();
            }
        }

        /// <summary>
        /// 屏幕方向变化回调
        /// </summary>
        public void OnOrientationChanged()
        {
            if (isMobileUIActive && mobileUIRootComponent != null)
            {
                bool isLandscape = Screen.width > Screen.height;
                mobileUIRootComponent.OnOrientationChanged(isLandscape);
            }
        }

        /// <summary>
        /// 应用程序焦点变化回调
        /// </summary>
        private void OnApplicationFocus(bool hasFocus)
        {
            if (hasFocus)
            {
                // 应用程序获得焦点时刷新适配
                RefreshUIAdaptation();
            }
        }

        /// <summary>
        /// 应用程序暂停回调
        /// </summary>
        private void OnApplicationPause(bool pauseStatus)
        {
            if (!pauseStatus)
            {
                // 应用程序恢复时刷新适配
                RefreshUIAdaptation();
            }
        }

        /// <summary>
        /// 设置自动检测平台
        /// </summary>
        public void SetAutoDetectPlatform(bool autoDetect)
        {
            autoDetectPlatform = autoDetect;

            if (autoDetect)
            {
                RefreshUIAdaptation();
            }
        }

        /// <summary>
        /// 设置允许手动切换
        /// </summary>
        public void SetAllowManualToggle(bool allow)
        {
            allowManualToggle = allow;
        }

        /// <summary>
        /// 设置切换按键
        /// </summary>
        public void SetToggleKey(KeyCode key)
        {
            toggleKey = key;
        }

        /// <summary>
        /// 获取适配器状态信息
        /// </summary>
        public string GetAdapterStatus()
        {
            return $"Initialized: {isInitialized}, " +
                   $"Mobile UI Active: {isMobileUIActive}, " +
                   $"Auto Detect: {autoDetectPlatform}, " +
                   $"Manual Toggle: {allowManualToggle}";
        }

        private void OnGUI()
        {
            if (!showPlatformInfo) return;

            // 显示平台信息（调试用）
            GUILayout.BeginArea(new Rect(10, 10, 400, 100));
            GUILayout.Label("=== Mobile UI Adapter ===");
            GUILayout.Label(GetPlatformInfo());
            GUILayout.Label(GetAdapterStatus());

            if (allowManualToggle)
            {
                GUILayout.Label($"Press {toggleKey} to toggle UI mode");
            }

            GUILayout.EndArea();
        }
    }
}
