using UnityEngine;

namespace GooseDuckKill.UI
{
    /// <summary>
    /// UI管理器
    /// 统一管理所有UI组件和系统
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("UI组件引用")]
        [SerializeField] private ConnectionUI connectionUI;
        [SerializeField] private SettingsManager settingsManager;
        [SerializeField] private MobileUIAdapter mobileUIAdapter;

        [Header("UI根节点")]
        [SerializeField] private GameObject mobileUIRoot;
        [SerializeField] private GameObject desktopUIRoot;

        [Header("管理设置")]
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private bool enableDebugLog = true;

        // 单例实例
        public static UIManager Instance { get; private set; }

        // 初始化状态
        private bool isInitialized = false;

        // 事件
        public System.Action OnUIInitialized;
        public System.Action<bool> OnUIModeChanged;

        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            if (autoInitialize)
            {
                InitializeUI();
            }
        }

        private void Start()
        {
            if (!isInitialized && autoInitialize)
            {
                InitializeUI();
            }
        }

        /// <summary>
        /// 初始化UI系统
        /// </summary>
        public void InitializeUI()
        {
            if (isInitialized)
            {
                if (enableDebugLog)
                {
                    Debug.LogWarning("UI系统已经初始化过了");
                }
                return;
            }

            // 自动查找组件
            FindUIComponents();

            // 设置组件关联
            SetupComponentReferences();

            // 订阅事件
            SubscribeToEvents();

            isInitialized = true;

            if (enableDebugLog)
            {
                Debug.Log("UI系统初始化完成");
            }

            // 触发初始化完成事件
            OnUIInitialized?.Invoke();
        }

        /// <summary>
        /// 自动查找UI组件
        /// </summary>
        private void FindUIComponents()
        {
            // 查找ConnectionUI - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (connectionUI == null)
            {
                connectionUI = FindFirstObjectByType<ConnectionUI>();
            }

            // 查找SettingsManager - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (settingsManager == null)
            {
                settingsManager = FindFirstObjectByType<SettingsManager>();
            }

            // 查找MobileUIAdapter - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (mobileUIAdapter == null)
            {
                mobileUIAdapter = FindFirstObjectByType<MobileUIAdapter>();
            }

            // 查找UI根节点 - 使用FindFirstObjectByType替代已弃用的FindObjectOfType
            if (mobileUIRoot == null || desktopUIRoot == null)
            {
                var canvas = FindFirstObjectByType<Canvas>();
                if (canvas != null)
                {
                    if (mobileUIRoot == null)
                    {
                        var mobileRoot = canvas.transform.Find("Mobile UI Root");
                        if (mobileRoot != null)
                        {
                            mobileUIRoot = mobileRoot.gameObject;
                        }
                    }

                    if (desktopUIRoot == null)
                    {
                        var desktopRoot = canvas.transform.Find("Desktop UI Root");
                        if (desktopRoot != null)
                        {
                            desktopUIRoot = desktopRoot.gameObject;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 设置组件引用关系
        /// </summary>
        private void SetupComponentReferences()
        {
            // 为MobileUIAdapter设置UI根节点引用
            if (mobileUIAdapter != null)
            {
                // 通过反射或公共方法设置引用
                // 这里假设MobileUIAdapter有公共方法来设置引用
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            // 订阅MobileUIAdapter的UI模式变化事件
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.OnUIModeChanged += HandleUIModeChanged;
            }
        }

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.OnUIModeChanged -= HandleUIModeChanged;
            }
        }

        /// <summary>
        /// 处理UI模式变化
        /// </summary>
        private void HandleUIModeChanged(bool isMobileMode)
        {
            if (enableDebugLog)
            {
                Debug.Log($"UI模式变化: {(isMobileMode ? "移动端" : "桌面端")}");
            }

            // 触发UI模式变化事件
            OnUIModeChanged?.Invoke(isMobileMode);
        }

        /// <summary>
        /// 获取ConnectionUI
        /// </summary>
        public ConnectionUI GetConnectionUI()
        {
            return connectionUI;
        }

        /// <summary>
        /// 获取SettingsManager
        /// </summary>
        public SettingsManager GetSettingsManager()
        {
            return settingsManager;
        }

        /// <summary>
        /// 获取MobileUIAdapter
        /// </summary>
        public MobileUIAdapter GetMobileUIAdapter()
        {
            return mobileUIAdapter;
        }

        /// <summary>
        /// 切换到移动端UI
        /// </summary>
        public void SwitchToMobileUI()
        {
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.SetMobileUIEnabled(true);
            }
        }

        /// <summary>
        /// 切换到桌面端UI
        /// </summary>
        public void SwitchToDesktopUI()
        {
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.SetMobileUIEnabled(false);
            }
        }

        /// <summary>
        /// 切换UI模式
        /// </summary>
        public void ToggleUIMode()
        {
            if (mobileUIAdapter != null)
            {
                mobileUIAdapter.ToggleMobileUI();
            }
        }

        /// <summary>
        /// 显示设置面板
        /// </summary>
        public void ShowSettings()
        {
            // 这里可以显示设置面板
            if (enableDebugLog)
            {
                Debug.Log("显示设置面板");
            }
        }

        /// <summary>
        /// 隐藏设置面板
        /// </summary>
        public void HideSettings()
        {
            // 这里可以隐藏设置面板
            if (enableDebugLog)
            {
                Debug.Log("隐藏设置面板");
            }
        }

        /// <summary>
        /// 显示连接面板
        /// </summary>
        public void ShowConnectionPanel()
        {
            if (connectionUI != null)
            {
                connectionUI.ShowConnectionPanel();
            }
        }

        /// <summary>
        /// 隐藏连接面板
        /// </summary>
        public void HideConnectionPanel()
        {
            if (connectionUI != null)
            {
                connectionUI.HideConnectionPanel();
            }
        }

        /// <summary>
        /// 保存所有设置
        /// </summary>
        public void SaveAllSettings()
        {
            if (settingsManager != null)
            {
                settingsManager.SaveSettings();
            }
        }

        /// <summary>
        /// 加载所有设置
        /// </summary>
        public void LoadAllSettings()
        {
            if (settingsManager != null)
            {
                settingsManager.LoadSettings();
            }
        }

        /// <summary>
        /// 获取UI系统状态信息
        /// </summary>
        public string GetUISystemStatus()
        {
            return $"Initialized: {isInitialized}, " +
                   $"ConnectionUI: {(connectionUI != null ? "OK" : "Missing")}, " +
                   $"SettingsManager: {(settingsManager != null ? "OK" : "Missing")}, " +
                   $"MobileUIAdapter: {(mobileUIAdapter != null ? "OK" : "Missing")}";
        }

        /// <summary>
        /// 检查UI系统完整性
        /// </summary>
        public bool ValidateUISystem()
        {
            bool isValid = true;

            if (connectionUI == null)
            {
                Debug.LogWarning("ConnectionUI 组件缺失");
                isValid = false;
            }

            if (settingsManager == null)
            {
                Debug.LogWarning("SettingsManager 组件缺失");
                isValid = false;
            }

            if (mobileUIAdapter == null)
            {
                Debug.LogWarning("MobileUIAdapter 组件缺失");
                isValid = false;
            }

            return isValid;
        }

        private void OnDestroy()
        {
            // 取消事件订阅
            UnsubscribeFromEvents();

            // 清理单例引用
            if (Instance == this)
            {
                Instance = null;
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (!pauseStatus)
            {
                // 应用程序恢复时重新验证UI系统
                if (isInitialized)
                {
                    ValidateUISystem();
                }
            }
        }
    }
}
