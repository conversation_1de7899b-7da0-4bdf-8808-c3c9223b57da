using UnityEngine;
using UnityEngine.Audio;

namespace GooseDuckKill.UI
{
    /// <summary>
    /// 简化版音频管理器
    /// 避免命名空间冲突，提供基础音频管理功能
    /// </summary>
    public class SimpleAudioManager : MonoBehaviour
    {
        [Header("音频混合器")]
        [SerializeField] private AudioMixer audioMixer;
        
        [Header("音频源")]
        [SerializeField] private AudioSource backgroundMusicSource;
        [SerializeField] private AudioSource uiSoundEffectsSource;
        
        [Header("音量设置")]
        [SerializeField] private float masterVolume = 1f;
        [SerializeField] private float musicVolume = 0.7f;
        [SerializeField] private float sfxVolume = 0.8f;
        
        // 单例实例
        public static SimpleAudioManager Instance { get; private set; }
        
        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }
            
            InitializeAudioManager();
        }
        
        private void Start()
        {
            ApplyVolumeSettings();
        }
        
        /// <summary>
        /// 初始化音频管理器
        /// </summary>
        private void InitializeAudioManager()
        {
            // 自动查找音频源组件
            if (backgroundMusicSource == null)
            {
                var musicGO = transform.Find("Background Music");
                if (musicGO != null)
                {
                    backgroundMusicSource = musicGO.GetComponent<AudioSource>();
                }
            }
            
            if (uiSoundEffectsSource == null)
            {
                var uiSfxGO = transform.Find("UI Sound Effects");
                if (uiSfxGO != null)
                {
                    uiSoundEffectsSource = uiSfxGO.GetComponent<AudioSource>();
                }
            }
            
            // 如果没有找到，使用默认的音频源
            if (backgroundMusicSource == null)
            {
                backgroundMusicSource = GetComponent<AudioSource>();
                if (backgroundMusicSource == null)
                {
                    backgroundMusicSource = gameObject.AddComponent<AudioSource>();
                }
                backgroundMusicSource.loop = true;
                backgroundMusicSource.playOnAwake = false;
            }
            
            if (uiSoundEffectsSource == null)
            {
                uiSoundEffectsSource = backgroundMusicSource; // 共用音频源
            }
        }
        
        /// <summary>
        /// 播放背景音乐
        /// </summary>
        public void PlayBackgroundMusic(AudioClip musicClip)
        {
            if (musicClip == null || backgroundMusicSource == null) return;
            
            backgroundMusicSource.clip = musicClip;
            backgroundMusicSource.volume = musicVolume;
            backgroundMusicSource.Play();
        }
        
        /// <summary>
        /// 停止背景音乐
        /// </summary>
        public void StopBackgroundMusic()
        {
            if (backgroundMusicSource != null)
            {
                backgroundMusicSource.Stop();
            }
        }
        
        /// <summary>
        /// 播放UI音效
        /// </summary>
        public void PlayUISound(AudioClip soundClip, float volume = 1f)
        {
            if (soundClip == null || uiSoundEffectsSource == null) return;
            
            uiSoundEffectsSource.PlayOneShot(soundClip, volume * sfxVolume);
        }
        
        /// <summary>
        /// 设置主音量
        /// </summary>
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat("MasterVolume", Mathf.Log10(masterVolume) * 20);
            }
        }
        
        /// <summary>
        /// 设置音乐音量
        /// </summary>
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat("MusicVolume", Mathf.Log10(musicVolume) * 20);
            }
            
            if (backgroundMusicSource != null)
            {
                backgroundMusicSource.volume = musicVolume;
            }
        }
        
        /// <summary>
        /// 设置音效音量
        /// </summary>
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat("SFXVolume", Mathf.Log10(sfxVolume) * 20);
            }
        }
        
        /// <summary>
        /// 应用音量设置
        /// </summary>
        public void ApplyVolumeSettings()
        {
            SetMasterVolume(masterVolume);
            SetMusicVolume(musicVolume);
            SetSFXVolume(sfxVolume);
        }
        
        /// <summary>
        /// 静音/取消静音
        /// </summary>
        public void SetMuted(bool muted)
        {
            if (audioMixer != null)
            {
                audioMixer.SetFloat("MasterVolume", muted ? -80f : Mathf.Log10(masterVolume) * 20);
            }
        }
        
        /// <summary>
        /// 获取当前音量设置
        /// </summary>
        public (float master, float music, float sfx) GetVolumeSettings()
        {
            return (masterVolume, musicVolume, sfxVolume);
        }
        
        /// <summary>
        /// 获取当前播放状态
        /// </summary>
        public bool IsPlayingMusic()
        {
            return backgroundMusicSource != null && backgroundMusicSource.isPlaying;
        }
        
        /// <summary>
        /// 获取当前背景音乐
        /// </summary>
        public AudioClip GetCurrentMusic()
        {
            return backgroundMusicSource != null ? backgroundMusicSource.clip : null;
        }
        
        // 便捷方法
        public void PlayButtonClick()
        {
            // 可以在这里添加默认的按钮点击音效
            Debug.Log("Button click sound played");
        }
        
        public void PlayNotification()
        {
            // 可以在这里添加默认的通知音效
            Debug.Log("Notification sound played");
        }
        
        /// <summary>
        /// 暂停所有音频
        /// </summary>
        public void PauseAll()
        {
            if (backgroundMusicSource != null)
                backgroundMusicSource.Pause();
        }
        
        /// <summary>
        /// 恢复所有音频
        /// </summary>
        public void ResumeAll()
        {
            if (backgroundMusicSource != null)
                backgroundMusicSource.UnPause();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                PauseAll();
            }
            else
            {
                ResumeAll();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                PauseAll();
            }
            else
            {
                ResumeAll();
            }
        }
    }
}
