using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using CustomNetworking.Core;
using GooseDuckKill.Network;

namespace GooseDuckKill.UI
{
    /// <summary>
    /// 连接UI - 提供连接、创建房间和加入房间的用户界面
    /// </summary>
    public class ConnectionUI : MonoBehaviour
    {
        [Header("Panels")]
        [SerializeField] private GameObject connectPanel;
        [SerializeField] private GameObject roomListPanel;
        [SerializeField] private GameObject createRoomPanel;
        [SerializeField] private GameObject loadingPanel;

        [Header("Connect Panel")]
        [SerializeField] private Button connectButton;
        [SerializeField] private TMP_InputField playerNameInput;

        [Header("Room List Panel")]
        [SerializeField] private Transform roomListContent;
        [SerializeField] private GameObject roomEntryPrefab;
        [SerializeField] private Button refreshButton;
        [SerializeField] private Button createRoomButton;
        [SerializeField] private Button backFromRoomListButton;

        [Header("Create Room Panel")]
        [SerializeField] private TMP_InputField roomNameInput;
        [SerializeField] private TMP_InputField maxPlayersInput;
        [SerializeField] private Toggle isPublicToggle;
        [SerializeField] private Button createButton;
        [SerializeField] private Button backFromCreateRoomButton;

        [Header("Loading Panel")]
        [SerializeField] private TextMeshProUGUI loadingText;

        // 状态
        private bool _isConnected;
        private List<RoomInfo> _roomList = new List<RoomInfo>();

        private void Awake()
        {
            // 确保UI初始化正确
            SetupUI();
        }

        private void OnEnable()
        {
            // 订阅网络事件
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            // 取消订阅网络事件
            UnsubscribeFromEvents();
        }

        /// <summary>
        /// 设置UI界面
        /// </summary>
        private void SetupUI()
        {
            // 连接按钮
            if (connectButton != null)
            {
                connectButton.onClick.AddListener(OnConnectClicked);
            }

            // 刷新房间列表按钮
            if (refreshButton != null)
            {
                refreshButton.onClick.AddListener(OnRefreshClicked);
            }

            // 创建房间按钮
            if (createRoomButton != null)
            {
                createRoomButton.onClick.AddListener(OnCreateRoomPanelClicked);
            }

            // 返回按钮（房间列表）
            if (backFromRoomListButton != null)
            {
                backFromRoomListButton.onClick.AddListener(OnBackFromRoomListClicked);
            }

            // 创建按钮
            if (createButton != null)
            {
                createButton.onClick.AddListener(OnCreateButtonClicked);
            }

            // 返回按钮（创建房间）
            if (backFromCreateRoomButton != null)
            {
                backFromCreateRoomButton.onClick.AddListener(OnBackFromCreateRoomClicked);
            }

            // 默认显示连接面板
            ShowPanel(connectPanel);
        }

        /// <summary>
        /// 订阅网络事件
        /// </summary>
        private void SubscribeToEvents()
        {
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.OnConnectedToServerEvent += HandleConnectedToServer;
                NetworkManager.Instance.OnDisconnectedFromServerEvent += HandleDisconnectedFromServer;
                NetworkManager.Instance.OnRoomListUpdated += HandleRoomListUpdated;
                NetworkManager.Instance.OnErrorOccurred += HandleErrorOccurred;
            }

            GooseDuckKill.Network.NetworkEvents.OnConnecting += HandleConnecting;
            GooseDuckKill.Network.NetworkEvents.OnConnected += HandleConnected;
            GooseDuckKill.Network.NetworkEvents.OnRoomJoined += HandleRoomJoined;
        }

        /// <summary>
        /// 取消订阅网络事件
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.OnConnectedToServerEvent -= HandleConnectedToServer;
                NetworkManager.Instance.OnDisconnectedFromServerEvent -= HandleDisconnectedFromServer;
                NetworkManager.Instance.OnRoomListUpdated -= HandleRoomListUpdated;
                NetworkManager.Instance.OnErrorOccurred -= HandleErrorOccurred;
            }

            GooseDuckKill.Network.NetworkEvents.OnConnecting -= HandleConnecting;
            GooseDuckKill.Network.NetworkEvents.OnConnected -= HandleConnected;
            GooseDuckKill.Network.NetworkEvents.OnRoomJoined -= HandleRoomJoined;
        }

        /// <summary>
        /// 连接按钮点击
        /// </summary>
        private void OnConnectClicked()
        {
            // 保存玩家名称
            if (playerNameInput != null && !string.IsNullOrEmpty(playerNameInput.text))
            {
                PlayerPrefs.SetString("PlayerName", playerNameInput.text);
                PlayerPrefs.Save();
            }

            // 显示加载面板
            ShowPanel(loadingPanel);
            loadingText.text = "连接中...";

            // 连接到服务器
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.ConnectToServer();
                GooseDuckKill.Network.NetworkEvents.RaiseConnecting();
            }
        }

        /// <summary>
        /// 刷新按钮点击
        /// </summary>
        private void OnRefreshClicked()
        {
            // 刷新房间列表
            if (NetworkManager.Instance != null)
            {
                ShowPanel(loadingPanel);
                loadingText.text = "刷新房间列表...";

                NetworkManager.Instance.StartRoomList();
            }
        }

        /// <summary>
        /// 创建房间面板按钮点击
        /// </summary>
        private void OnCreateRoomPanelClicked()
        {
            ShowPanel(createRoomPanel);
        }

        /// <summary>
        /// 创建房间按钮点击
        /// </summary>
        private void OnCreateButtonClicked()
        {
            string roomName = roomNameInput.text;
            if (string.IsNullOrEmpty(roomName))
            {
                roomName = "Room_" + Random.Range(1000, 10000);
                roomNameInput.text = roomName;
            }

            int maxPlayers = 16;
            if (!string.IsNullOrEmpty(maxPlayersInput.text))
            {
                if (int.TryParse(maxPlayersInput.text, out int parsedMaxPlayers))
                {
                    maxPlayers = Mathf.Clamp(parsedMaxPlayers, 2, 16);
                }
            }

            bool isPublic = isPublicToggle.isOn;

            // 显示加载面板
            ShowPanel(loadingPanel);
            loadingText.text = "创建房间...";

            // 创建房间
            if (NetworkManager.Instance != null)
            {
                var roomManager = NetworkManager.Instance.GetComponent<RoomManager>();
                if (roomManager != null)
                {
                    roomManager.CreateRoom(roomName, maxPlayers, isPublic);
                }
                else
                {
                    NetworkManager.Instance.CreateRoom(roomName);
                }
            }
        }

        /// <summary>
        /// 返回按钮点击（房间列表）
        /// </summary>
        private void OnBackFromRoomListClicked()
        {
            ShowPanel(connectPanel);
        }

        /// <summary>
        /// 返回按钮点击（创建房间）
        /// </summary>
        private void OnBackFromCreateRoomClicked()
        {
            ShowPanel(roomListPanel);
        }

        /// <summary>
        /// 加入房间按钮点击
        /// </summary>
        private void OnJoinRoomClicked(string roomName)
        {
            // 显示加载面板
            ShowPanel(loadingPanel);
            loadingText.text = "加入房间...";

            // 加入房间
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.JoinRoom(roomName);
            }
        }

        /// <summary>
        /// 更新房间列表
        /// </summary>
        private void UpdateRoomList(List<RoomInfo> roomList)
        {
            _roomList = roomList;

            // 清空当前房间列表
            if (roomListContent != null)
            {
                foreach (Transform child in roomListContent)
                {
                    Destroy(child.gameObject);
                }

                // 添加新的房间条目
                if (roomEntryPrefab != null)
                {
                    foreach (var room in roomList)
                    {
                        GameObject entryObj = Instantiate(roomEntryPrefab, roomListContent);
                        RoomEntry entry = entryObj.GetComponent<RoomEntry>();

                        if (entry != null)
                        {
                            entry.Initialize(room.Name, room.PlayerCount, room.MaxPlayers);
                            Button joinButton = entry.GetJoinButton();

                            if (joinButton != null)
                            {
                                joinButton.onClick.AddListener(() => OnJoinRoomClicked(room.Name));
                            }
                        }
                    }
                }
            }

            // 如果没有房间，显示提示
            if (roomList.Count == 0)
            {
                GameObject entryObj = Instantiate(roomEntryPrefab, roomListContent);
                RoomEntry entry = entryObj.GetComponent<RoomEntry>();

                if (entry != null)
                {
                    entry.Initialize("没有可用的房间", 0, 0, false);
                }
            }

            // 显示房间列表面板
            ShowPanel(roomListPanel);
        }

        /// <summary>
        /// 显示指定面板
        /// </summary>
        private void ShowPanel(GameObject panel)
        {
            // 隐藏所有面板
            if (connectPanel != null) connectPanel.SetActive(false);
            if (roomListPanel != null) roomListPanel.SetActive(false);
            if (createRoomPanel != null) createRoomPanel.SetActive(false);
            if (loadingPanel != null) loadingPanel.SetActive(false);

            // 显示指定面板
            if (panel != null)
            {
                panel.SetActive(true);
            }
        }

        #region 事件处理器

        private void HandleConnecting()
        {
            ShowPanel(loadingPanel);
            loadingText.text = "正在连接到服务器...";
        }

        private void HandleConnected()
        {
            _isConnected = true;

            // 连接成功后显示房间列表
            ShowPanel(roomListPanel);

            // 刷新房间列表
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.StartRoomList();
            }
        }

        private void HandleConnectedToServer()
        {
            _isConnected = true;

            // 连接成功后显示房间列表
            ShowPanel(roomListPanel);

            // 刷新房间列表
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.StartRoomList();
            }
        }

        private void HandleDisconnectedFromServer(NetDisconnectReason reason)
        {
            _isConnected = false;

            // 断开连接后返回连接面板
            ShowPanel(connectPanel);
        }

        private void HandleRoomListUpdated(List<SessionInfo> roomList)
        {
            // 将SessionInfo列表转换为RoomInfo列表
            List<RoomInfo> roomInfoList = RoomInfo.FromSessionInfoList(roomList);
            UpdateRoomList(roomInfoList);
        }

        private void HandleRoomJoined(RoomInfo roomInfo)
        {
            // 房间加入成功，游戏会自动加载下一个场景
            Debug.Log($"Joined room: {roomInfo.Name}");
        }

        private void HandleErrorOccurred(string errorMessage)
        {
            // 显示错误信息
            loadingText.text = errorMessage;

            // 3秒后返回到之前的界面
            Invoke("ShowPreviousPanel", 3f);
        }

        private void ShowPreviousPanel()
        {
            if (_isConnected)
            {
                ShowPanel(roomListPanel);
            }
            else
            {
                ShowPanel(connectPanel);
            }
        }

        /// <summary>
        /// 显示连接面板
        /// </summary>
        public void ShowConnectionPanel()
        {
            ShowPanel(connectPanel);
        }

        /// <summary>
        /// 隐藏连接面板
        /// </summary>
        public void HideConnectionPanel()
        {
            // 隐藏所有面板
            if (connectPanel != null) connectPanel.SetActive(false);
            if (roomListPanel != null) roomListPanel.SetActive(false);
            if (createRoomPanel != null) createRoomPanel.SetActive(false);
            if (loadingPanel != null) loadingPanel.SetActive(false);
        }

        #endregion
    }

    /// <summary>
    /// 房间条目组件 - 用于显示房间信息
    /// </summary>
    public class RoomEntry : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI roomNameText;
        [SerializeField] private TextMeshProUGUI playerCountText;
        [SerializeField] private Button joinButton;

        public void Initialize(string roomName, int playerCount, int maxPlayers, bool joinable = true)
        {
            if (roomNameText != null)
            {
                roomNameText.text = roomName;
            }

            if (playerCountText != null)
            {
                playerCountText.text = $"{playerCount}/{maxPlayers}";
            }

            if (joinButton != null)
            {
                joinButton.gameObject.SetActive(joinable);
            }
        }

        public Button GetJoinButton()
        {
            return joinButton;
        }
    }
}
