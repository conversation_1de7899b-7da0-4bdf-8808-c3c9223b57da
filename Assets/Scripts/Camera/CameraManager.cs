using UnityEngine;
using UnityEngine.SceneManagement;

namespace GooseDuckKill.CameraSystem
{
    /// <summary>
    /// 相机管理器
    /// 统一管理不同场景的相机设置和切换
    /// </summary>
    public class CameraManager : MonoBehaviour
    {
        [Header("场景相机设置")]
        [SerializeField] private CameraSettings mainMenuSettings;
        [SerializeField] private CameraSettings lobbySettings;
        [SerializeField] private CameraSettings gameSettings;

        [Header("过渡设置")]
        [SerializeField] private float transitionDuration = 1f;
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        private UnityEngine.Camera currentCamera;
        private CameraSettings currentSettings;
        private bool isTransitioning = false;

        [System.Serializable]
        public class CameraSettings
        {
            [Header("基础设置")]
            public bool orthographic = true;
            public float orthographicSize = 5f;
            public float fieldOfView = 60f;
            public Color backgroundColor = Color.black;
            public CameraClearFlags clearFlags = CameraClearFlags.SolidColor;

            [Header("渲染设置")]
            public float nearClipPlane = 0.3f;
            public float farClipPlane = 1000f;
            public int depth = 0;
            public LayerMask cullingMask = -1;

            [Header("质量设置")]
            public bool allowHDR = true;
            public bool allowMSAA = true;
            public bool allowDynamicResolution = false;
        }

        private void Awake()
        {
            // 确保只有一个相机管理器
            if (FindObjectsOfType<CameraManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }

            DontDestroyOnLoad(gameObject);
        }

        private void Start()
        {
            currentCamera = UnityEngine.Camera.main;
            if (currentCamera == null)
                currentCamera = FindObjectOfType<UnityEngine.Camera>();

            // 根据当前场景应用设置
            ApplySettingsForCurrentScene();

            // 监听场景切换
            SceneManager.sceneLoaded += OnSceneLoaded;
        }

        private void OnDestroy()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }

        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            // 获取新场景的主相机
            currentCamera = UnityEngine.Camera.main;
            if (currentCamera == null)
                currentCamera = FindObjectOfType<UnityEngine.Camera>();

            // 应用对应场景的设置
            ApplySettingsForCurrentScene();
        }

        /// <summary>
        /// 根据当前场景应用相机设置
        /// </summary>
        private void ApplySettingsForCurrentScene()
        {
            string sceneName = SceneManager.GetActiveScene().name;
            CameraSettings settings = GetSettingsForScene(sceneName);

            if (settings != null)
            {
                ApplyCameraSettings(settings);
            }
        }

        /// <summary>
        /// 获取指定场景的相机设置
        /// </summary>
        private CameraSettings GetSettingsForScene(string sceneName)
        {
            switch (sceneName.ToLower())
            {
                case "mainmenu":
                case "main menu":
                    return mainMenuSettings;

                case "lobby":
                    return lobbySettings;

                case "skeld":
                case "mira":
                case "polus":
                    return gameSettings;

                default:
                    return mainMenuSettings; // 默认使用主菜单设置
            }
        }

        /// <summary>
        /// 应用相机设置
        /// </summary>
        public void ApplyCameraSettings(CameraSettings settings)
        {
            if (currentCamera == null || settings == null) return;

            currentSettings = settings;

            // 基础设置
            currentCamera.orthographic = settings.orthographic;
            currentCamera.orthographicSize = settings.orthographicSize;
            currentCamera.fieldOfView = settings.fieldOfView;
            currentCamera.backgroundColor = settings.backgroundColor;
            currentCamera.clearFlags = settings.clearFlags;

            // 渲染设置
            currentCamera.nearClipPlane = settings.nearClipPlane;
            currentCamera.farClipPlane = settings.farClipPlane;
            currentCamera.depth = settings.depth;
            currentCamera.cullingMask = settings.cullingMask;

            // 质量设置
            currentCamera.allowHDR = settings.allowHDR;
            currentCamera.allowMSAA = settings.allowMSAA;
            currentCamera.allowDynamicResolution = settings.allowDynamicResolution;

            Debug.Log($"已应用相机设置: {SceneManager.GetActiveScene().name}");
        }

        /// <summary>
        /// 平滑过渡到新的相机设置
        /// </summary>
        public void TransitionToSettings(CameraSettings newSettings)
        {
            if (isTransitioning || currentCamera == null) return;

            StartCoroutine(SmoothTransition(currentSettings, newSettings));
        }

        private System.Collections.IEnumerator SmoothTransition(CameraSettings from, CameraSettings to)
        {
            isTransitioning = true;
            float elapsed = 0f;

            while (elapsed < transitionDuration)
            {
                elapsed += Time.deltaTime;
                float progress = transitionCurve.Evaluate(elapsed / transitionDuration);

                // 插值相机参数
                if (from.orthographic && to.orthographic)
                {
                    currentCamera.orthographicSize = Mathf.Lerp(
                        from.orthographicSize, to.orthographicSize, progress);
                }
                else if (!from.orthographic && !to.orthographic)
                {
                    currentCamera.fieldOfView = Mathf.Lerp(
                        from.fieldOfView, to.fieldOfView, progress);
                }

                currentCamera.backgroundColor = Color.Lerp(
                    from.backgroundColor, to.backgroundColor, progress);

                yield return null;
            }

            // 应用最终设置
            ApplyCameraSettings(to);
            isTransitioning = false;
        }

        /// <summary>
        /// 获取当前相机
        /// </summary>
        public UnityEngine.Camera GetCurrentCamera()
        {
            return currentCamera;
        }

        /// <summary>
        /// 设置相机跟随目标
        /// </summary>
        public void SetFollowTarget(Transform target)
        {
            var followScript = currentCamera.GetComponent<CameraFollow>();
            if (followScript != null)
            {
                followScript.SetTarget(target);
            }
        }

        /// <summary>
        /// 重置相机设置
        /// </summary>
        public void ResetCamera()
        {
            ApplySettingsForCurrentScene();
        }

        /// <summary>
        /// 获取推荐的移动端设置
        /// </summary>
        public static CameraSettings GetMobileOptimizedSettings()
        {
            return new CameraSettings
            {
                orthographic = true,
                orthographicSize = 6f, // 移动端稍大一些
                backgroundColor = Color.black,
                clearFlags = CameraClearFlags.SolidColor,
                allowHDR = false, // 移动端关闭HDR
                allowMSAA = false, // 移动端关闭MSAA
                allowDynamicResolution = true // 启用动态分辨率
            };
        }

        /// <summary>
        /// 获取推荐的桌面端设置
        /// </summary>
        public static CameraSettings GetDesktopOptimizedSettings()
        {
            return new CameraSettings
            {
                orthographic = true,
                orthographicSize = 5f,
                backgroundColor = Color.black,
                clearFlags = CameraClearFlags.SolidColor,
                allowHDR = true,
                allowMSAA = true,
                allowDynamicResolution = false
            };
        }
    }
}
