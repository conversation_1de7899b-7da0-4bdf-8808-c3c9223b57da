using UnityEngine;

namespace GooseDuckKill.CameraSystem
{
    /// <summary>
    /// MainMenu 场景相机控制器
    /// 提供动态相机效果，包括浮动、缩放动画和过渡效果
    /// </summary>
    public class MainMenuCameraController : MonoBehaviour
    {
        [Header("相机基础设置")]
        [SerializeField] private UnityEngine.Camera mainCamera;
        [SerializeField] private float orthographicSize = 5f;
        [SerializeField] private Color backgroundColor = Color.black;

        [Header("浮动效果")]
        [SerializeField] private bool enableFloating = true;
        [SerializeField] private float floatAmplitude = 0.1f;
        [SerializeField] private float floatSpeed = 1f;
        [SerializeField] private Vector3 floatDirection = Vector3.up;

        [Header("缩放动画")]
        [SerializeField] private bool enableZoomAnimation = false;
        [SerializeField] private float zoomAmplitude = 0.5f;
        [SerializeField] private float zoomSpeed = 0.8f;
        [SerializeField] private float minZoom = 4.5f;
        [SerializeField] private float maxZoom = 5.5f;

        [Header("旋转效果")]
        [SerializeField] private bool enableRotation = false;
        [SerializeField] private float rotationSpeed = 5f;
        [SerializeField] private Vector3 rotationAxis = Vector3.forward;

        [Header("过渡动画")]
        [SerializeField] private bool enableTransitionEffects = true;
        [SerializeField] private float transitionDuration = 2f;
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("响应式设置")]
        [SerializeField] private bool adaptToScreenSize = true;
        [SerializeField] private float baseAspectRatio = 16f / 9f;
        [SerializeField] private float aspectRatioMultiplier = 1f;

        // 私有变量
        private Vector3 originalPosition;
        private float originalOrthographicSize;
        private Quaternion originalRotation;
        private float transitionTimer = 0f;
        private bool isTransitioning = false;

        // 动画状态
        private float floatTimer = 0f;
        private float zoomTimer = 0f;
        private float rotationTimer = 0f;

        private void Awake()
        {
            // 获取相机组件
            if (mainCamera == null)
                mainCamera = GetComponent<UnityEngine.Camera>();

            if (mainCamera == null)
                mainCamera = UnityEngine.Camera.main;
        }

        private void Start()
        {
            InitializeCamera();
            StartTransitionEffect();
        }

        private void Update()
        {
            UpdateTransitionEffect();
            UpdateFloatingEffect();
            UpdateZoomAnimation();
            UpdateRotationEffect();
            UpdateResponsiveSettings();
        }

        /// <summary>
        /// 初始化相机设置
        /// </summary>
        private void InitializeCamera()
        {
            if (mainCamera == null) return;

            // 保存原始值
            originalPosition = transform.position;
            originalOrthographicSize = orthographicSize;
            originalRotation = transform.rotation;

            // 设置相机基础参数
            mainCamera.orthographic = true;
            mainCamera.orthographicSize = orthographicSize;
            mainCamera.backgroundColor = backgroundColor;
            mainCamera.clearFlags = CameraClearFlags.SolidColor;

            // 确保有 Audio Listener
            if (GetComponent<AudioListener>() == null)
            {
                gameObject.AddComponent<AudioListener>();
            }

            Debug.Log("MainMenu 相机初始化完成");
        }

        /// <summary>
        /// 启动过渡效果
        /// </summary>
        private void StartTransitionEffect()
        {
            if (!enableTransitionEffects) return;

            isTransitioning = true;
            transitionTimer = 0f;

            // 设置初始状态（稍微缩小）
            if (mainCamera != null)
            {
                mainCamera.orthographicSize = originalOrthographicSize * 1.2f;
            }
        }

        /// <summary>
        /// 更新过渡效果
        /// </summary>
        private void UpdateTransitionEffect()
        {
            if (!isTransitioning || !enableTransitionEffects) return;

            transitionTimer += Time.deltaTime;
            float progress = transitionTimer / transitionDuration;

            if (progress >= 1f)
            {
                progress = 1f;
                isTransitioning = false;
            }

            // 应用过渡曲线
            float curveValue = transitionCurve.Evaluate(progress);

            // 平滑过渡到目标大小
            if (mainCamera != null)
            {
                float startSize = originalOrthographicSize * 1.2f;
                float targetSize = originalOrthographicSize;
                mainCamera.orthographicSize = Mathf.Lerp(startSize, targetSize, curveValue);
            }
        }

        /// <summary>
        /// 更新浮动效果
        /// </summary>
        private void UpdateFloatingEffect()
        {
            if (!enableFloating) return;

            floatTimer += Time.deltaTime * floatSpeed;

            Vector3 floatOffset = floatDirection.normalized * Mathf.Sin(floatTimer) * floatAmplitude;
            transform.position = originalPosition + floatOffset;
        }

        /// <summary>
        /// 更新缩放动画
        /// </summary>
        private void UpdateZoomAnimation()
        {
            if (!enableZoomAnimation || mainCamera == null || isTransitioning) return;

            zoomTimer += Time.deltaTime * zoomSpeed;

            float zoomValue = Mathf.Sin(zoomTimer) * zoomAmplitude;
            float targetSize = originalOrthographicSize + zoomValue;
            targetSize = Mathf.Clamp(targetSize, minZoom, maxZoom);

            mainCamera.orthographicSize = targetSize;
        }

        /// <summary>
        /// 更新旋转效果
        /// </summary>
        private void UpdateRotationEffect()
        {
            if (!enableRotation) return;

            rotationTimer += Time.deltaTime * rotationSpeed;

            Quaternion rotationOffset = Quaternion.AngleAxis(
                Mathf.Sin(rotationTimer) * 2f, rotationAxis);

            transform.rotation = originalRotation * rotationOffset;
        }

        /// <summary>
        /// 更新响应式设置
        /// </summary>
        private void UpdateResponsiveSettings()
        {
            if (!adaptToScreenSize || mainCamera == null) return;

            float currentAspectRatio = (float)Screen.width / Screen.height;
            float aspectDifference = currentAspectRatio / baseAspectRatio;

            // 根据屏幕比例调整相机大小
            if (!enableZoomAnimation && !isTransitioning)
            {
                mainCamera.orthographicSize = originalOrthographicSize * aspectDifference * aspectRatioMultiplier;
            }
        }

        /// <summary>
        /// 设置相机效果开关
        /// </summary>
        public void SetFloatingEnabled(bool enabled)
        {
            enableFloating = enabled;
            if (!enabled)
            {
                transform.position = originalPosition;
            }
        }

        /// <summary>
        /// 设置缩放动画开关
        /// </summary>
        public void SetZoomAnimationEnabled(bool enabled)
        {
            enableZoomAnimation = enabled;
            if (!enabled && mainCamera != null)
            {
                mainCamera.orthographicSize = originalOrthographicSize;
            }
        }

        /// <summary>
        /// 设置旋转效果开关
        /// </summary>
        public void SetRotationEnabled(bool enabled)
        {
            enableRotation = enabled;
            if (!enabled)
            {
                transform.rotation = originalRotation;
            }
        }

        /// <summary>
        /// 重置相机到初始状态
        /// </summary>
        public void ResetCamera()
        {
            transform.position = originalPosition;
            transform.rotation = originalRotation;

            if (mainCamera != null)
            {
                mainCamera.orthographicSize = originalOrthographicSize;
            }

            floatTimer = 0f;
            zoomTimer = 0f;
            rotationTimer = 0f;
        }

        /// <summary>
        /// 平滑过渡到新的相机大小
        /// </summary>
        public void TransitionToSize(float newSize, float duration = 1f)
        {
            if (mainCamera != null)
            {
                StartCoroutine(SmoothTransitionToSize(newSize, duration));
            }
        }

        private System.Collections.IEnumerator SmoothTransitionToSize(float targetSize, float duration)
        {
            float startSize = mainCamera.orthographicSize;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;

                mainCamera.orthographicSize = Mathf.Lerp(startSize, targetSize,
                    transitionCurve.Evaluate(progress));

                yield return null;
            }

            mainCamera.orthographicSize = targetSize;
            originalOrthographicSize = targetSize;
        }

        private void OnValidate()
        {
            // 在编辑器中实时预览效果
            if (Application.isPlaying && mainCamera != null)
            {
                mainCamera.orthographicSize = orthographicSize;
                mainCamera.backgroundColor = backgroundColor;
            }
        }
    }
}
