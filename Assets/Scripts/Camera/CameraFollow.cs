using UnityEngine;

namespace GooseDuckKill.CameraSystem
{
    /// <summary>
    /// 相机跟随脚本
    /// 用于游戏场景中跟随玩家移动
    /// </summary>
    public class CameraFollow : MonoBehaviour
    {
        [Header("跟随设置")]
        [SerializeField] private Transform target;
        [SerializeField] private float followSpeed = 5f;
        [SerializeField] private Vector3 offset = new Vector3(0, 0, -10);

        [Header("平滑设置")]
        [SerializeField] private bool useSmoothDamping = true;
        [SerializeField] private float smoothTime = 0.3f;
        [SerializeField] private float maxSpeed = 10f;

        [Header("边界限制")]
        [SerializeField] private bool useBounds = false;
        [SerializeField] private Bounds cameraBounds = new Bounds(Vector3.zero, Vector3.one * 20);

        [Header("预测移动")]
        [SerializeField] private bool enablePrediction = false;
        [SerializeField] private float predictionTime = 0.5f;

        [Header("死区设置")]
        [SerializeField] private bool useDeadZone = false;
        [SerializeField] private Vector2 deadZoneSize = new Vector2(2f, 1f);

        // 私有变量
        private Vector3 velocity = Vector3.zero;
        private Vector3 lastTargetPosition;
        private Vector3 targetVelocity;
        private UnityEngine.Camera cam;

        private void Awake()
        {
            cam = GetComponent<UnityEngine.Camera>();
        }

        private void Start()
        {
            if (target != null)
            {
                lastTargetPosition = target.position;
            }
        }

        private void LateUpdate()
        {
            if (target == null) return;

            UpdateTargetVelocity();
            Vector3 desiredPosition = CalculateDesiredPosition();

            if (useDeadZone && IsInDeadZone(desiredPosition))
            {
                return; // 在死区内不移动相机
            }

            MoveCamera(desiredPosition);
        }

        /// <summary>
        /// 更新目标速度
        /// </summary>
        private void UpdateTargetVelocity()
        {
            targetVelocity = (target.position - lastTargetPosition) / Time.deltaTime;
            lastTargetPosition = target.position;
        }

        /// <summary>
        /// 计算期望位置
        /// </summary>
        private Vector3 CalculateDesiredPosition()
        {
            Vector3 targetPosition = target.position;

            // 添加预测移动
            if (enablePrediction)
            {
                targetPosition += targetVelocity * predictionTime;
            }

            Vector3 desiredPosition = targetPosition + offset;

            // 应用边界限制
            if (useBounds)
            {
                desiredPosition = ApplyBounds(desiredPosition);
            }

            return desiredPosition;
        }

        /// <summary>
        /// 移动相机
        /// </summary>
        private void MoveCamera(Vector3 desiredPosition)
        {
            if (useSmoothDamping)
            {
                transform.position = Vector3.SmoothDamp(
                    transform.position,
                    desiredPosition,
                    ref velocity,
                    smoothTime,
                    maxSpeed);
            }
            else
            {
                transform.position = Vector3.Lerp(
                    transform.position,
                    desiredPosition,
                    followSpeed * Time.deltaTime);
            }
        }

        /// <summary>
        /// 应用边界限制
        /// </summary>
        private Vector3 ApplyBounds(Vector3 position)
        {
            if (cam == null) return position;

            // 计算相机视野边界
            float cameraHeight = cam.orthographicSize;
            float cameraWidth = cameraHeight * cam.aspect;

            // 限制位置
            position.x = Mathf.Clamp(position.x,
                cameraBounds.min.x + cameraWidth,
                cameraBounds.max.x - cameraWidth);

            position.y = Mathf.Clamp(position.y,
                cameraBounds.min.y + cameraHeight,
                cameraBounds.max.y - cameraHeight);

            return position;
        }

        /// <summary>
        /// 检查是否在死区内
        /// </summary>
        private bool IsInDeadZone(Vector3 desiredPosition)
        {
            Vector3 currentPosition = transform.position;
            Vector3 difference = desiredPosition - currentPosition;

            return Mathf.Abs(difference.x) < deadZoneSize.x * 0.5f &&
                   Mathf.Abs(difference.y) < deadZoneSize.y * 0.5f;
        }

        /// <summary>
        /// 设置跟随目标
        /// </summary>
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
            if (target != null)
            {
                lastTargetPosition = target.position;
            }
        }

        /// <summary>
        /// 立即移动到目标位置
        /// </summary>
        public void SnapToTarget()
        {
            if (target == null) return;

            Vector3 desiredPosition = target.position + offset;

            if (useBounds)
            {
                desiredPosition = ApplyBounds(desiredPosition);
            }

            transform.position = desiredPosition;
            velocity = Vector3.zero;
        }

        /// <summary>
        /// 设置相机边界
        /// </summary>
        public void SetBounds(Bounds bounds)
        {
            cameraBounds = bounds;
            useBounds = true;
        }

        /// <summary>
        /// 禁用边界限制
        /// </summary>
        public void DisableBounds()
        {
            useBounds = false;
        }

        /// <summary>
        /// 设置跟随速度
        /// </summary>
        public void SetFollowSpeed(float speed)
        {
            followSpeed = speed;
        }

        /// <summary>
        /// 设置平滑时间
        /// </summary>
        public void SetSmoothTime(float time)
        {
            smoothTime = time;
        }

        /// <summary>
        /// 获取当前目标
        /// </summary>
        public Transform GetTarget()
        {
            return target;
        }

        /// <summary>
        /// 震动效果
        /// </summary>
        public void Shake(float intensity, float duration)
        {
            StartCoroutine(ShakeCoroutine(intensity, duration));
        }

        private System.Collections.IEnumerator ShakeCoroutine(float intensity, float duration)
        {
            Vector3 originalPosition = transform.position;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                float x = Random.Range(-1f, 1f) * intensity;
                float y = Random.Range(-1f, 1f) * intensity;

                transform.position = originalPosition + new Vector3(x, y, 0);

                elapsed += Time.deltaTime;
                yield return null;
            }

            transform.position = originalPosition;
        }

        private void OnDrawGizmosSelected()
        {
            // 绘制相机边界
            if (useBounds)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(cameraBounds.center, cameraBounds.size);
            }

            // 绘制死区
            if (useDeadZone)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(transform.position, new Vector3(deadZoneSize.x, deadZoneSize.y, 0));
            }

            // 绘制到目标的连线
            if (target != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(transform.position, target.position);
            }
        }
    }
}
