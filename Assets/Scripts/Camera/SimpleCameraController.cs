using UnityEngine;

namespace GooseDuckKill.CameraSystem
{
    /// <summary>
    /// 简化版相机控制器
    /// 提供基础的动态效果，适合快速设置
    /// </summary>
    public class SimpleCameraController : MonoBehaviour
    {
        [Header("基础设置")]
        [SerializeField] private float orthographicSize = 5f;
        [SerializeField] private Color backgroundColor = new Color(0.1f, 0.1f, 0.15f, 1f);

        [Header("浮动效果")]
        [SerializeField] private bool enableFloating = true;
        [SerializeField] private float floatAmplitude = 0.05f;
        [SerializeField] private float floatSpeed = 1.5f;

        [Header("呼吸效果")]
        [SerializeField] private bool enableBreathing = false;
        [SerializeField] private float breathingAmplitude = 0.2f;
        [SerializeField] private float breathingSpeed = 0.8f;

        private UnityEngine.Camera cam;
        private Vector3 originalPosition;
        private float originalSize;
        private float time;

        private void Awake()
        {
            cam = GetComponent<UnityEngine.Camera>();
            if (cam == null)
                cam = UnityEngine.Camera.main;
        }

        private void Start()
        {
            SetupCamera();
        }

        private void Update()
        {
            time += Time.deltaTime;

            if (enableFloating)
                UpdateFloating();

            if (enableBreathing)
                UpdateBreathing();
        }

        private void SetupCamera()
        {
            if (cam == null) return;

            // 保存原始值
            originalPosition = transform.position;
            originalSize = orthographicSize;

            // 设置相机
            cam.orthographic = true;
            cam.orthographicSize = orthographicSize;
            cam.backgroundColor = backgroundColor;
            cam.clearFlags = CameraClearFlags.SolidColor;

            // 确保有音频监听器
            if (GetComponent<AudioListener>() == null)
                gameObject.AddComponent<AudioListener>();
        }

        private void UpdateFloating()
        {
            float yOffset = Mathf.Sin(time * floatSpeed) * floatAmplitude;
            transform.position = originalPosition + Vector3.up * yOffset;
        }

        private void UpdateBreathing()
        {
            if (cam == null) return;

            float sizeOffset = Mathf.Sin(time * breathingSpeed) * breathingAmplitude;
            cam.orthographicSize = originalSize + sizeOffset;
        }

        /// <summary>
        /// 启用/禁用浮动效果
        /// </summary>
        public void ToggleFloating(bool enable)
        {
            enableFloating = enable;
            if (!enable)
                transform.position = originalPosition;
        }

        /// <summary>
        /// 启用/禁用呼吸效果
        /// </summary>
        public void ToggleBreathing(bool enable)
        {
            enableBreathing = enable;
            if (!enable && cam != null)
                cam.orthographicSize = originalSize;
        }

        /// <summary>
        /// 重置相机
        /// </summary>
        public void ResetCamera()
        {
            transform.position = originalPosition;
            if (cam != null)
                cam.orthographicSize = originalSize;
            time = 0f;
        }
    }
}
