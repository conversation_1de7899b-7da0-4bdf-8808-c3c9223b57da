// 临时测试文件，用于验证编译错误是否已修复
using UnityEngine;
using CustomNetworking.Debug;
using CustomNetworking.ErrorHandling;
using CustomNetworking.Core;

public class CompilationTest
{
    public void TestMethod()
    {
        // 测试Debug.Log调用
        UnityEngine.Debug.Log("测试日志");
        UnityEngine.Debug.LogError("测试错误");
        UnityEngine.Debug.LogWarning("测试警告");
        
        // 测试NetworkDiagnostics使用
        var diagnostics = new CustomNetworking.ErrorHandling.NetworkDiagnostics
        {
            State = ConnectionState.Connected,
            Quality = NetworkQuality.Good,
            Latency = 50f,
            PacketLoss = 0.01f,
            BandwidthUsage = 25f,
            Stability = 0.95f
        };
        
        Debug.Log("编译测试通过");
    }
}
